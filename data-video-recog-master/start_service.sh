#!/bin/bash

echo "检查Python环境..."

# 检查python3是否可用
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "错误: 未找到Python，请先安装Python"
    exit 1
fi

echo "使用Python命令: $PYTHON_CMD"

# 检查是否有虚拟环境
if [ -d ".venv" ]; then
    echo "激活虚拟环境..."
    source .venv/bin/activate
fi

# 安装依赖
echo "安装依赖..."
$PYTHON_CMD -m pip install fastapi uvicorn python-multipart

# 启动服务
echo "启动视频审核服务..."
echo "服务将在 http://localhost:8080 启动"

# 根据项目结构，使用现有的启动方式
if [ -f "server/kafka_process.py" ]; then
    echo "启动Kafka处理服务..."
    $PYTHON_CMD server/kafka_process.py
else
    echo "创建简单的HTTP服务..."
    # 创建一个简单的FastAPI服务
    cat > temp_server.py << 'EOF'
from fastapi import FastAPI, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import uvicorn

app = FastAPI(title="视频审核服务")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "视频审核服务已启动"}

@app.get("/health")
async def health():
    return {"status": "ok"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    return {
        "filename": file.filename,
        "content_type": file.content_type,
        "size": file.size,
        "status": "uploaded"
    }

if __name__ == "__main__":
    print("启动服务在 http://localhost:8080")
    uvicorn.run(app, host="0.0.0.0", port=8080)
EOF
    
    $PYTHON_CMD temp_server.py
fi