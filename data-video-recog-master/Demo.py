import cv2
from algorithm.video_Ai_auditor import VideoAiAuditor
import json
from config import get_config


def main():
    configs = get_config()
    img_sim_checker = VideoAiAuditor(configs["model_config"])

    mock_input = {
    "srvLogTraceId": "请求UUID",
    "reqStartTime": 1675003131,
    "reserved_info": {"给业务中台留的预留字段": "内容"},
    "data":{
        "data_info": {
            "id": "video_id",
            "url": "http://10.139.1.220:8087/%E4%B8%B4%E6%97%B6%E6%B5%8B%E8%AF%95/%E7%9F%AD%E8%A7%86%E9%A2%91/0aac764b-5697-40b3-98c9-b2b750b226d3.mp4",
            # "file_path": "/Users/<USER>/workspace/数据/短视频示例/1f4d1b07-22ed-4a25-b83a-c56b7ce01ea4.mp4",
            "file_path": "/Users/<USER>/Downloads/f39edcdf-8e9f-44b2-ad7f-0b97804e5c4e.mp4",
            "data_type": 1
            # "file_path": "/Users/<USER>/workspace/codes/XMagnet视频审核/投放视频新闻播报场景识别代码及模型_240424/测试用例/audit_hit_frames_477a1db3610d986f2cd9a9b2ee2e6b7f_172127_1248.jpg",
            # "data_type": 2

        }},
    "optional_param": {
            "time_interval": 3.0,
            "begin_margin": 1.0
        }
    }

    res = img_sim_checker.process(mock_input)
    a = json.dumps(res, ensure_ascii=False).encode("utf-8")
    print('over')


def test():

    import imageio
    file_path = "/Users/<USER>/Downloads/16-9002-avi-20220225090039.avi"
    data_obj = imageio.get_reader(file_path, 'ffmpeg')

    # data_url = "https://test-open.oss-cn-hzfinance.aliyuncs.com/x-magnet/16-9002-avi-20220225090039.avi"
    # video_obj = imageio.get_reader(data_url, 'ffmpeg')

    print(1)


import pickle
def read_pickle(path):
    with open(path, 'rb') as file:
        res = pickle.load(file)
    return res


def 开发风险模型():

    path = "/Users/<USER>/workspace/codes/XMagnet视频审核/保险服务/data-video-recog-保险-开发区/开发入参.pkl"
    req, asr_list, res = read_pickle(path)

    configs = get_config()
    img_sim_checker = VideoAiAuditor(configs["model_config"])
    res = img_sim_checker.process_risk_evaluation(req, asr_list, res)


    print(1)


if __name__ == '__main__':
    # main()


    # test()

    开发风险模型()
