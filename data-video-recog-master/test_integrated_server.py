#!/usr/bin/env python3
import requests
import json

def test_integrated_server():
    """测试司内集成版服务器"""
    base_url = "http://localhost:8080"
    
    print("🏢 测试司内集成版视频审核服务")
    print("=" * 60)
    
    # 1. 测试服务健康状态
    print("\n🏥 测试1: 服务健康检查")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务状态: {result['status']}")
            print(f"📊 服务详情:")
            for service, available in result['services'].items():
                status = "✅ 可用" if available else "❌ 不可用"
                print(f"  - {service}: {status}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 2. 测试服务状态详情
    print("\n📋 测试2: 服务状态详情")
    try:
        response = requests.get(f"{base_url}/services/status")
        if response.status_code == 200:
            result = response.json()
            print("📊 司内服务状态:")
            for service, info in result.items():
                status = "✅ 可用" if info['available'] else "❌ 不可用"
                print(f"  - {service}: {status}")
                print(f"    描述: {info['description']}")
        else:
            print(f"❌ 服务状态查询失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 服务状态查询异常: {e}")
    
    # 3. 测试视频文件上传（众安贷视频）
    print("\n🎬 测试3: 上传众安贷视频文件")
    try:
        # 创建一个模拟的众安贷视频文件
        test_content = b"fake video content for testing"
        files = {"file": ("众安贷视频.mp4", test_content, "video/mp4")}
        
        response = requests.post(f"{base_url}/upload", files=files)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 上传成功")
            print(f"🔒 追踪ID: {result['trace_id']}")
            print(f"📊 风险等级: {result['audit_result']['risk_evaluation']}")
            print(f"🔍 分析方法: {result['audit_result']['analysis_method']}")
            print(f"📝 OCR结果: {result['audit_result']['ocr_result']}")
            print(f"⚠️  风险因素: {result['audit_result'].get('risk_factors', [])}")
            print(f"🛠️  使用的服务: {result['audit_result'].get('services_used', [])}")
            
            return result['trace_id']
        else:
            print(f"❌ 上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None
    
    # 4. 测试图片文件上传
    print("\n🖼️  测试4: 上传银行宣传图片")
    try:
        with open("test_files/银行宣传图.png", "rb") as f:
            files = {"file": ("银行宣传图.png", f, "image/png")}
            response = requests.post(f"{base_url}/upload", files=files)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 上传成功")
                print(f"📊 风险等级: {result['audit_result']['risk_evaluation']}")
                print(f"📝 OCR结果: {result['audit_result']['ocr_result']}")
                print(f"⚠️  风险因素: {result['audit_result'].get('risk_factors', [])}")
            else:
                print(f"❌ 上传失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 上传异常: {e}")

def test_json_response_format():
    """测试JSON响应格式"""
    print("\n📄 测试5: JSON响应格式验证")
    try:
        # 上传理财产品说明书
        with open("test_files/理财产品说明书.txt", "rb") as f:
            files = {"file": ("理财产品说明书.txt", f, "text/plain")}
            response = requests.post("http://localhost:8080/upload", files=files)
            
            if response.status_code == 200:
                result = response.json()
                audit_result = result['audit_result']
                
                print("✅ JSON格式验证:")
                print(f"  - ocr_result: {type(audit_result.get('ocr_result', []))}")
                print(f"  - face_detection: {type(audit_result.get('face_detection', []))}")
                print(f"  - asr_result: {type(audit_result.get('asr_result', []))}")
                print(f"  - qr_code_result: {type(audit_result.get('qr_code_result', []))}")
                print(f"  - risk_evaluation: {audit_result.get('risk_evaluation')}")
                print(f"  - compliance_check: {audit_result.get('compliance_check')}")
                print(f"  - analysis_method: {audit_result.get('analysis_method')}")
                print(f"  - services_used: {audit_result.get('services_used', [])}")
                print(f"  - risk_factors: {len(audit_result.get('risk_factors', []))} 个风险因素")
                
                # 显示完整的JSON结构（用于Demo页面展示）
                print("\n📊 完整JSON结构预览:")
                json_preview = {
                    "filename": result['filename'],
                    "trace_id": result['trace_id'],
                    "analysis_result": audit_result
                }
                print(json.dumps(json_preview, ensure_ascii=False, indent=2)[:500] + "...")
                
            else:
                print(f"❌ 测试失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_records():
    """测试记录查询"""
    print("\n📋 测试6: 查询所有审核记录")
    try:
        response = requests.get("http://localhost:8080/records")
        if response.status_code == 200:
            result = response.json()
            records = result['records']
            print(f"✅ 查询成功，共 {len(records)} 条记录")
            
            # 显示最近的记录
            if records:
                latest = records[-1]
                print(f"📄 最新记录:")
                print(f"  - 文件名: {latest['filename']}")
                print(f"  - 风险等级: {latest['audit_result']['risk_evaluation']}")
                print(f"  - 分析方法: {latest['audit_result']['analysis_method']}")
                print(f"  - 上传时间: {latest['upload_time']}")
        else:
            print(f"❌ 查询失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 查询异常: {e}")

def main():
    print("🧪 司内集成版视频审核服务 - 功能测试")
    print("=" * 70)
    
    # 执行所有测试
    test_integrated_server()
    test_json_response_format()
    test_records()
    
    print("\n" + "=" * 70)
    print("🎉 测试完成！")
    print("\n📝 测试总结:")
    print("  ✅ 服务启动正常，基础分析功能可用")
    print("  ⚠️  司内AI服务因依赖缺失暂时不可用")
    print("  ✅ 风险评估基于司内业务规则正常工作")
    print("  ✅ JSON格式符合Demo页面展示要求")
    print("\n🌐 现在可以使用Demo页面测试: http://localhost:8080/demo")
    print("📊 查看服务状态: http://localhost:8080/services/status")
    print("=" * 70)

if __name__ == "__main__":
    main()
