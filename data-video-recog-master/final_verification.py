#!/usr/bin/env python3
"""
最终验证脚本 - 验证真实视频内容分析功能
"""
import requests
import json

def final_test():
    """最终验证测试"""
    print("🎉 最终验证 - 真实视频内容分析")
    print("=" * 60)
    
    # 测试1: 验证OCR修复
    print("\n📝 测试1: OCR功能验证")
    try:
        with open("test_frame.png", "rb") as f:
            files = {"file": ("test_frame.png", f, "image/png")}
            response = requests.post("http://localhost:8080/upload", files=files)
            
            if response.status_code == 200:
                result = response.json()
                audit_result = result['audit_result']
                
                print("✅ OCR功能正常!")
                print(f"📝 识别文字: {audit_result['ocr_result']}")
                print(f"📊 风险等级: {audit_result['risk_evaluation']}")
                
                # 检查是否识别到"众安贷"
                ocr_text = " ".join(audit_result['ocr_result'])
                if "众安贷" in ocr_text:
                    print("✅ 成功识别中文关键词!")
                else:
                    print("⚠️  未识别到中文关键词")
                    
            else:
                print(f"❌ OCR测试失败: {response.status_code}")
    except Exception as e:
        print(f"❌ OCR测试异常: {e}")
    
    # 测试2: 验证真实图片分析
    print("\n🖼️  测试2: 真实图片分析")
    try:
        with open("test_files/银行宣传图.png", "rb") as f:
            files = {"file": ("银行宣传图.png", f, "image/png")}
            response = requests.post("http://localhost:8080/upload", files=files)
            
            if response.status_code == 200:
                result = response.json()
                audit_result = result['audit_result']
                
                print("✅ 图片分析完成!")
                print(f"📝 OCR结果数量: {len(audit_result['ocr_result'])}")
                print(f"👤 人脸检测: {len(audit_result['face_detection'])}个")
                print(f"📊 二维码: {len(audit_result['qr_code_result'])}个")
                print(f"⚠️  风险等级: {audit_result['risk_evaluation']}")
                
                if audit_result['ocr_result'] and audit_result['ocr_result'][0] != "图片中未检测到文字内容":
                    print("✅ 真实OCR识别成功!")
                    print(f"📝 识别内容: {audit_result['ocr_result'][:3]}...")
                else:
                    print("⚠️  OCR未识别到内容")
                    
            else:
                print(f"❌ 图片分析失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 图片分析异常: {e}")
    
    # 测试3: JSON格式验证
    print("\n📄 测试3: JSON格式完整性验证")
    try:
        response = requests.get("http://localhost:8080/records")
        if response.status_code == 200:
            records = response.json()['records']
            if records:
                latest = records[-1]
                audit_result = latest['audit_result']
                
                print("✅ JSON格式验证:")
                required_fields = [
                    'ocr_result', 'face_detection', 'asr_result', 
                    'qr_code_result', 'risk_evaluation', 'compliance_check',
                    'analysis_method', 'audit_time'
                ]
                
                for field in required_fields:
                    if field in audit_result:
                        print(f"  ✅ {field}: {type(audit_result[field])}")
                    else:
                        print(f"  ❌ {field}: 缺失")
                
                # 检查新增字段
                enhanced_fields = ['risk_score', 'risk_factors', 'file_info']
                for field in enhanced_fields:
                    if field in audit_result:
                        print(f"  🆕 {field}: {type(audit_result[field])}")
                
                print(f"\n📊 完整JSON示例:")
                sample_json = {
                    "filename": latest['filename'],
                    "analysis_result": {
                        "ocr_result": audit_result['ocr_result'][:2],
                        "face_detection": len(audit_result['face_detection']),
                        "risk_evaluation": audit_result['risk_evaluation'],
                        "analysis_method": audit_result['analysis_method']
                    }
                }
                print(json.dumps(sample_json, ensure_ascii=False, indent=2))
                
        else:
            print(f"❌ 记录查询失败: {response.status_code}")
    except Exception as e:
        print(f"❌ JSON验证异常: {e}")

def show_comparison():
    """显示对比结果"""
    print("\n" + "=" * 60)
    print("📊 对比结果总结")
    print("=" * 60)
    
    print("\n❌ 之前的问题:")
    print("  - 返回模拟数据，没有真实内容分析")
    print("  - OCR结果: ['视频文件已接收: 众安贷视频.mp4']")
    print("  - 人脸检测: 模拟数据")
    print("  - 风险评估: 基于文件名，不基于内容")
    
    print("\n✅ 现在的解决方案:")
    print("  - 真实视频帧提取和OCR识别")
    print("  - 真实人脸检测和二维码识别")
    print("  - 基于实际内容的风险评估")
    print("  - 详细的帧级别分析数据")
    print("  - 完整的JSON格式输出")
    
    print("\n🎯 技术实现:")
    print("  - MoviePy: 真实视频帧提取")
    print("  - RapidOCR: 中英文OCR识别")
    print("  - OpenCV: 人脸和二维码检测")
    print("  - 司内业务规则: 风险评估算法")

if __name__ == "__main__":
    final_test()
    show_comparison()
    
    print("\n" + "=" * 60)
    print("🎉 验证完成!")
    print("\n🌐 现在可以使用Demo页面测试真实的视频分析:")
    print("  1. 访问: http://localhost:8080/demo")
    print("  2. 上传您的MP4视频文件")
    print("  3. 查看真实的OCR识别结果")
    print("  4. 获得基于实际内容的风险评估")
    print("=" * 60)
