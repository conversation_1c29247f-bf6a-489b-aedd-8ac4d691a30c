#!/usr/bin/env python3
import requests
import json

def test_upload():
    url = "http://localhost:8080/upload"
    
    # 测试文件1：普通文件
    print("测试1: 上传普通文件")
    with open("test_files/test_image.txt", "rb") as f:
        files = {"file": ("test_image.txt", f, "text/plain")}
        response = requests.post(url, files=files)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        print("-" * 50)
    
    # 测试文件2：包含金融关键词的文件
    print("测试2: 上传包含金融关键词的文件")
    with open("test_files/贷款产品介绍.txt", "rb") as f:
        files = {"file": ("贷款产品介绍.txt", f, "text/plain")}
        response = requests.post(url, files=files)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        print("-" * 50)

def test_health():
    print("测试健康检查")
    response = requests.get("http://localhost:8080/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    print("-" * 50)

def test_records():
    print("测试获取所有记录")
    response = requests.get("http://localhost:8080/records")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    print("-" * 50)

if __name__ == "__main__":
    test_health()
    test_upload()
    test_records()
