import os
import yaml

CONFIG = None
app_name = "data-video-recog"
config_path = "config.yml"  # 最外层config文件位置，也是Cube上配置中心推送的config位置


def read_yaml(config_path):
    file = open(config_path, "r", encoding="utf-8")
    ys = yaml.load(file.read(), Loader=yaml.Loader)
    return ys


def get_config():
    base = os.path.abspath(os.path.dirname(__file__))
    global CONFIG
    if CONFIG:
        return CONFIG

    root_config = read_yaml(config_path)
    local_model_path_config = read_yaml(root_config["local_model_config_path"])
    local_model_path_config.update(root_config["model_config"])

    CONFIG = {
        "app_name": app_name,
        "default_log_dir": os.path.join(base, "logs"),
        "model_config": local_model_path_config,
        "kafka": root_config["kafka"],
        "mysql": root_config["mysql"]
    }
    return CONFIG
