#!/usr/bin/env python
"""
@File:      zbar
@Author:    sunjie
@Time:      2024/12/2 18:20
Describe:   
"""
from pyzbar.pyzbar import decode


def acquire_qr_info_by_pyzbar(input):
    dec_res = decode(input) if input is not None else []  # 是一个list

    qrcode_infos = []
    for i in range(len(dec_res)):
        qrcode_info = dec_res[i].data.decode('utf-8')
        qrcode_infos.append(qrcode_info)

    return qrcode_infos