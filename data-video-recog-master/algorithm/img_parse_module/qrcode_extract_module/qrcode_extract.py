"""
@file:    qrcode_extract.py
@brief:   二维码内容解析
@author:  she<PERSON><PERSON>
@time:    2023-03-01
"""

import cv2
from algorithm.utils.try_except import *
from .pyzbar_tool import acquire_qr_info_by_pyzbar


def extract_qrcode_info(wechat_qrcode_parser, pred, img0):
    res = {}
    barcode = None
    max_conf = 0
    for j in range(len(pred)):
        cur_conf = pred[j][4]
        if cur_conf > max_conf:   # 只取置信度最大的1个
            roi = pred[j]
            padding = 30
            h, w = img0.shape[:2]
            left = max(0, int(roi[0] - padding))
            top = max(0, int(roi[1] - padding))
            right = min(w, int(roi[2] + padding))
            bottom = min(h, int(roi[3] + padding))

            barcode = img0[top: bottom, left: right]
            barcode = cv2.resize(barcode, (500, 500))

            max_conf = cur_conf
    
    # 先调用opencv
    if barcode is None:
        res["result"] = ""
    else:
        qrcode_infos, points = wechat_qrcode_parser.detectAndDecode(barcode)

        # 如果没有结果 调用pyzbar
        if len(qrcode_infos) == 0:
            qrcode_infos = acquire_qr_info_by_pyzbar(barcode)

        if len(qrcode_infos) == 1:
            qrcode_info = qrcode_infos[0]
        else:
            qrcode_info = ""

        res["result"] = qrcode_info

    barcode = cv2.resize(barcode, (256, 256)) if barcode is not None else None
    res["roi"] = barcode

    return res


class QRCodeExtract(object):
    def __init__(self, model_indicator, wechat_qrcode_parser):
        self.qr_code_detector = model_indicator
        self.wechat_qrcode_parser = wechat_qrcode_parser 

    # 方向校正入口
    @error_handler("QRCodeExtract", item_return=False)
    def get_qrcode_info(self, img):
        # 1 检测二维码
        qrcode_detect_res = self.qr_code_detector.process(img)
        
        # 2 取二维码解析内容
        res = extract_qrcode_info(self.wechat_qrcode_parser, qrcode_detect_res, img)

        return res
