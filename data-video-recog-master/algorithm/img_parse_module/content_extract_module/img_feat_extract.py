from algorithm.utils.data_type import DataType, FaceType
from algorithm.img_parse_module.content_extract_module.face_recog_module import Face<PERSON><PERSON>ognize
import copy
import cv2
import numpy as np
from algorithm.utils.try_except import error_handler


class ContentExtract:
    def __init__(self, face_detector, face_emb_encoder, paddle_ocr, face_library, news_scene_classifier, qrcode_extractor):
        """
        入参模型：全图特征提取moco、文档前景分割模型、背景补全模型、背景特征提取simclr
        """
        # 传入模型类
        self.face_detector = face_detector
        self.face_emb_encoder = face_emb_encoder
        self.paddle_ocr = paddle_ocr
        self.news_scene_classifier = news_scene_classifier
        self.qrcode_extractor = qrcode_extractor

        # 导入人脸处理模块
        self.face_processer = FaceRecognize(face_detector, face_emb_encoder, face_library)

        self.feamp_data_template = {
            "info": {
                "time_stamp": None,
                "frame_stamp": None
            },
            "face": [],
            "text": [],
            "label": {"news": None,
                      "QR_code": None}
        }

        self.ocr_content_template = {"words": None,
                                     "location": None,
                                     "score": None}

        self.label_dict_template = {"news": False,
                                    "QR_code": False}

    @error_handler("ContentExtract", num_return=2, item_return=[])
    def get_key_frame_idx(self, data_info, param):
        # step1 计算时间序号
        time_stamp_list = list(np.arange(param.begin_margin,
                                     int(data_info.duration - param.end_margin),
                                     param.time_interval))

        # step1.5 补首帧
        time_stamp_list.insert(0, 0)

        # step2 补最后末尾的指定帧
        last_time_tamp = round(data_info.duration - param.end_margin)      # 目前允许最后一帧是小数
        time_stamp_list.append(last_time_tamp)

        # step3 计算对应的帧序号
        fps = data_info.fps
        frame_stamp_list = [int(t*fps) for t in time_stamp_list]

        return time_stamp_list, frame_stamp_list

    @error_handler("ContentExtract", num_return=1, item_return=[])
    def get_ocr_result(self, image):
        result, elapse = self.paddle_ocr(image)
        image_shape = image.shape
        result_list = []
        if result is not None:
            # 表示检测到文字。没检测到的话result=None
            # 将检测到的文字转录为存储格式
            for content in result:
                polygon, text, score = content
                # polygon = np.array(polygon)
                # box = [polygon[:, 0].min(),
                #        polygon[:, 1].min(),
                #        polygon[:, 0].max(),
                #        polygon[:, 1].max()]

                # 20250122 过滤掉约定情况的
                # 1. 过滤掉图片中上方带有“免息”二字的文字切片。
                if "免息" in text:
                    if polygon[2][1]/image_shape[0] < 0.11:
                        continue

                new_content_dict = copy.deepcopy(self.ocr_content_template)
                new_content_dict["words"] = text
                new_content_dict["location"] = polygon
                new_content_dict["score"] = float(score)
                result_list.append(new_content_dict)

        return result_list

    @error_handler("ContentExtract", num_return=1, item_return=[])
    def get_face_result(self, this_image):
        face_res_list = self.face_processer.process(this_image)
        return face_res_list

    @error_handler("ContentExtract", num_return=1, item_return=False)
    def get_scene_flag(self, this_image, face_result_list):
        # 目前仅检查是否新闻画面，输出为布尔量
        label_res = copy.deepcopy(self.label_dict_template)

        # step1 筛选素人数量。1-2个素人才判断新闻
        general_face_list = [face for face in face_result_list if face["face_type"] == FaceType.General]
        if len(general_face_list) in [1, 2]:

            # step2 场景分类模型。目前仅新闻，返回 0：其他，1：新闻
            news_flag = self.news_scene_classifier.predict(this_image, with_score=False)
            if news_flag == 1:
                label_res["news"] = True

        # step3 二维码检查，查找可解析的二维码
        qr_res = self.qrcode_extractor.get_qrcode_info(this_image)
        if qr_res["roi"] is not None:
            label_res["QR_code"] = True

        return label_res

    def postprocess_label(self, all_frame_res_list):
        # 2-1 对新闻场景修正，如不满足"连续2帧阳性"条件，则整个视频都不报出。
        news_list = [x["label"]["news"] for x in all_frame_res_list]
        news_flag = [news_list[i] for i in range(len(news_list) - 1) if news_list[i] and news_list[i + 1]]
        if len(news_flag) == 0:
            # 不存在两张连续的新闻判定，就认为是误判，全部帧都置为False
            for frame in all_frame_res_list:
                frame["label"]["news"] = False
        return all_frame_res_list

    @error_handler("ContentExtract", num_return=1, item_return=[])
    def process_selected_frame(self, video_obj, time_stamp_list, frame_stamp_list):
        all_frame_res_list = []

        # step1 逐帧进行提取
        for time_stamp, frame_stamp in zip(time_stamp_list, frame_stamp_list):
            new_frame_dict = copy.deepcopy(self.feamp_data_template)
            new_frame_dict["info"]["time_stamp"] = int(time_stamp)
            new_frame_dict["info"]["frame_stamp"] = int(frame_stamp)

            # 抽帧图片
            # this_image = video_obj.get_data(frame_stamp)
            # this_image = cv2.cvtColor(this_image, cv2.COLOR_RGB2BGR)
            video_obj.set(cv2.CAP_PROP_POS_FRAMES, frame_stamp)
            ret, this_image = video_obj.read()

            # step1 人脸检测
            face_result_list = self.get_face_result(this_image)
            new_frame_dict["face"] = face_result_list

            # step2 ocr
            ocr_result_list = self.get_ocr_result(this_image)
            new_frame_dict["text"] = ocr_result_list

            # step3 场景分类（目前仅区分是否疑似新闻）
            label_res = self.get_scene_flag(this_image, face_result_list)
            new_frame_dict["label"] = label_res

            all_frame_res_list.append(new_frame_dict)

        # step2 做视频级别的修正
        all_frame_res_list = self.postprocess_label(all_frame_res_list)

        return all_frame_res_list


    # @error_handler("ImgFeatureExtract", num_return=6)
    def content_process_pipeline_video(self, video_obj, data_info, param):
        # step1 切分关键帧
        time_stamp_list, frame_stamp_list = self.get_key_frame_idx(data_info, param)

        # step2 逐帧抽图，做检测，并创建帧结果
        all_frame_res_list = self.process_selected_frame(video_obj, time_stamp_list, frame_stamp_list)

        return all_frame_res_list


    def content_process_pipeline_image(self, image_np, data_info, param):
        # 对图片检测，创建1帧结果
        new_frame_dict = copy.deepcopy(self.feamp_data_template)
        new_frame_dict["info"]["time_stamp"] = 1
        new_frame_dict["info"]["frame_stamp"] = 1

        # step1 人脸检测
        face_result_list = self.get_face_result(image_np)
        new_frame_dict["face"] = face_result_list

        # step2 ocr
        ocr_result_list = self.get_ocr_result(image_np)
        new_frame_dict["text"] = ocr_result_list

        # step3 场景分类（目前仅区分是否疑似新闻）
        label_dict = self.get_scene_flag(image_np, face_result_list)
        new_frame_dict["label"] = label_dict

        all_frame_res_list = [new_frame_dict]
        return all_frame_res_list


    def go_service_entrance(self, case_struct_info):
        # step1 获取所需材料
        data_obj = case_struct_info.data.data
        data_info = case_struct_info.data.info
        data_type = case_struct_info.data.info.data_type
        param = case_struct_info.optional_param

        # step2 进入内容提取流程
        frame_list = []
        if data_type == DataType.VIDEO.value:
            frame_list = self.content_process_pipeline_video(data_obj, data_info, param)
        else:
            frame_list = self.content_process_pipeline_image(data_obj, data_info, param)

        # step3 结果录入数据流结构体中
        case_struct_info.update_img_feat_result(frame_list)

        return case_struct_info
