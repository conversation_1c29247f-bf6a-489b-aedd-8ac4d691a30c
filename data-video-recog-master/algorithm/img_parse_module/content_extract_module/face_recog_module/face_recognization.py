import numpy as np


class FaceRecognize:
    def __init__(self, face_detector, face_emb_encoder, face_library):
        self.face_detector = face_detector
        self.face_emb_encoder = face_emb_encoder
        self.face_library = face_library

    def get_feature(self, image):
        feature, padding_face = self.face_emb_encoder(image)
        feature = feature/np.linalg.norm(feature, axis=1)
        return feature, padding_face

    def process(self, image):
        # 备注，hanjia版本，原图BGR(cv2)进入人脸检测，输出小脸是RGB(Image)，进入特征提取。
        # step1 人脸检测
        face_list, face_box_list = self.face_detector(image)

        # step2 人脸特征提取
        feature_list = []
        for face_res in face_list:
            face, conf = face_res
            feature, padding_face = self.get_feature(face)
            feature_list.append(feature)

        # step3 对比是否是库中代言人脸
        face_ReID_res = []
        for id, face_feature in enumerate(feature_list):
            res_dict = self.face_library.match_face_library(face_feature)
            x1, y1, x2, y2 = face_box_list[id]
            res_dict["location"] = [[x1, y1], [x2, y1], [x2, y2], [x1, y2]]
            face_ReID_res.append(res_dict)

        return face_ReID_res
