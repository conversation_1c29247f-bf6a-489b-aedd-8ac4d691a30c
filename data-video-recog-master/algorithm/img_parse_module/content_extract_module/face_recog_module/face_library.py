import glob, os
import numpy as np
from algorithm.utils.data_type import FaceTypeMap, FaceType


class FaceLibrary:
    def __init__(self, lib_path, threshold):
        self.threshold = threshold
        file_list = glob.glob(lib_path + '/*.npy')

        self.target_face_list = []
        for file_path in file_list:
            face = np.load(file_path, allow_pickle=True)  ## 加载
            base_name = os.path.basename(file_path).split('.')[0]
            cls = base_name.split('-')[0]

            face_template = {
                "class": FaceTypeMap[int(cls)],
                "name": base_name,
                "feature": face
            }
            self.target_face_list.append(face_template)

    def match_face_library(self, face_np):
        # 输入一个人脸特征，遍历历史库中的人脸。
        res = {"face_type": FaceType.General,
               "name": None}    # 默认没匹配上，就是素人
        for face_dict in self.target_face_list:
            face_lib = face_dict["feature"]
            l1 = np.linalg.norm(face_lib - face_np, axis=1)

            if l1 < self.threshold:
                res = {"face_type": face_dict["class"],
                       "name": face_dict["name"]}
                break

        return res
