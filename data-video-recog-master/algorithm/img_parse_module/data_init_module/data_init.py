from algorithm.utils.data_func import *
from algorithm.utils.data_type import DataType
from algorithm.utils.try_except import error_handler
from algorithm.utils import try_except
import imageio, cv2
import copy


class DataInitModule:
    # 将输入字典结构对应录入结构体
    def __init__(self, cfg):
        self.default_param = cfg["default_param"]

    @error_handler("DataInitModule")
    def record_parm(self, case_struct_info, input):
        # 可选参数录入
        optional_param = copy.deepcopy(self.default_param)

        # 如果没有该输入，则全部使用默认，如有则更新
        new_param = input.get("optional_param")
        if new_param is not None:
            optional_param.update(new_param)

        # 更新到案件结构体中
        for k, v in optional_param.items():
            setattr(case_struct_info.optional_param, k, v)
        return case_struct_info

    @error_handler("DataInitModule")
    def record_case_data(self, case_struct_info, input):
        srvLogTraceId = input["srvLogTraceId"]
        case_struct_info.srvLogTraceId = srvLogTraceId
        try_except.build_context(srvLogTraceId)

        # Step1 录入案件信息
        case_info = input["data"]["data_info"]
        for key in ["id", "url", "data_type"]:
            setattr(case_struct_info.data.info, key, case_info[key])

        # Step2 读取视频对象或图片对象
        data_type = input["data"]["data_info"]["data_type"]
        file_path = input["data"]["data_info"].get("file_path")  # 本地调用有这个
        data_obj = input["data"]["data_info"].get("data_obj")   # 服务调用有这个

        if data_type == DataType.VIDEO.value:   # 视频请求
            if data_obj is None:        # 本地测试调用
                # data_obj = imageio.get_reader(file_path, 'ffmpeg')
                data_obj = cv2.VideoCapture(file_path)
                case_struct_info.data.data = data_obj
            else:       # 服务网络调用
                case_struct_info.data.data = data_obj
            # meta_data = data_obj.get_meta_data()
            # case_struct_info.data.info.duration = meta_data['duration']
            # case_struct_info.data.info.fps = meta_data['fps']
            # case_struct_info.data.info.size = meta_data['size']

            # 用cv2读取视频基本信息
            frame_count = int(data_obj.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = data_obj.get(cv2.CAP_PROP_FPS)
            duration = frame_count / fps
            case_struct_info.data.info.duration = duration
            case_struct_info.data.info.fps = fps
            case_struct_info.data.info.size = (int(data_obj.get(cv2.CAP_PROP_FRAME_WIDTH)),
                                               int(data_obj.get(cv2.CAP_PROP_FRAME_HEIGHT)))
        else:           # 图片请求
            if data_obj is None:  # 本地测试调用
                data_obj = cv2.imread(file_path)
            else:       # 服务网络调用
                case_struct_info.data.data = data_obj
            case_struct_info.data.data = data_obj
            case_struct_info.data.info.size = data_obj.shape[1::-1]  # (W, H)

        return case_struct_info


    def go_service_entrance(self, input):
        case_struct_info = BaseCaseStructInfo()

        # Step1 录入可选参数
        case_struct_info = self.record_parm(case_struct_info, input)

        # Step2 录入报案信息
        new_case = self.record_case_data(case_struct_info, input)
        if new_case is not None:
            case_struct_info = new_case

        return case_struct_info
