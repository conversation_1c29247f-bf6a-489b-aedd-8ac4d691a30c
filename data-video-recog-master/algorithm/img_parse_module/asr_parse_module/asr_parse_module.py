#!/usr/bin/env python
"""
@File:      asr_parse
@Author:    sunjie
@Time:      2024/8/7 16:57
Describe:   
"""

from algorithm.utils.try_except import error_handler
from algorithm.utils import try_except
from moviepy.editor import VideoFileClip


class AsrParse:
    def __init__(self, asr_net):
        self.asr_net = asr_net

    @staticmethod
    def mp4_to_wav(video_path, save_wav_path='output.wav'):
        video = VideoFileClip(video_path)
        audio = video.audio
        audio.write_audiofile(save_wav_path)

    def get_voice_file(self, video_path):
        tmp_path = video_path + ".wav"
        self.mp4_to_wav(video_path, save_wav_path=tmp_path)
        return tmp_path

    def get_asr(self, wav_path='output.wav', video_id="video_id"):
        asr_res = self.asr_net.server_test(wav_path, video_id)
        if isinstance(asr_res, dict) and asr_res.get("success") == False:       # asr服务报错，也可能gpu OOM
            return [], False
        elif len(asr_res) == 0:     # 确实没有说话内容
            return [], True
        sentence_info = asr_res[0]["sentence_info"]
        return sentence_info, True

    def remove_word_timestamp(self, sentence_info_list):
        # 剔除每个字的详细时间，没必要使用
        for x in sentence_info_list:
            del x["timestamp"]
        return sentence_info_list

    # @error_handler("AsrParse", num_return=1, item_return=[])
    def process(self, input_dict):
        # step0 排除图片请求
        data_type = input_dict["data"]["data_info"]["data_type"]
        if int(data_type) != 1:
            return [], True

        # step1 取出音轨
        video_id = input_dict["data"]["data_info"]["id"]
        video_path = input_dict["data"]["data_info"]["file_path"]
        voice_path = self.get_voice_file(video_path)

        # step2 请求asr
        sentence_info_list, request_flag = self.get_asr(wav_path=voice_path, video_id=video_id)

        # step3 剔除每个字的详细时间，没必要使用
        sentence_info_list = self.remove_word_timestamp(sentence_info_list)

        return sentence_info_list, request_flag


    def go_service_entrance(self, input_dict):
        """
        注意，该方法暂不启用，ASR环节的报错不在内部处理，直接抛到 asr_loop.py 留一个log即可。只有ocr环节报错才区分错误码，返回kafka信息。
        """
        # 初始化线程内案件
        try_except.build_context(input_dict["srvLogTraceId"])

        # 算法处理
        sentence_info_list = self.process(input_dict)

        # 错误码处理
        error_code = 0
        error_message = None
        error_detail = None

        # 检查线程案件号一致
        OCR_CASE_CONTEXT_ID = try_except.get_context_id()
        id = input_dict["srvLogTraceId"]
        # assert OCR_CASE_CONTEXT_ID == id      # todo 测试用

        # 取出案件错误信息
        error_code_list, error_msg_list, err_detail_list = try_except.get_error_event()
        if len(error_code_list) > 0:
            error_code = error_code_list[0]
            error_message = error_msg_list[0]
            error_detail = err_detail_list[0]

        # 对服务层输出
        output = {"errcode": error_code,
                  "message": error_message,
                  "detail": error_detail,
                  "result": sentence_info_list}

        return output
