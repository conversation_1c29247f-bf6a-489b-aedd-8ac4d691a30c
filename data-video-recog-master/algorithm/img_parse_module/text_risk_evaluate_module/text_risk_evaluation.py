#!/usr/bin/env python
"""
@File:      text_risk_evaluation
@Author:    sunjie
@Time:      2024/11/19 15:14
Describe:   
"""
from algorithm.utils.data_type import DataType
from algorithm.utils.try_except import error_handler
from algorithm.utils import try_except

class TextRiskEvaluation:
    def __init__(self, text_classification):
        self.text_classification = text_classification

    @error_handler("TextRiskEvaluation", item_return=[], record_code=False)
    def preprocess_ocr(self, frame_list):
        all_texts = [text_line["words"]  for frame in frame_list for text_line in frame["text"] ]
        text_list = list(set(all_texts))
        return text_list

    @error_handler("TextRiskEvaluation", item_return=[], record_code=False)
    def preprocess_asr(self, asr_list):
        asr_list = [line["text"] for line in asr_list]
        return asr_list

    @error_handler("DataInitModule", num_return=2, item_return=None, record_code=False)
    def risk_predict(self, ocr_text_list, asr_text_list):
        flag, prob = self.text_classification.predict(ocr_text_list, asr_text_list)
        return flag, prob

    def process(self, req, asr_list, img_algorithm_res):
        try_except.build_context(req["srvLogTraceId"])  # 记录本次请求的uuid

        # step0 图片跳过
        if req["data"]["data_info"]["data_type"] == DataType.IMAGE.value:
            res_dict = {"flag": False, "score": 0.0}
            return res_dict

        # step1 预处理ocr文本
        ocr_text_list = self.preprocess_ocr(img_algorithm_res["result"]["frame_list"])

        # step2 预处理asr文本
        asr_text_list = self.preprocess_asr(asr_list)

        # step3 风险模型预测
        flag, prob = self.risk_predict(ocr_text_list, asr_text_list)
        output_dict = {"value": flag, "score": prob}

        return output_dict
