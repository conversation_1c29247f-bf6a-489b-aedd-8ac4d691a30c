import copy
from algorithm.utils.data_type import *
from algorithm.utils.data_func import *
from algorithm.utils.try_except import error_handler


class ApiMapping:
    def __init__(self):
        self.result_template = {"data_info": {"id": None,
                                               "duration": None,
                                               "frames": None,
                                               "fps": None,
                                               "size": None
                                               },
                                "frame_list": []}

    @error_handler("ApiMapping", num_return=1)
    def transform(self, case_struct_info):
        result_dict = copy.deepcopy(self.result_template)

        # step1 录入基本信息
        result_dict["data_info"]["id"] = case_struct_info.data.info.id
        result_dict["data_info"]["duration"] = case_struct_info.data.info.duration
        result_dict["data_info"]["frames"] = case_struct_info.data.info.frames
        result_dict["data_info"]["fps"] = case_struct_info.data.info.fps
        result_dict["data_info"]["size"] = case_struct_info.data.info.size

        # step2 录入帧信息
        frame_list = case_struct_info.data.frame_list
        for frame in frame_list:
            for face in frame["face"]:
                face["face_type"] = face["face_type"].value
        result_dict["frame_list"] = frame_list
        return result_dict


    def go_service_entrance(self, case_struct_info):
        # API映射
        result_dict = self.transform(case_struct_info)

        # 错误码处理
        error_code = 0
        error_message = None
        error_detail = None

        # 检查线程案件号一致
        OCR_CASE_CONTEXT_ID = try_except.get_context_id()
        id = case_struct_info.srvLogTraceId
        assert OCR_CASE_CONTEXT_ID == id      # todo 测试用

        error_code_list, error_msg_list, err_detail_list = try_except.get_error_event()

        if len(error_code_list) > 0:
            error_code = error_code_list[0]
            error_message = error_msg_list[0]
            error_detail = err_detail_list[0]

            if error_code == 30100:
                error_code = 30000      # 该错误过于严重，导致输出无效，直接返回整体失败

        # 对服务层输出
        output = {"errcode": error_code,
                  "message": error_message,
                  "detail": error_detail,
                  "result": result_dict}

        return output
