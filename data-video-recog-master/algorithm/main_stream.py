#!/usr/bin/env python
"""
@file:       main_stream.py
@brief:      project walk flow
@author:     sunjie
@time:       2023-03-02
"""

from config import get_config
from server.zalog import get_app_logger
from .utils import try_except
from algorithm.img_parse_module.video_frame_extract_module import FrameExtraction
from algorithm.img_parse_module.asr_parse_module.asr_parse_module import Asr<PERSON>arse
from algorithm.img_parse_module.data_init_module.data_init import DataInitModule
from algorithm.img_parse_module.content_extract_module import ContentExtract
from algorithm.img_parse_module.API_output.API_output import ApiMapping
from algorithm.img_parse_module.text_risk_evaluate_module.text_risk_evaluation import TextRiskEvaluation
from algorithm.img_parse_module.qrcode_extract_module.qrcode_extract import QRCodeExtract

db_handler = None

class MainStreamControl(object):
    # 主要流程初始化
    def __init__(self, model_manager):
        # 输入数据记录
        self.data_init_parse = DataInitModule(model_manager.default_parse_cfg)

        # 视频抽帧模块
        self.frame_extraction = FrameExtraction(model_manager.default_parse_cfg)

        # 二维码处理模块
        self.qrcode_extractor = QRCodeExtract(model_manager.qrcode_detector, model_manager.wechat_qrcode_parser)

        # 图片分析模块
        self.content_extractor = ContentExtract(model_manager.face_detector,
                                                model_manager.face_emb_encoder,
                                                model_manager.paddle_ocr,
                                                model_manager.face_library,
                                                model_manager.news_classifier,
                                                self.qrcode_extractor)

        # 视频asr模块
        self.asr_tools = AsrParse(model_manager.ASR_model)

        # 视频文字风险评估模块
        self.text_risk_evaluator = TextRiskEvaluation(model_manager.risk_evaluator)

        # 案件结果汇总模块
        self.API_output = ApiMapping()

        # logger初始化
        configs = get_config()
        self.app_logger = get_app_logger(configs)


    def process_init(self, input_dict):
        try_except.build_context(input_dict["srvLogTraceId"])  # 记录本次请求的uuid
        self.app_logger.info("Start processing the request {}".format(input_dict["srvLogTraceId"]))  # log记录请求

        case_struct_info = self.data_init_parse.go_service_entrance(input_dict)
        return case_struct_info


    def procoess(self,  input_dict):
        # Step1 案件请求参数初始化，新建案子级别数据传输结构体
        case_struct_info = self.process_init(input_dict)

        # Step2 内容提取
        case_struct_info = self.content_extractor.go_service_entrance(case_struct_info)

        # Step3 API映射。返回：1错误码。2查重结果。3落库信息。
        output = self.API_output.go_service_entrance(case_struct_info)
        return output


    def process_asr(self, input_dict):
        # Step1 处理asr
        asr_result = self.asr_tools.process(input_dict)

        return asr_result

    def process_risk_evaluation(self, req, asr_list, img_algorithm_res):

        res = self.text_risk_evaluator.process(req, asr_list, img_algorithm_res)

        return res
