from .data_type import *
from algorithm.utils import try_except
import copy

from config import get_config
from server.zalog import get_app_logger, get_biz_logger

configs = get_config()
app_logger = get_app_logger(configs)


class BaseCaseStructInfo(BaseCaseStruct):
    # 案件级结构体
    def __init__(self):
        super(BaseCaseStructInfo, self).__init__()
        pass

    # 提取完特征后更新数据流
    def update_img_feat_result(self, frame_result_list):
        self.data.frame_list = frame_result_list
        return True
