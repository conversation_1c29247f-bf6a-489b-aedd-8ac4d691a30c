#!/usr/bin/env python
"""
@file:    try_except.py
@brief:   try except module
@author:  shenh<PERSON>
@time:    2021-01-20
"""
import functools
import traceback
import threading

from config import get_config
from server.zalog import get_app_logger



# 映射错误码
MAP_ALGORITHM_CODE = {
    "DataInitModule": {"record_parm": 30100,
                       "record_case_data": 30101},
    "ContentExtract": {"get_key_frame_idx": 30200,
                       "get_ocr_result": 30201,
                       "get_face_result": 30202,
                       "process_selected_frame": 30203,
                       "get_scene_flag": 30204},
    "QRCodeExtract": {"get_qrcode_info": 30300},
    "AsrParse": {"process": 30300},
    "ApiMapping": {"transform": 30400}
}

# 创建logger
configs = get_config()
app_logger = get_app_logger(configs)


class ThreadLocalData:
    def __init__(self):
        self.thread_local_var = threading.local()

    def init_thread_local_var(self, value):
        self.thread_local_var.context_id = value
        self.thread_local_var.error_code = []
        self.thread_local_var.error_msg = []
        self.thread_local_var.error_detail = []

    def get_context_id(self):
        return getattr(self.thread_local_var, 'context_id', None)

    def add_error_event(self, error_code, msg, detail):
        self.thread_local_var.error_code.append(error_code)
        self.thread_local_var.error_msg.append(msg)
        self.thread_local_var.error_detail.append(detail)

    def get_error_event(self):
        error_list = [getattr(self.thread_local_var, k) for k in ["error_code", "error_msg", "error_detail"]]
        return error_list


thread_local_data = ThreadLocalData()


def build_context(context_id):
    thread_local_data.init_thread_local_var(context_id)

def get_context_id():
    return thread_local_data.get_context_id()

def get_error_event():
    return thread_local_data.get_error_event()

def add_error_event(error_code, msg, detail):
    return thread_local_data.add_error_event(error_code, msg, detail)


def error_handler(mudule_name, num_return=1, item_return=None, record_code=True):
    def error_handler(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # app_logger.info("start processing : {} mudule {} function. request id: {}".format(mudule_name, func.__name__, OCR_CASE_CONTEXT_ID))
                return func(*args, **kwargs)
            except Exception:
                OCR_CASE_CONTEXT_ID = get_context_id()
                error_msg = "Error occured in : {} mudule {} function. request id: {}".format(mudule_name, func.__name__,
                                                                                        OCR_CASE_CONTEXT_ID)
                print(error_msg)
                app_logger.info(error_msg)
                err_detail = traceback.format_exc()    # 具体报错
                app_logger.info("traceback infos: {}".format(error_msg))

                # 错误码记录
                if record_code:
                    error_code = MAP_ALGORITHM_CODE[mudule_name][func.__name__]
                    add_error_event(error_code, error_msg, err_detail)
                return [item_return] * num_return if num_return > 1 else item_return

        return wrapper

    return error_handler
