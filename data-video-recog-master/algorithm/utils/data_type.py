import enum
from typing_extensions import TypedDict
from typing import Any, Optional
import numpy as np



class DataType(enum.Enum):
    # 对应用户号的对比情况
    VIDEO = 1
    IMAGE = 2

class FaceType(enum.Enum):
    # 人脸ID
    General = 0
    ZhangGuoLi = 1
    HuangLei = 2

# FaceTypeMap = {
#     0: FaceType.General,
#     1: FaceType.ZhangGuoLi,
#     2: FaceType.NewStar}

FaceTypeMap = {member.value: member for member in FaceType}

# 每一帧的数据结构
class FrameData(object):
    def __init__(self):
        self.time_stamp = None
        self.frame_stamp = None
        self.image = None
        self.face_list = []
        self.text_list = []


# 案件数据结构
class CaseInfo(object):
    def __init__(self):
        self.id = None
        self.url = None
        self.file_path = None
        self.data_type = None
        self.duration = None
        self.frames = None
        self.fps = None
        self.size = None


# 案件数据结构
class CaseData(object):
    def __init__(self):
        self.info = CaseInfo()
        self.data = None    # 存放视频或图片对象
        self.frame_list = []


# 可选参数结构
class OptinalParam(object):
    def __init__(self):
        self.time_interval = None,
        self.begin_margin = None
        self.end_margin = None


# 案件结构
class BaseCaseStruct(object):
    def __init__(self):
        self.srvLogTraceId = None
        self.data = CaseData()
        self.optional_param = OptinalParam()
