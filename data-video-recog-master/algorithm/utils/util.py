import cv2
import requests
import numpy as np
from PIL import Image
from io import BytesIO


def npImage(img):
    img = np.fromstring(img, np.uint8)
    img = cv2.imdecode(img, cv2.IMREAD_COLOR)
    return img

def read_img_PIL(image):
    bytes_io = bytearray(image)
    img = np.array(Image.open(BytesIO(bytes_io)))
    if img is None:
        return None
    img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    return img

def get_img_from_url(url):
    # 尝试从url拿到图片，返回np形式
    try:
        res = requests.get(url, timeout=10)
        image = res.content
        np_image = npImage(image)
        return True, np_image
    except:
        return False, None


def get_legal_shape_image(image):
    # 对极大尺寸图做一次防御，如果长边大于5000，就把长边等比例压缩到3000。忽略该resize操作对后续特征提取对影响。
    long_size = max(image.shape)
    if long_size < 5000:
        return image
    else:
        scale_ratio = 3000/long_size
        new_image = cv2.resize(image, dsize=None, fx=scale_ratio, fy=scale_ratio, interpolation=cv2.INTER_AREA)
        return new_image


def download_url(url, save_path):
    try:
        res = requests.get(url, timeout=10, verify=False)
        with open(save_path, "wb") as f:
            f.write(res.content)
        return True
    except:
        return False
