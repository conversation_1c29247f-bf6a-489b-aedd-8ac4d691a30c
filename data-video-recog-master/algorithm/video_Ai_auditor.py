
from algorithm import main_stream
from algorithm.utils import parse_model_config
from algorithm.basic_model_module import face_detection, face_emb_encoder
from algorithm.basic_model_module.risk_evaluation_module.risk_evaluation import TextClassifier
from algorithm.img_parse_module.content_extract_module.face_recog_module.face_library import FaceLibrary
from rapidocr_onnxruntime import RapidOCR   # 1.3.7
from algorithm.basic_model_module.scene_classification.regnetx import SceneClassification
from algorithm.basic_model_module.asr_module.asr import FunASR
from algorithm.basic_model_module.qr_code_detect import QRcodeDetect
import cv2


class ModelInit(object):
    def __init__(self, model_configs):
        # model_configs = parse_model_config.do_parse_model_config(model_config_path)

        self.default_parse_cfg = parse_model_config.do_parse_model_config(model_configs["ocr_init_cfg_path"])

        # 人脸检测模型-RetinaFace
        self.face_detector = face_detection.FaceDetect(model_configs["face_detect_openvino_model_xml_path"],
                                                       model_configs["face_detect_openvino_model_bin_path"],
                                                       model_configs["face_detect_model_config_path"])

        # 人脸特征提取器-FaceNet
        self.face_emb_encoder = face_emb_encoder.FaceRec(model_configs["face_emb_openvino_model_xml_path"],
                                                         model_configs["face_emb_openvino_model_bin_path"],
                                                         model_configs["face_emb_model_config_path"])

        # 文字ocr模型-paddle
        self.paddle_ocr = RapidOCR()

        # 人脸特征库管理
        self.face_library = FaceLibrary(model_configs["face_lib_path"],
                                        self.default_parse_cfg["face_recognize"]["face_feature_threshold"])

        # 新闻场景分类模型
        self.news_classifier = SceneClassification(model_configs["news_classify_model_xml_path"])

        # ASR模型-API调用
        self.ASR_model = FunASR(model_configs["FunASR"])

        # 风险评估模型-@WangJiDi
        self.risk_evaluator = TextClassifier(model_configs["TextRiskClassification"])

        # 二维码检测器
        self.qrcode_detector = QRcodeDetect(model_configs["QR_code_detect_config"]["qr_code_detect_model_path"])

        # 微信二维码解析
        self.wechat_qrcode_parser = cv2.wechat_qrcode_WeChatQRCode(
            model_configs["QR_3rdparty_config"]["opencv_3rdparty-wechat_qrcode_model_detect_xml_path"],
            model_configs["QR_3rdparty_config"]["opencv_3rdparty-wechat_qrcode_model_detect_bin_path"],
            model_configs["QR_3rdparty_config"]["opencv_3rdparty-wechat_qrcode_model_sr_xml_path"],
            model_configs["QR_3rdparty_config"]["opencv_3rdparty-wechat_qrcode_model_sr_bin_path"])


class VideoAiAuditor(object):
    def __init__(self, model_configs):
        # 初始化模型
        self.model_manager = ModelInit(model_configs)

        # 流程初始化
        self.MainStreamController = main_stream.MainStreamControl(self.model_manager)

    def process(self, input_data):
        res = self.MainStreamController.procoess(input_data)
        return res

    def process_asr(self, input_data):
        res = self.MainStreamController.process_asr(input_data)
        return res

    def process_risk_evaluation(self, req, asr_list, img_algorithm_res):
        res = self.MainStreamController.process_risk_evaluation(req, asr_list, img_algorithm_res)
        return res
