#!/usr/bin/env python
"""
@File:      regnetx
@Author:    sunji<PERSON>, tanguozhu
@Time:      2024/4/25 10:29
Describe:   
"""


import time
import cv2
import numpy as np
from openvino.runtime import Core

means = (0.485, 0.456, 0.406)
stds = (0.229, 0.224, 0.225)


def reshape(ori_image, target_size):
    # 获取原图像宽高
    ori_size = ori_image.shape[0:2]

    # 根据原图比例缩放最长边至224
    ratio = min(float(target_size[i]) / (ori_size[i]) for i in range(len(ori_size)))
    new_size = tuple([int(i * ratio) for i in ori_size])

    # resize并padding至224*224
    img_resized = cv2.resize(ori_image, (new_size[1], new_size[0]), interpolation=3)
    pad_w = target_size[1] - new_size[1]
    pad_h = target_size[0] - new_size[0]
    top, bottom = pad_h // 2, pad_h - (pad_h // 2)
    left, right = pad_w // 2, pad_w - (pad_w // 2)
    img_resized = cv2.copyMakeBorder(img_resized, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(104, 116, 124))  # rgb

    return img_resized


def process(img):
    img_resized = reshape(img, (224, 224))
    img_resized = img_resized[:, :, [2, 1, 0]]

    img_np = img_resized / 255.0  # 64精度
    img_np = img_np.astype(np.float32)
    img_np -= np.array(means)
    img_np /= np.array(stds)

    img_np_chw = np.transpose(img_np, (2, 0, 1))
    img_np_chw = np.expand_dims(img_np_chw, axis=0)

    return img_np_chw


class SceneClassification:
    def __init__(self, model_xml_path):
        ie = Core()
        self.exec_net = ie.compile_model(model=model_xml_path, device_name="CPU")  # 载入模型

        self.out_blob = self.exec_net.outputs[0]

        self.mapping_dict = {0: 1,  # 新闻场景
                             1: 0  # 其他场景
                             }

    def predict(self, img, with_score=False):
        # 1) 图像预处理
        pil_img_numpy = process(img)

        # 2) openvino 推理
        start = time.time()
        net_res = self.exec_net([pil_img_numpy])
        cost_time = time.time() - start
        out = net_res[self.out_blob]
        res = np.argmax(out, axis=1).tolist()[0]
        res = self.mapping_dict[res]        # 为了类别好看添加的映射。注意下面置信度别取错

        if with_score:
            prob = np.exp(out) / np.sum(np.exp(out), axis=1)
            confidence = np.max(prob, axis=1).tolist()[0]  # 单batchsize
            return res, confidence, cost_time
        else:
            return res
