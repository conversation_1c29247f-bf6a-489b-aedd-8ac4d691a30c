
"""
@file:       qr_code_detect.py
@brief:      二维码检测模块
@author:     she<PERSON><PERSON> 重构
@from:       线上dev版本
@time:       2023-03-01
"""

import cv2
import numpy as np
import onnxruntime

def clip_coords(boxes, img_shape):
    # Clip bounding xyxy bounding boxes to image shape (height, width)
    np.clip(boxes[0], 0, img_shape[1])  # x1
    np.clip(boxes[1], 0, img_shape[0])  # y1
    np.clip(boxes[2], 0, img_shape[1])  # x2
    np.clip(boxes[3], 0, img_shape[0])  # y2

def scale_coords(img1_shape, coords, img0_shape, ratio_pad=None):
    if ratio_pad is None:  
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    coords[0] -= pad[0]  # x padding
    coords[2] -= pad[0]  # x padding
    coords[1] -= pad[1]  # y padding
    coords[3] -= pad[1]  # y padding
    coords[:4] /= gain

    clip_coords(coords, img0_shape)

    return coords

def zoom_coor(img, pred):
    if len(pred) == 0:
        return pred

    scale_pred = pred
    for j in range(len(pred)):
        single_scale_res = scale_coords(np.array([640, 640]), pred[j][:4], img.shape).round()
        scale_pred[j][:4] = single_scale_res
    
    return scale_pred

def letterbox(img, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True):
    # Resize image to a 32-pixel-multiple rectangle https://github.com/ultralytics/yolov3/issues/232
    shape = img.shape[:2]  # current shape [height, width]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)

    # Scale ratio (new / old)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    if not scaleup:  # only scale down, do not scale up (for better test mAP)
        r = min(r, 1.0)

    # Compute padding
    ratio = r, r  # width, height ratios
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
    if auto:  # minimum rectangle
        dw, dh = np.mod(dw, 32), np.mod(dh, 32)  # wh padding
    elif scaleFill:  # stretch
        dw, dh = 0.0, 0.0
        new_unpad = (new_shape[1], new_shape[0])
        ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

    dw /= 2  # divide padding into 2 sides
    dh /= 2

    if shape[::-1] != new_unpad:  # resize
        img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border

    return img, ratio, (dw, dh)

def image_preprocessing(image):
    img_size = 640
    img = letterbox(image, new_shape=img_size)[0] # Padded resize

    w, h, c = img.shape
    max_width = max(w, h)
    bottom = int((max_width - w) / 2)
    top = max_width - w - bottom
    right = int((max_width - h) / 2)
    left = max_width - h - right

    img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=0)       # 上下左右要填补的像素数量，数值为0（黑边）
    
    # Convert
    img = img.astype(np.float32, copy=False)
    img /= 255.0                              # 0 - 255 to 0.0 - 1.0
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
    img = np.ascontiguousarray(img)
    img = img[np.newaxis, :]

    return img

def xywh2xyxy(x):
    # Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right
    y = np.zeros_like(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2  # top left x
    y[:, 1] = x[:, 1] - x[:, 3] / 2  # top left y
    y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom right x
    y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom right y

    return y

def non_max_suppression(prediction, conf_thres=0.4, iou_thres=0.45):
    xc = prediction[..., 4] > conf_thres  # candidates

    x = prediction[xc]  # confidence
    # Compute conf
    x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf
    box = xywh2xyxy(x[:, :4])

    i, j = np.nonzero(x[:, 5:] > conf_thres)
    x = np.hstack((box[i], x[i, j + 5, None], j[:, None]))

    boxes, scores = x[:, :4], x[:, 4]
    x1 = boxes[:, 0]
    y1 = boxes[:, 1]
    x2 = boxes[:, 2]
    y2 = boxes[:, 3]
    scores = scores

    areas = (x2 - x1 + 1) * (y2 - y1 + 1)
    order = scores.argsort()[::-1]
    c_i = []
    while order.size > 0:
        i = order[0]
        c_i.append(i)
        xx1 = np.maximum(x1[i], x1[order[1:]])
        yy1 = np.maximum(y1[i], y1[order[1:]])
        xx2 = np.minimum(x2[i], x2[order[1:]])
        yy2 = np.minimum(y2[i], y2[order[1:]])

        w = np.maximum(0.0, xx2 - xx1 + 1)
        h = np.maximum(0.0, yy2 - yy1 + 1)
        inter = w * h
        ovr = inter / (areas[i] + areas[order[1:]] - inter)

        inds = np.where(ovr <= iou_thres)[0]
        order = order[inds + 1]

    boxes_c = x[c_i]
    boxes_c0 = boxes_c[boxes_c[:, 5] == 0]
    boxes_c1 = boxes_c[boxes_c[:, 5] == 1]

    return boxes_c0, boxes_c1


class QRcodeDetect(object):
    def __init__(self, model_path):
        self.ort_session = onnxruntime.InferenceSession(model_path)

    # 备注： 沈辉2023.3.7深度重构
    def process(self, input):
        # step1 图像预处理
        img = image_preprocessing(input)

        # step2 模型推理
        ort_inputs = {self.ort_session.get_inputs()[0].name: img}
        ort_outputs = self.ort_session.run(None, ort_inputs)
        pred = ort_outputs[0]

        # step3 后处理
        pred_0, pred_1 = non_max_suppression(pred, 0.2, 0.45)
      
        # step4 恢复到原图尺寸
        scale_pred = zoom_coor(input, pred_1)

        return scale_pred