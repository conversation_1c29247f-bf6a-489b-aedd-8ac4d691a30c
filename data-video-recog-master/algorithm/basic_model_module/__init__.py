import time

import requests
import json
import traceback

class CustomError(Exception):
    # 自定义的请求相关报错码
    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


class BaseOnlineModel:

    def is_service_health(self, health_url):
        try:
            response = requests.get(url=health_url)
            if response.status_code == 200:
                print("Remote model {} is ready.".format(self.__class__.__name__))
            else:
                print("Remote model {} isn't ready, status_code={}.".format(self.__class__.__name__, response.status_code))
        except:
            err_detail = traceback.format_exc()  # 具体报错
            raise CustomError("算法模型{}服务健康检查失败。detail={}".format(self.__class__.__name__, err_detail))


    def post_service(self, input_dict, service_url):
        try:
            response = requests.post(url=service_url, data=json.dumps(input_dict), timeout=120)

            if response.status_code == 200:
                result = json.loads(response.text)

                if result["success"]:
                    return result["result"]
                raise CustomError("算法模型{}服务请求成功，处理失败。".format(self.__class__.__name__))

            elif response.status_code > 500:
                print("服务不可用，默认等待。")
                time.sleep(30)
                raise CustomError("算法模型服务{}请求失败，默认等待，status_code={}。".format(self.__class__.__name__, response.status_code))
            else:
                raise CustomError("算法模型服务{}请求失败，status_code={}。".format(self.__class__.__name__, response.status_code))
        except:
            err_detail = traceback.format_exc()  # 具体报错
            raise CustomError("算法模型服务{}请求失败，无法发出post请求。detail={}".format(self.__class__.__name__, err_detail))
