import cv2
from math import ceil
from itertools import product
import numpy as np
from openvino.inference_engine import IECore
from algorithm.utils.parse_model_config import do_parse_model_config


class PriorBox(object):
    def __init__(self, image_size=None):
        super(PriorBox, self).__init__()
        self.min_sizes = [[16, 32], [64, 128], [256, 512]]
        self.steps = [8, 16, 32]
        self.clip = False
        self.image_size = image_size
        self.feature_maps = [[ceil(self.image_size[0] / step), ceil(self.image_size[1] / step)] for step in self.steps]
        self.name = "s"

    def forward(self):
        anchors = []
        for k, f in enumerate(self.feature_maps):
            min_sizes = self.min_sizes[k]
            for i, j in product(range(f[0]), range(f[1])):
                for min_size in min_sizes:
                    s_kx = min_size / self.image_size[1]
                    s_ky = min_size / self.image_size[0]
                    dense_cx = [x * self.steps[k] / self.image_size[1] for x in [j + 0.5]]
                    dense_cy = [y * self.steps[k] / self.image_size[0] for y in [i + 0.5]]
                    for cy, cx in product(dense_cy, dense_cx):
                        anchors += [cx, cy, s_kx, s_ky]
        anchors = np.array(anchors)
        # back to torch land
        output = anchors.reshape(-1, 4)
        if self.clip:
            output.clamp_(max=1, min=0)
        return output


class FaceDetect:
    def __init__(self, model_xml_path, model_bin_path, cfg_path):
        # 模型初始化
        ie = IECore()
        net = ie.read_network(model=model_xml_path, weights=model_bin_path)
        self.exec_net = ie.load_network(network=net, device_name="CPU")

        # 默认参数配置
        self.config = do_parse_model_config(cfg_path)
        self.m_input_size = self.config["m_input_size"]
        self.rgb_mean = self.config["rgb_mean"]
        self.iou_thresh = self.config["iou_thresh"]
        self.vis_thresh = self.config["vis_thresh"]

    def decode(self, loc, priors, variances):
        boxes = np.concatenate((
            priors[:, :2] + loc[:, :2] * variances[0] * priors[:, 2:],
            priors[:, 2:] * np.exp(loc[:, 2:] * variances[1])), axis=1)
        boxes[:, :2] -= boxes[:, 2:] / 2
        boxes[:, 2:] += boxes[:, :2]
        return boxes

    def decode_landm(self, pre, priors, variances):
        landms = np.concatenate((priors[:, :2] + pre[:, :2] * variances[0] * priors[:, 2:],
                                 priors[:, :2] + pre[:, 2:4] * variances[0] * priors[:, 2:],
                                 priors[:, :2] + pre[:, 4:6] * variances[0] * priors[:, 2:],
                                 priors[:, :2] + pre[:, 6:8] * variances[0] * priors[:, 2:],
                                 priors[:, :2] + pre[:, 8:10] * variances[0] * priors[:, 2:],
                                 ), axis=1)
        return landms

    def py_cpu_nms(self, dets, thresh):
        """Pure Python NMS baseline."""
        x1 = dets[:, 0]
        y1 = dets[:, 1]
        x2 = dets[:, 2]
        y2 = dets[:, 3]
        scores = dets[:, 4]
        areas = (x2 - x1 + 1) * (y2 - y1 + 1)
        order = scores.argsort()[::-1]
        keep = []
        while order.size > 0:
            i = order[0]
            keep.append(i)
            xx1 = np.maximum(x1[i], x1[order[1:]])
            yy1 = np.maximum(y1[i], y1[order[1:]])
            xx2 = np.minimum(x2[i], x2[order[1:]])
            yy2 = np.minimum(y2[i], y2[order[1:]])

            w = np.maximum(0.0, xx2 - xx1 + 1)
            h = np.maximum(0.0, yy2 - yy1 + 1)
            inter = w * h
            ovr = inter / (areas[i] + areas[order[1:]] - inter)
            inds = np.where(ovr <= thresh)[0]
            order = order[inds + 1]
        return keep

    def __call__(self, img_data):
        faces = []
        face_boxes = []
        img_data = cv2.cvtColor(img_data, cv2.COLOR_BGR2RGB)

        origin_img = img_data.copy()
        img_data = cv2.resize(img_data, (self.m_input_size, self.m_input_size))
        img_data = np.float32(img_data)
        im_h, im_w, _ = origin_img.shape
        img_data -= np.array(self.rgb_mean).reshape((1, 1, 3))
        img_data = img_data.transpose(2, 0, 1)
        img_data = np.expand_dims(img_data, axis=0)
        output = self.exec_net.infer({'image': img_data})

        loc, conf, landms = output['loc'], output['conf'], output['landms']
        priorbox = PriorBox(image_size=(self.m_input_size, self.m_input_size))
        priors = priorbox.forward()
        prior_data = priors
        boxes = self.decode(loc[0], prior_data, [0.1, 0.2])
        im_width_0 = float(im_w / self.m_input_size)
        im_height_0 = float(im_h / self.m_input_size)
        scale = [self.m_input_size, self.m_input_size, self.m_input_size, self.m_input_size]
        boxes = boxes * scale
        scores = conf[0][:, 1]
        landms = self.decode_landm(landms[0], prior_data, [0.1, 0.2])
        scale1 = [self.m_input_size for i in range(10)]
        landms = landms * scale1
        inds = np.where(scores > self.vis_thresh)[0]
        boxes = boxes[inds]
        landms = landms[inds]
        scores = scores[inds]
        order = scores.argsort()[::-1]
        boxes = boxes[order]
        landms = landms[order]
        scores = scores[order]
        dets = np.hstack((boxes, scores[:, np.newaxis])).astype(np.float32, copy=False)
        keep = self.py_cpu_nms(dets, self.iou_thresh)
        dets = dets[keep, :]
        landms = landms[keep]
        dets = np.concatenate((dets, landms), axis=1)
        for b in dets:
            if b[4] < self.vis_thresh:
                continue
            x = int(float(b[0] * im_width_0))
            y = int(float(b[1] * im_height_0))
            w = int(float(b[2] * im_width_0))
            h = int(float(b[3] * im_height_0))
            if x < 0:
                x = 0
            if y < 0:
                y = 0
            # new = origin_img[max(0,y-int(im_h/10)):h, x-int(im_w/10):w+int(im_w/10)]
            new = origin_img[max(0, y):h, x:w]
            box = [x, y, w, h]
            face_boxes.append(box)
            faces.append([new, b[4]])
        return faces, face_boxes
