import tempfile
import fasttext
import numpy as np
from sklearn.base import BaseEstimator, ClassifierMixin
import pandas as pd
from sklearn.metrics import roc_auc_score, accuracy_score, f1_score, classification_report, confusion_matrix
from lime.lime_text import LimeTextExplainer
import inspect
import jieba as jieba
import sys
import time


class Metrics(object):
    def __init__(self):
        self.metrics = {}
        self.cnt = {}

    def add_metrics(self, **kwargs):
        for k, v in kwargs.items():
            if isinstance(v, (tuple, list)) and len(v) == 2:
                v, cnt = v
            else:
                cnt = 1
            self.metrics.setdefault(k, [])
            self.metrics[k].append(v)
            self.cnt.setdefault(k, [])
            self.cnt[k].append(cnt)

    def get_result(self):
        output = {}
        for k, vs in self.metrics.items():
            if len(vs) == 0:
                output[k] = 0
            else:
                output[k] = np.sum(vs) / np.sum(self.cnt[k])

        for k in self.metrics:
            self.metrics[k] = []
            self.cnt[k] = []
        return output

    def formated_result(self):
        output = self.get_result()
        return "\t".join(["%s=%f" % (k, v) for k, v in output.items()])

    def size(self):
        values = list(self.metrics.values())
        if len(values) == 0:
            return 0
        else:
            return len(values[0])


class ProcessStatus(object):
    enabled = True

    def __init__(self, total_steps, task=None, update_steps=10):
        self.task = task
        self.total_steps = total_steps
        self.update_steps = update_steps
        self.global_step = 0
        self.metrics = Metrics()
        self.begin_time = time.time()
        if self.task is None:
            caller_frame = inspect.stack()[1]
            self.task = "%s.%s:%s" % (inspect.getmodule(caller_frame[0]).__name__,
                                      caller_frame.function, caller_frame.lineno)

    def update(self, **kwargs):
        self.metrics.add_metrics(**kwargs)

        self.global_step += 1
        if self.global_step % self.update_steps != 0:
            return

        percentage = 100 * self.global_step // self.total_steps
        seconds = int(time.time() - self.begin_time)
        m, s = divmod(seconds, 60)
        h, m = divmod(m, 60)

        if self.enabled:
            sys.stdout.write(
                "\r[%s]\t%d%%\ttime=%02d:%02d:%02d\tstep=%d\t%s" %
                (self.task, percentage,
                 h, m, s, self.global_step,
                 self.metrics.formated_result()))
            sys.stdout.flush()

    def finish(self):
        if self.enabled:
            sys.stdout.write("\n")


def _jieba_cut(text):
    return " ".join(jieba.lcut(text))


class JobRuner(object):
    _pool = None

    @classmethod
    def run(cls, func, inputs):
        caller_frame = inspect.stack()[1]
        name = "%s.%s:%s" % (inspect.getmodule(caller_frame[0]).__name__,
                             caller_frame.function, caller_frame.lineno)
        status = ProcessStatus(task=name, total_steps=len(inputs))

        outputs = []
        if cls._pool is not None:
            for output in cls._pool.imap_unordered(func, inputs):
                outputs.append(output)
                status.update()
        else:
            for output in map(func, inputs):
                outputs.append(output)
                status.update()
        status.finish()
        return outputs

    @classmethod
    def jieba_cut(cls, texts):
        return cls.run(_jieba_cut, texts)


class TextClassifier(BaseEstimator, ClassifierMixin):
    def __init__(self):
        super(TextClassifier, self).__init__()
        self.classes_ = None

    def fit(self, texts, labels):
        assert len(texts) == len(labels)
        return self

    def predict(self, texts) -> np.ndarray:
        pass

    def predict_proba(self, texts) -> np.ndarray:
        pass

    def rank_metrics(self, texts, labels):
        probs = self.predict_proba(texts)[:, 1]
        print("auc =", roc_auc_score(labels, probs))

        sort_indices = np.argsort(-probs)
        output = []
        for label_rate in np.arange(0.1, 1.1, 0.1):
            label_num = min(int(len(labels) * label_rate), len(labels) - 1)

            threshold = probs[sort_indices[label_num]]
            __preds = [1.0] * label_num
            __y_test = labels[sort_indices[:label_num]]

            recall_rate = sum(__y_test) / sum(labels)
            recall_num = sum(__y_test)

            output.append([threshold, label_rate, label_num, recall_rate, recall_num])
        return pd.DataFrame(output, columns=["threshold", "label_rate", "label_num", "recall_rate", "recall_num"])

    def classify_metrics(self, texts, labels):
        preds = self.predict(texts)
        print("f1 =", f1_score(labels, preds, average="macro"))
        print("acc =", accuracy_score(labels, preds))
        print(classification_report(labels, preds, digits=4))
        # noinspection PyBroadException
        try:
            return pd.DataFrame(confusion_matrix(labels, preds), columns=self.classes_, index=self.classes_)
        except:
            return pd.DataFrame(confusion_matrix(labels, preds))

    def explain(self, text, num_features=20, tokenizer=r'\W+'):
        explainer = LimeTextExplainer(class_names=self.classes_, split_expression=tokenizer)
        exp = explainer.explain_instance(text, self.predict_proba, num_features=num_features, top_labels=1)
        for w, s in sorted(exp.as_list(label=exp.available_labels()[0]), key=lambda x: -x[1]):
            print("%.4f\t%s" % (s, w))
        exp.show_in_notebook()
        return exp


class FasttextClassifier(TextClassifier):
    def __init__(self, num_epochs=100, tokenizer="jieba", **kwargs):
        super(FasttextClassifier, self).__init__()
        self.tokenizer = tokenizer
        self.kwargs = kwargs
        self.kwargs["epoch"] = num_epochs
        self.label_prefix = self.kwargs.get("label", "__label__")
        self.model = None
        self.classes_ = None
        self.class_type_ = None

    def fit(self, texts, labels):
        with tempfile.NamedTemporaryFile(mode="w") as fout:
            self._dump_file(texts, labels, fout)
            self.model = fasttext.train_supervised(fout.name, **self.kwargs)
            self.classes_ = sorted(set(labels))
            self.class_type_ = type(self.classes_[0])
        return self

    def _raw_predict(self, texts, k=1):
        texts = [t.replace("\n", "") for t in texts]
        texts = JobRuner.jieba_cut(texts)

        preds, probs = self.model.predict(texts, k=k)
        preds = [
            [self.class_type_(pred.replace(self.label_prefix, "")) for pred in preds_]
            for preds_ in preds
        ]
        return preds, probs

    def predict_proba(self, texts):
        preds, probs = self._raw_predict(texts, k=len(self.classes_))
        sorted_probs = []
        for preds_, probs_ in zip(preds, probs):
            pred2prob = dict(zip(preds_, probs_))
            probs_ = [pred2prob.get(c, 0.0) for c in self.classes_]
            sorted_probs.append(probs_)
        return np.asarray(sorted_probs)

    def _dump_file(self, texts, labels, fout):
        num_samples = len(texts)
        batch_size = 100000

        for offset in range(0, num_samples, batch_size):
            sub_texts = texts[offset: offset + batch_size]
            sub_labels = labels[offset: offset + batch_size]

            sub_texts = [t.replace("\n", "") for t in sub_texts]
            sub_texts = JobRuner.jieba_cut(sub_texts)
            for text, label in zip(sub_texts, sub_labels):
                fout.write("%s\t%s%s\n" % (text, self.label_prefix, label))



    def __getstate__(self):
        params = self.__dict__
        with tempfile.NamedTemporaryFile() as fout:
            self.model.save_model(fout.name)
            model = open(fout.name, 'rb').read()
        params["model"] = model
        return params

    def __setstate__(self, state):
        self.__dict__.update(state)
        with tempfile.NamedTemporaryFile() as fout:
            fout.write(self.model)
            self.model = fasttext.load_model(fout.name)
