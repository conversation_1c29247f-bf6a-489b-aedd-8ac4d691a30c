import json
import os
import pickle
import time
import threading
import traceback
from pathlib import Path
from shutil import rmtree
from typing import Dict, Any, Optional

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
import oss2
from pydantic import validator, Field, BaseModel

from zalearn.classification import *
from server.zalog import get_app_logger
from config import get_config
import pickle

configs = get_config()
app_logger = get_app_logger(configs)

class ModelConfig(BaseModel):
    name: str
    version: str
    model_dir: Optional[str] = None
    model_file_name: Optional[str] = None
    model_path: Optional[str] = None
    extra_config: Dict[str, Any] = Field(default=None)

    @validator('model_path', pre=True, always=True)
    def generate_model_path(cls, v, values):
        if v is not None:
            return v
        # 如果model_dir和model_file_name都提供了，则生成model_path
        if values.get('model_dir') and values.get('model_file_name'):
            return os.path.join(values['model_dir'], values['model_file_name'])
        raise ValueError("Either model_dir and model_file_name or model_path must be provided.")



class ModelManagerConfig(BaseModel):
    model_name: str
    version_spec: str
    model_unload_delay_seconds: int = Field(default=360)
    model_config: Optional[ModelConfig] = Field(default=None)



def download_oss_directory(bucket, oss_dir, local_dir):
    os.makedirs(local_dir, exist_ok=True)

    for obj in oss2.ObjectIterator(bucket, prefix=oss_dir):
        relative_path = obj.key[len(oss_dir) + 1:]
        local_file_path = os.path.join(local_dir, relative_path)
        os.makedirs(os.path.dirname(local_file_path), exist_ok=True)

        if not obj.key.endswith('/'):
            app_logger.info(f"Downloading {obj.key} to {local_file_path}")
            bucket.get_object_to_file(obj.key, local_file_path)
        else:
            app_logger.info(f"Skipping directory {obj.key}")



class Model:
    def __init__(self, name, version, model_path, **kwargs):
        self.name = name
        self.version = version
        self.status = 'pending'
        self.model_path = model_path
        self.score = None
        self.ready = False
        self.model = None
        self.init_extra_config(**kwargs)
        self.load()

    def init_extra_config(self, **kwargs):
        self.score = kwargs["threshold"]


    def load(self):
        app_logger.info(f"Loading model {self.name} v{self.version}")
        self.model = pickle.load(open(self.model_path, "rb"))  # 依赖都在zalearn
        self.status = 'ready'
        self.ready = True
        app_logger.info(f"Model {self.name} v{self.version} loaded and ready")

    def unload(self):
        app_logger.info(f"Unloading model {self.name} v{self.version}")
        self.status = 'unloading'
        self.ready = False
        self.model = None

        path = Path(self.model_path)
        absolute_path = path.resolve()
        if absolute_path.exists():
            app_logger.info(f"模型文件 {absolute_path} 存在，准备删除其父目录。")
            parent_dir = absolute_path.parent
            if parent_dir.exists() and parent_dir.is_dir():
                rmtree(parent_dir)
                app_logger.info(f"模型文件的父目录 {parent_dir} 已被删除。")
            else:
                app_logger.info(f"父目录 {parent_dir} 不存在或不是目录。")
        else:
            app_logger.info(f"模型文件 {absolute_path} 不存在。")
        self.status = 'unloaded'
        app_logger.info(f"Model {self.name} v{self.version} unloaded")

    def predict(self, ocr_lst, asr_lst):
        ocr = ",".join(ocr_lst)
        asr = ",".join(asr_lst)
        predict_input= f"ocr:{ocr} asr:{asr}"
        prob = [x[1] for x in self.model.predict_proba([predict_input])][0]

        if prob > self.score:
            return True, float(prob)  # 有风险应驳回
        else:
            return False, float(prob)  # 无风险应通过



class ModelManager:
    def __init__(self, config: ModelManagerConfig):
        self.current_model = None
        self.new_model = None
        self.old_model = None
        self.schedule_first_run = True
        self.hot_deploy_scheduler = None
        self.model_unload_delay_seconds = config.model_unload_delay_seconds
        self.init_model(config.model_config)
        self.__init_scheduler()

    def init_model(self, model_config: ModelConfig):
        self.current_model = Model(
            model_config.name,
            model_config.version,
            model_config.model_path,
            **model_config.extra_config
        )

    def __init_scheduler(self):
        self.hot_deploy_scheduler = BackgroundScheduler()
        # 10分钟执行一次更新，时间不要太短
        trigger = IntervalTrigger(seconds=600)
        self.hot_deploy_scheduler.add_job(self.update_model, trigger, id="update_model")
        self.hot_deploy_scheduler.start()


    def load_new_model(self, model_config: ModelConfig):
        self.new_model = Model(
            model_config.name,
            model_config.version,
            model_config.model_path,
            **model_config.extra_config
        )

    def switch_model(self):
        new_ready = self.new_model.ready if self.new_model else False
        if new_ready:
            self.old_model = self.current_model
            self.current_model = self.new_model
            self.new_model = None
            self.schedule_unload_old_model()

    def update_model(self):
        if self.schedule_first_run:
            self.schedule_first_run = False
            app_logger.info("跳过第一次执行")
            return
        try:
            # 拉最新配置，这里三行代码里先写死
            auth = oss2.Auth("LTAI5tFVcczJVGzMbCH3Kruk", "******************************")
            bucket = oss2.Bucket(auth, "https://oss-cn-hzfinance-internal.aliyuncs.com", "dsl-os")
            model_repo_oss_prefix = "text_risk_classify"

            model_manager_config_path = f"{model_repo_oss_prefix}/config.json"
            if not bucket.object_exists(model_manager_config_path):
                app_logger.info("cannot found modelmanager config, wait for next run")
                return
            result = bucket.get_object(model_manager_config_path)
            content = result.read().decode('utf-8')
            config_data = json.loads(content)
            model_manager_config = ModelManagerConfig(**config_data)
            self.model_unload_delay_seconds = model_manager_config.model_unload_delay_seconds
            if self.current_model.name == model_manager_config.model_name and self.current_model.version != model_manager_config.version_spec:
                # 下载新模型，生成新的model_config
                # 把text_risk_classify/modelname_modelversion下载到workingdir/downloads/modelname_modelversion
                new_model_dir = f"{model_manager_config.model_name}_{model_manager_config.version_spec}"
                new_model_oss_prefix = f"{model_repo_oss_prefix}/{new_model_dir}"
                local_model_prefix = f"{os.getcwd()}/downloads/{new_model_dir}"
                if bucket.object_exists(f"{new_model_oss_prefix}/config.json"):
                    download_oss_directory(bucket, new_model_oss_prefix, local_model_prefix)
                    with open(f"{local_model_prefix}/config.json") as jsonf:
                        model_config_json = json.load(jsonf)
                    model_config_json.update({
                        "name": model_manager_config.model_name,
                        "version": model_manager_config.version_spec,
                        "model_dir": local_model_prefix
                    })
                    model_config = ModelConfig(**model_config_json)
                    try:
                        self.load_new_model(model_config)
                    except Exception as e:
                        app_logger.info(f"load new model failed, {str(e)}")
                        self.new_model = None
                        return
                    self.switch_model()
                else:
                    app_logger.info("cannot found config file for new model, wait for next run")
            else:
                app_logger.info(f"no need to update model, current model {self.current_model.name} version: {self.current_model.version} is latest")
        except Exception as e:
            app_logger.info(f"update model failed, waiting for next turn, {str(e)}")
            traceback.print_exc()


    def schedule_unload_old_model(self):
        app_logger.info(f"Scheduling unload of old model in {self.model_unload_delay_seconds} seconds")
        threading.Timer(self.model_unload_delay_seconds, self.unload_old_model).start()

    def unload_old_model(self):
        if self.old_model:
            self.old_model.unload()
            self.old_model = None
            app_logger.info("Old model unloaded")

    def handle_request(self, *args, **kwargs):
        """使用新模型，如果新模型还没有准备好就使用current_model(应该指向旧模型)"""
        if self.new_model and self.new_model.ready:
            app_logger.info("当前风险预测模型版本" + self.new_model.name + self.new_model.version)
            return self.new_model.predict(*args, **kwargs)
        if self.current_model and self.current_model.ready:
            app_logger.info("当前风险预测模型版本" + self.current_model.name + self.current_model.version)
            return self.current_model.predict(*args, **kwargs)
        else:
            raise Exception("model not ready")



if __name__ == "__main__":

    def simulate_requests(manager, num_requests=10):
        import time
        for _ in range(num_requests):
            result = manager.handle_request(["每月只要1元", "所有进口药免赔", "百万医疗"],
                                            ["家人们，上福利", "大爷你怎么了"])
            print(f"预测结果: {result}")
            time.sleep(0.1)  # 模拟请求间隔
        while True:
            result = manager.handle_request(["每月只要1元", "所有进口药免赔", "百万医疗"],
                                            ["家人们，上福利", "大爷你怎么了"])
            print(f"预测结果: {result}")
            time.sleep(28)


    def print_current_model_info(manager):
        import time
        while True:
            if manager.current_model:
                print(f"current_model: {manager.current_model.version}, status: {manager.current_model.ready}")
            if manager.old_model:
                print(f"old_model: {manager.old_model.version}, status: {manager.old_model.ready}")
            else:
                print("old model not exist")
            if manager.new_model:
                print(f"new_model: {manager.new_model.version}, status: {manager.new_model.ready}")
            else:
                print("new model not exist")
            time.sleep(25)


    def main():
        model_manager_config_json = {
            "model_name": "TextRiskClassification",
            "version_spec": "20241229",
            "model_unload_delay_seconds": 120,
            "model_config": {
                "name": "TextRiskClassification",
                "version": "20241229",
                "model_path": f"{os.getcwd()}/downloads/text_risk_classification/ocrasr_4.m",
                "extra_config": {
                    "threshold": 0.6532
                }
            }
        }
        model_manager_config = ModelManagerConfig(**model_manager_config_json)
        manager = ModelManager(model_manager_config)
        time.sleep(5)

        request_thread = threading.Thread(target=simulate_requests, args=(manager,))
        request_thread.start()

        request_thread2 = threading.Thread(target=print_current_model_info, args=(manager,))
        request_thread2.start()

        time.sleep(1800)

        request_thread.join()


    main()