#!/usr/bin/env python
"""
@File:      risk_evaluation
@Author:    sunjie
@Time:      2024/11/19 14:42
Describe:   
"""

from algorithm.basic_model_module.risk_evaluation_module.syn_model_manager import Model<PERSON>anager, ModelManagerConfig


class TextClassifier(object):
    def __init__(self, model_config):
        model_manager_config = ModelManagerConfig(**model_config)
        self.risk_model_manager = ModelManager(model_manager_config)
        print("The TextClassifier for risk classification is ready.")

    def predict(self, ocr_lst, asr_lst):
        return self.risk_model_manager.handle_request(ocr_lst, asr_lst)