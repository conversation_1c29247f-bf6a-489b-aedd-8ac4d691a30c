### Part1 深度学习模型相关文件路径 ###

# 人脸检测模型(retina-face) @zhaohanjia
face_detect_openvino_model_xml_path: "./downloads/retinaface/retinaface.xml"
face_detect_openvino_model_bin_path: "./downloads/retinaface/retinaface.bin"
face_detect_model_config_path: "./algorithm/basic_model_module/face_detection/face_detect.yaml"

# 人脸特征提取模型(Face-net) @zhaohanjia
face_emb_openvino_model_xml_path: "./downloads/facenet/facenet.xml"
face_emb_openvino_model_bin_path: "./downloads/facenet/facenet.bin"
face_emb_model_config_path: "./algorithm/basic_model_module/face_emb_encoder/face_emb_encoder.yaml"

# 场景分类模型-判断新闻二分类(RegNetX) @tanguozhu
news_classify_model_xml_path: "downloads/news_240424/news_classify.xml"
news_classify_model_bin_path: "downloads/news_240424/news_classify.bin"


### Part2 非模型代码相关文件路径 ###

# 人脸特征库位置
face_lib_path: "algorithm/img_parse_module/content_extract_module/face_recog_feature_library/"
ocr_init_cfg_path: "algorithm/basic_model_module/default_parse.yaml"
