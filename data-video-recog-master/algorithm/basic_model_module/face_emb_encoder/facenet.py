import numpy as np
import cv2
from openvino.inference_engine import <PERSON><PERSON>ore
from algorithm.utils.parse_model_config import do_parse_model_config


class FaceRec:
    def __init__(self, model_xml_path, model_bin_path, config_path):
        # 模型初始化
        ie = IECore()
        net = ie.read_network(model=model_xml_path, weights=model_bin_path)
        self.exec_net = ie.load_network(network=net, device_name="CPU")

        # 默认参数
        self.config = do_parse_model_config(config_path)
        self.m_input_size = self.config["m_input_size"]
        self.rgb_mean = self.config["rgb_mean"]

    def __call__(self, img_data):
        padding_img = self.padding_resize(img_data, (self.m_input_size, self.m_input_size))
        std_img = np.float32(padding_img)
        std_img /= 255.0
        std_img = std_img.transpose(2, 0, 1)
        std_img = np.expand_dims(std_img, axis=0)
        output = self.exec_net.infer({'face': std_img})
        # print(output)
        return output['feature'], padding_img

    def padding_resize(self, in_image, size):
        # print(in_image.shape)
        ih, iw = in_image.shape[:2]
        h, w = size
        scale = min( w /iw, h/ ih)
        nw = int(iw * scale)
        nh = int(ih * scale)
        in_image = cv2.resize(in_image, (nw, nh), interpolation=cv2.INTER_CUBIC)
        padding_array = np.ones([h, w, 3], dtype='uint8')
        # print(padding_array)
        padding_array *= 128
        padding_array[(h - nh) // 2: (h + nh) // 2, (w - nw) // 2: (w + nw) // 2] = in_image

        return padding_array
