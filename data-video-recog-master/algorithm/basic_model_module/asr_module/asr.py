import requests
import json
import base64
from algorithm.basic_model_module import BaseOnlineModel, CustomError
import traceback


class FunASR(BaseOnlineModel):
    def __init__(self, config, health_check_flag=True):
        self.post_url = config["infer_url"]
        self.health_url = config["heahth_url"]
        if health_check_flag:
            self.is_service_health(self.health_url)

        # 载入热词
        self.hot_words = config["hot_words"]

    @staticmethod
    def encode_file_to_base64(file_path):
        with open(file_path, "rb") as file_file:
            encoded_string = base64.b64encode(file_file.read())
            return encoded_string.decode('utf-8')


    def post_service(self, input_dict, service_url):
        try:
            response = requests.post(url=service_url, data=json.dumps(input_dict))

            if response.status_code == 200:
                result = json.loads(response.text)
                return result

        except:
            err_detail = traceback.format_exc()  # 具体报错
            raise CustomError("算法模型服务{}请求失败，无法发出post请求。detail={}".format(self.__class__.__name__, err_detail))


    def server_test(self, audio_wav, video_id):
        wavbase64 = self.encode_file_to_base64(audio_wav)
        data = {
            "audio_id": video_id,
            "audio_url": "",
            "audio_base64": wavbase64,
            "hot_words": self.hot_words
        }

        asr_res = self.post_service(data, self.post_url)
        return asr_res