import glob

from algorithm.utils import parse_model_config
from algorithm.basic_model_module import face_detection, face_emb_encoder
import cv2
import numpy as np
import os

os.chdir("../../")

model_config_path = "algorithm/basic_model_module/model_config.yaml"
model_configs = parse_model_config.do_parse_model_config(model_config_path)


class FaceAgent:
    def __init__(self):
        self.face_detector = face_detection.FaceDetect(model_configs["face_detect_openvino_model_xml_path"],
                                                       model_configs["face_detect_openvino_model_bin_path"],
                                                       model_configs["face_detect_model_config_path"])

        self.face_emb_encoder = face_emb_encoder.FaceRec(model_configs["face_emb_openvino_model_xml_path"],
                                                         model_configs["face_emb_openvino_model_bin_path"],
                                                         model_configs["face_emb_model_config_path"])

    def get_feature(self, image):
        feature, padding_face = self.face_emb_encoder(image)
        feature = feature / np.linalg.norm(feature, axis=1)
        return feature, padding_face

    def get_face_feat(self, image):
        # step1 人脸检测
        face_list, face_box_list = self.face_detector(image)

        # step2 人脸特征提取
        feature_list = []
        for face_res in face_list:
            face, conf = face_res
            feature, padding_face = self.get_feature(face)
            feature_list.append(feature)

        face_list = [cv2.cvtColor(x[0], cv2.COLOR_RGB2BGR) for x in face_list]
        return face_list, feature_list




def main():
    tool = FaceAgent()
    save_dir = "/Users/<USER>/Desktop/临时查看/张国立"
    img_dir = "/Users/<USER>/Desktop/临时查看/张国立"
    img_list = glob.glob(img_dir + '/*.jpg')
    img_list.sort()

    for idx, image_path in enumerate(img_list):
        image = cv2.imread(image_path)

        face_list, feature_list = tool.get_face_feat(image)

        # 保存
        np.save("{}/1-{}.npy".format(save_dir, idx+6), feature_list[0], allow_pickle=True)  # 保存
        cv2.imwrite("{}/1-{}.jpg".format(save_dir, idx+6), face_list[0])


if __name__ == '__main__':
    main()
