FROM base-registry.zhonganinfo.com/vision/py39-confluent_kafka-2_0_2:v0


RUN sed -i 's/\(snapshot\|deb\|security\).debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list
RUN apt-get update && apt-get install -y libsm6 libgl1
RUN apt-get install -y libzbar0


ARG PyPI_CN_HOST=pypi.tuna.tsinghua.edu.cn
ARG PyPI_CN_REPO=https://pypi.tuna.tsinghua.edu.cn/simple

RUN echo "[global]" > /etc/pip.conf \
    && echo "index-url = ${PyPI_CN_REPO}" >> /etc/pip.conf \
    && echo "trusted-host = ${PyPI_CN_HOST}" >> /etc/pip.conf \
    && python3 -m pip install -U pip \
    && python3 -m pip install wheel


# pip源 -i http://pypi.zhonganinfo.com/root/public --trusted-host pypi.zhonganinfo.com
# pip源 -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
COPY requirements.txt /tmp/requirements.txt
RUN python3 -m pip install --upgrade pip && python3 -m pip install -r /tmp/requirements.txt && rm /tmp/requirements.txt
RUN python3 -m pip install opencv-python==******** && python3 -m pip install opencv-contrib-python==********


