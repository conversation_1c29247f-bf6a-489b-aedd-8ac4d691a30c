#!/usr/bin/env python3
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import os

def create_test_pdf():
    # 创建一个简单的PDF测试文件
    filename = 'test_files/理财产品说明书.pdf'
    
    # 确保目录存在
    os.makedirs('test_files', exist_ok=True)
    
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # 添加标题
    c.setFont("Helvetica-Bold", 16)
    c.drawString(100, height - 100, "Bank Financial Product Description")
    
    # 添加中文内容（如果支持的话）
    c.setFont("Helvetica", 12)
    y_position = height - 150
    
    content = [
        "Product Name: Premium Investment Plan",
        "Annual Return Rate: 4.8%",
        "Investment Period: 24 months",
        "Risk Level: Medium-Low Risk",
        "",
        "Key Features:",
        "- Guaranteed principal protection",
        "- Flexible withdrawal options",
        "- Professional fund management",
        "- Regular performance reports",
        "",
        "Risk Warning:",
        "Investment involves risks. Past performance does not",
        "guarantee future results. Please read the terms",
        "and conditions carefully before investing.",
        "",
        "Contact Information:",
        "Customer Service: 400-123-4567",
        "Email: <EMAIL>",
        "Website: www.bank.com"
    ]
    
    for line in content:
        c.drawString(100, y_position, line)
        y_position -= 20
        if y_position < 100:  # 如果接近页面底部，换页
            c.showPage()
            y_position = height - 100
    
    c.save()
    print(f"PDF测试文件已创建: {filename}")

if __name__ == "__main__":
    try:
        create_test_pdf()
    except ImportError:
        print("需要安装reportlab库: pip install reportlab")
        # 创建一个简单的文本文件作为替代
        with open('test_files/理财产品说明书.txt', 'w', encoding='utf-8') as f:
            f.write("""理财产品说明书

产品名称：优选投资计划
年化收益率：4.8%
投资期限：24个月
风险等级：中低风险

主要特点：
- 本金保障
- 灵活提取
- 专业管理
- 定期报告

风险提示：
投资有风险，过往业绩不代表未来表现。
请在投资前仔细阅读条款和条件。

联系方式：
客服电话：400-123-4567
邮箱：<EMAIL>
网站：www.bank.com
""")
        print("已创建文本版本的测试文件: test_files/理财产品说明书.txt")
