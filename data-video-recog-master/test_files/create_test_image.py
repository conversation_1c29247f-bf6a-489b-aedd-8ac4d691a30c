#!/usr/bin/env python3
from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image():
    # 创建一个简单的测试图片
    width, height = 800, 600
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # 添加文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 40)
    except:
        # 如果找不到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 绘制标题
    title = "银行产品宣传图"
    draw.text((50, 50), title, fill='black', font=font)
    
    # 绘制内容
    content = [
        "理财产品介绍",
        "年化收益率: 4.5%",
        "投资期限: 12个月",
        "风险等级: 中低风险"
    ]
    
    y_pos = 150
    for line in content:
        draw.text((50, y_pos), line, fill='blue', font=font)
        y_pos += 60
    
    # 绘制一个简单的图形
    draw.rectangle([50, 400, 750, 550], outline='red', width=3)
    draw.text((60, 450), "重要提示: 投资有风险，理财需谨慎", fill='red', font=font)
    
    # 保存图片
    image.save('test_files/银行宣传图.png')
    print("测试图片已创建: test_files/银行宣传图.png")

if __name__ == "__main__":
    # 确保目录存在
    os.makedirs('test_files', exist_ok=True)
    create_test_image()
