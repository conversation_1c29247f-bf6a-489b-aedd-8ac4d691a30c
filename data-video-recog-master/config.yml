kafka:
  kafka_servers: ************:19092
  read_topic: topic_read
  write_topic: topic_write


mysql:
  request_cache:
    host: rm-bp1g3s1c53we221gk212.mysql.rds.aliyuncs.com
    port: 3306
    user: tst_user_4b08603
    password: tst_user_4b08603_17b54e
    db: video_understanding_00
    charset: utf8mb4
    table: video_understanding_request
    query_interval: 3
  asr_ache:
    host: rm-bp1g3s1c53we221gk212.mysql.rds.aliyuncs.com
    port: 3306
    user: tst_user_4b08603
    password: tst_user_4b08603_17b54e
    db: video_understanding_00
    charset: utf8mb4
    table: asr_result
    query_interval: 3


model_config:
  FunASR:
#    infer_url: http://za-video-asr.test.za.biz/inference_funasr
#    heahth_url: http://za-video-asr.test.za.biz/health
#    infer_url: http://*************:18091/inference_funasr
#    heahth_url: http://*************:18091/health
    infer_url: http://localhost:8080/inference_funasr
    heahth_url: http://localhost:8080/health
    hot_words: "众安,尊享e生,百万医疗"
  default_parse_config:
    face_recognize:
      face_feature_threshold: 0.65
    default_param:
      time_interval: 3
      begin_margin: 1
      end_margin: 1
  TextRiskClassification:
    model_name: "TextRiskClassification"
    version_spec: "20241203"
    model_unload_delay_seconds: 360
    model_config:
      name: "TextRiskClassification"
      version: "20241203"
      model_path: downloads/text_risk_classification/ocrasr_4.m
      extra_config:
        threshold: 0.6532
  QR_3rdparty_config:
    opencv_3rdparty-wechat_qrcode_model_detect_xml_path: downloads/opencv_3rdparty-wechat_qrcode/detect.prototxt
    opencv_3rdparty-wechat_qrcode_model_detect_bin_path: downloads/opencv_3rdparty-wechat_qrcode/detect.caffemodel
    opencv_3rdparty-wechat_qrcode_model_sr_xml_path: downloads/opencv_3rdparty-wechat_qrcode/sr.prototxt
    opencv_3rdparty-wechat_qrcode_model_sr_bin_path: downloads/opencv_3rdparty-wechat_qrcode/sr.caffemodel
  QR_code_detect_config:
    qr_code_detect_model_path: downloads/qrcode_detect/best_0122.onnx

local_model_config_path: algorithm/basic_model_module/model_config.yaml



