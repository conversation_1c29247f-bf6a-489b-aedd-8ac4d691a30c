FROM base-registry.zhonganinfo.com/vision/data-video-recog:v0

COPY . /root/app/
WORKDIR /root/app/
ENV PYTHONPATH=/root/app

ENV SERVICE_PORT=8080

# pip源 -i http://pypi.zhonganinfo.com/root/public --trusted-host pypi.zhonganinfo.com
# pip源 -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 模型下载
RUN wget 'http://dsl-os.oss-cn-hzfinance-internal.aliyuncs.com/zaintl/Xmagnet/downloads_Xmagnet_20241203.zip?OSSAccessKeyId=LTAI5tFVcczJVGzMbCH3Kruk&Expires=2363937781&Signature=unlEeCG%2FvQAOKqOVKx2nqLeorSY%3D' -O /tmp/models.zip && \
unzip /tmp/models.zip -d /tmp/ && \
mv /tmp/downloads /root/app/ && \
rm /tmp/models.zip && rm -rf /tmp/downloads


CMD ["/bin/bash", "run.sh"]
