#!/usr/bin/env python3
import requests
import json

def test_fixed_upload():
    url = "http://localhost:8080/upload"
    
    print("=" * 60)
    print("测试修复后的文件上传功能")
    print("=" * 60)
    
    # 测试1：理财产品说明书（包含金融关键词）
    print("\n📄 测试1: 理财产品说明书")
    try:
        with open("test_files/理财产品说明书.txt", "rb") as f:
            files = {"file": ("理财产品说明书.txt", f, "text/plain")}
            response = requests.post(url, files=files)
            print(f"✅ 状态码: {response.status_code}")
            result = response.json()
            print(f"📊 风险等级: {result['audit_result']['risk_evaluation']}")
            print(f"🔍 OCR结果: {result['audit_result']['ocr_result']}")
            print(f"✔️ 合规检查: {result['audit_result']['compliance_check']}")
    except Exception as e:
        print(f"❌ 上传失败: {e}")
    
    # 测试2：银行宣传图片
    print("\n🖼️  测试2: 银行宣传图片")
    try:
        with open("test_files/银行宣传图.png", "rb") as f:
            files = {"file": ("银行宣传图.png", f, "image/png")}
            response = requests.post(url, files=files)
            print(f"✅ 状态码: {response.status_code}")
            result = response.json()
            print(f"📊 风险等级: {result['audit_result']['risk_evaluation']}")
            print(f"🔍 OCR结果: {result['audit_result']['ocr_result']}")
            print(f"👤 人脸检测: {result['audit_result']['face_detection']}")
    except Exception as e:
        print(f"❌ 上传失败: {e}")
    
    # 测试3：查看所有记录
    print("\n📋 测试3: 查看所有审核记录")
    try:
        response = requests.get("http://localhost:8080/records")
        result = response.json()
        print(f"✅ 总记录数: {len(result['records'])}")
        for i, record in enumerate(result['records'][-2:], 1):  # 显示最后2条记录
            print(f"\n记录 {i}:")
            print(f"  📁 文件名: {record['filename']}")
            print(f"  🕒 上传时间: {record['upload_time']}")
            print(f"  📊 风险等级: {record['audit_result']['risk_evaluation']}")
            print(f"  🔒 追踪ID: {record['trace_id']}")
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！现在可以访问 http://localhost:8080/demo 进行Web界面测试")
    print("=" * 60)

if __name__ == "__main__":
    test_fixed_upload()
