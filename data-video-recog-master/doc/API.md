# data-video-recog
## 短视频智能审核
## 1、需求与背景

服务于X-magnet系统，对视频合规性进行智能分析。

## 2、接口定义

| 版本号  |  最后更改时间  | 撰写人 | 说明                        |
|:----:|:--------:| :----: |:--------------------------|
| v1.0 | 20231110 |  孙杰  |                           |
| v1.1 | 20240425 |  孙杰  | 新增新闻场景识别标签                |
| v1.2 | 20240902 |  孙杰  | 新增ASR功能，改造服务层逻辑，引入mysql   |
| v1.3 | 20240925 |  孙杰  | 补充，请求时增加md5字段，避免ASR重复计算   |
| v1.4 | 20241119 |  孙杰  | 新增基于视频文字的风险而分类模型，供保险侧视频使用 |
| v1.5 | 20241202 |  孙杰  | 新增针对每一帧的二维码检查             |


#### 算法描述

- 对视频截取帧。
- 人脸检测。
- OCR返回文字结果
- ASR返回文字结果


#### 测试接口链接

- 采用kafka消息队列的方式进行任务消费。
- test环境kafka集群地址：kafka.test.za-tech.net:9092 
- pre环境kafka集群地址：kafka.pre.za-tech.net:9092
- prd环境kafka集群地址：kafka.prd.za-tech.net:9092

平台端消费：
- test: D-dm-media-audit-request-************
- pre: S-dm-media-audit-request-************
- prd: P-dm-media-audit-request-************


算法端消费：
- test: D-dm-media-audit-response-************
- pre: S-dm-media-audit-response-************
- prd: P-dm-media-audit-response-************


> kafka 消息

### 请求参数

### 请求示例

| 参数                                | 必选    | 类型           | 默认值 | 说明                      |
|-----------------------------------|-------|--------------|-----|-------------------------|
| srvLogTraceId                     | true  | string       | /   | 请求uuid。可以是视频id+时间戳      |
| reqStartTime                      | true  | int          | /   | 请求发起时间戳（单位毫秒）           |
| data                              | true  | dict[object] | /   | 请求内容                    |
| data.video_info                   | true  | dict[object] | /   | 案件基本信息                  |
| data.video_info.id                | true  | string       | /   | 视频或图片的唯一id号             |
| data.video_info.url               | true  | string       | /   | 视频的url                  |
| data.video_info.md5               | true  | string       | /   | 视频的md5                  |
| data.video_info.data_type         | true  | int          | /   | 请求内容的类型，1:视频,2:图片       |
| data.optional_param               | false | dict[object] | /   | 可选参数                    |
| data.optional_param.time_interval | false | float        | 3   | 取帧间隔时间，单位秒。             |
| data.optional_param.begin_margin  | false | float        | 1   | 开头跳过的时间，开始1秒时会抽第一帧，单位秒。 |
| data.optional_param.end_margin    | false | float        | 1   | 末尾跳过的时间，倒数1秒时会加抽一帧，单位秒。 |


```json
// 请求内容示例
{
    "srvLogTraceId": "请求UUID",
    "reqStartTime": 1675003131,
    "data": {
        "data_info": {
            "id": "video_id",
            "url": "http:/abc/123.mp4",
            "md5": "a1b2c3d4",
            "data_type": 1
        }},
    "optional_param": {
            "time_interval": 3.0,
            "begin_margin": 1.0,
            "end_margin": 1.0
        }
}

```

### 返回结果参数

| 参数                                 | 类型           | 说明                           |
|------------------------------------|--------------|------------------------------|
| srvLogTraceId                      | string       | 请求uuid，同请求参数                 |
| success                            | bool         | 返回请求状态,True:成功, False:失败     |
| errcode                            | string       | 状态错误码见异常信息说明                 |
| message                            | string       | 简短的描述                        |
| detail                             | string       | 具体代码报错信息，便于快速定位bug           |
| result                             | dict[object] | 返回结果内容                       |
| result.data_info                   | dict[object] | 当前视频的基本信息                    |
| result.data_info.id                | string       | 视频唯一id                       |
| result.data_info.duration          | float        | 视频时间长度，图片则为0                 |
| result.data_info.frames            | int          | 视频总帧数，图片则为1                  |
| result.data_info.fps               | float        | 视频总帧数，图片则为null               |
| result.data_info.size              | list[object] | 视频/图片的分辨率                    |
| result.data_info.risk_flag         | dict[object] | 视频综合风险评估，仅对视频有效              |
| result.data_info.risk_flag.value   | bool         | 视频综合风险评估，false：无风险，true:高风险  |
| result.data_info.risk_flag.score   | float        | 视频综合风险评估，置信度，供后续动态调整阈值使用。    |
| result.frame_list                  | list[object] | 按帧返回结果                       |
| result.frame_list.info             | dict[object] | 当前帧的信息                       |
| result.frame_list.info.time_stamp  | float        | 当前帧时间戳                       |
| result.frame_list.info.frame_stamp | float        | 当前帧序号                        |
| result.frame_list.face             | list[object] | 当前帧的人脸结果                     |
| result.frame_list.face.face_type   | int          | 人脸类型。目前 0:素人，1:张国立，2:黄磊      |
| result.frame_list.face.name        | string       | 业务可不关注。如匹配上人脸库中的结果，这里记录匹配编号。 |
| result.frame_list.face.location    | list[object] | 人脸位置框                        |
| result.frame_list.text             | list[object] | 当前帧的ocr文字结果                  |
| result.frame_list.text.words       | string       | 当前文字切片的文字内容                  |
| result.frame_list.text.location    | list[object] | 当前文字切片的文字位置框                 |
| result.frame_list.text.score       | float        | 当前文字切片的置信度                   |
| result.frame_list.label            | dict[object] | 当前帧的标签                       |
| result.frame_list.label.news       | bool         | 当前帧是否疑似新闻画面                  |
| result.frame_list.label.QR_code    | bool         | 当前帧是否存在可解析的二维码               |
| result.ASR_list                    | list[object] | 存放整个视频的ASR结果                 |
| result.ASR_list.text               | bool         | 一段ASR结果的文字内容                 |
| result.ASR_list.start              | bool         | 一段ASR结果的起始时间，单位是毫秒           |
| result.ASR_list.end                | bool         | 一段ASR结果的结束时间，单位是毫秒           |
| result.ASR_list.spk                | bool         | 一段ASR结果的说话人id，用于语者分离         |


```json
// 返回结果示例
{
    "srvLogTraceId": "请求UUID",
    "success": true,
    "errcode": 0,
    "message": "",
    "detail": "",
    "result": {
        "data_info": {
            "id": "video_id",
            "duration": 60,
            "frames": 1440,
            "fps": 29,
            "size": [720, 1280],
            "risk_flag": {
              "value": true, 
              "score": 0.8
            }
        },
        "frame_list": [
            {
                "info": {
                    "time_stamp": 4.0,
                    "frame_stamp": 96
                },
                "face": [
                    {"face_type": 0,
                      "name": null,
                     "location": [x1, y1, x2, y2]},
                    {"face_type": 1,
                      "name": "1-2",
                     "location": [x1, y1, x2, y2]}
                ],
                "text": [
                    {"words": "众安体验官张国立",
                     "location": [x1, y1, x2, y2],
                     "score": 0.9},
                    {"words": "每月最低仅需1元起",
                     "location": [x1, y1, x2, y2],
                     "score": 0.9}
                    ],
              	"label":{
                  "news": true,
                  "QR_code": true
                }
            },
            {
                "info": {
                    "time_stamp": 7.0,
                    "frame_stamp": 168
                },
                "face": [
                    {"face_type": 2,
                     "location": [x1, y1, x2, y2]}
                ],
                "text": [
                    {"words": "众安体验官张国立",
                     "location": [x1, y1, x2, y2],
                     "score": 0.9},
                    {"words": "每月最低仅需1元起",……
                     "location": [x1, y1, x2, y2],
                     "score": 0.9}
                ],
                "label":{
                  "news": false,
                  "QR_code": false
                }
            }
            ],
        "ASR_list":[
            {"text": "也没有最高六百万保额呀，",
            "start": 15710,
            "end": 17250,
            "spk": 0},
            {"text": "他应该是投错了。",
            "start": 26150,
            "end": 27250,
            "spk": 1},
            {"text": "我们点击视频下方的链接，",
            "start": 98610,
            "end": 100450,
            "spk": 2}

        ]
}
}
```



