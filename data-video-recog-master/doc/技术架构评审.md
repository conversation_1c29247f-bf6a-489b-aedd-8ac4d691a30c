# 素材审核Agent - 技术架构评审

## 项目概述

基于大模型+工程规则的智能多模态内容审核系统，通过AI自动解析业务规则文档，生成结构化规则集，实现高效准确的内容审核。

## 系统架构

### 双链路业务流程

**链路一：规则配置流程**
1. 规则文档上传 → 2. 规则Agent解析 → 3. 知识库存储 → 4. 规则生效

**链路二：素材审核流程**  
1. 素材文件上传 → 2. 多模态解析 → 3. 审核Agent处理 → 4. 结果输出

### 核心组件

- **素材审核应用端**：用户界面，文件上传，结果展示
- **本地小模型服务**：视频切片、OCR识别、ASR语音识别、人脸检测
- **AI中台**：知识库、审核Agent、规则Agent、大模型审核、工程规则、结果融合

## 实现亮点

### 1. AI智能规则生成
基于大模型的规则文档理解能力，自动解析业务规则文档，生成结构化规则集。支持自然语言描述的复杂审核逻辑自动转换为可执行规则。

```bash
# 输入：自然语言规则文档
"金融产品宣传视频中，如果画面出现具体收益率数字，
 则语音必须同时包含风险提示语，如'投资有风险'等"
```

```json
# AI自动生成规则集
{
  "rule_id": "finance_risk_disclosure",
  "rule_name": "收益率风险提示配对规则",
  "audit_type": "ENGINE",
  "engine_type": "complex_match",
  "engine_config": {
    "text_sources": ["ocr", "asr"],
    "match_logic": "AND"
  },
  "engine_rule": {
    "logic": "AND",
    "conditions": [
      {
        "type": "conditional_cross_source",
        "if_ocr_contains": ["年化收益", "%", "收益率"],
        "then_asr_must_contain": ["投资有风险", "风险提示"]
      }
    ]
  }
}
```

### 2. LLM+工程规则混合审核
创新性地将大语言模型的语义理解能力与工程规则的精确匹配能力相结合，实现高效且准确的内容审核。工程规则负责快速筛选，LLM负责复杂语义判断。

```yaml
# 混合审核流程
1. 工程规则预筛选（毫秒级）
   - 关键词匹配：快速识别明显违规内容
   - 正则表达式：检测格式化违规信息
   - 基础特征检测：人脸、身份证等敏感元素

2. LLM语义审核（秒级）
   - 上下文理解：判断是否真实违规
   - 语义推理：识别隐晦表达和暗示
   - 场景分析：结合视频剧情判断合规性

3. 结果融合决策
   - 工程规则：高置信度直接判定
   - LLM审核：处理边界案例和复杂场景
   - 综合评分：多维度风险评估
```

### 3. 规则集技术设计
设计了层次化、可扩展的规则集数据结构，支持复杂业务逻辑的精确表达。采用JSON Schema规范，确保规则的标准化和可维护性。

```json
# 规则集核心字段设计
{
  "rule_id": "string",           // 规则唯一标识
  "rule_name": "string",         // 规则名称
  "business_category": "string", // 业务分类：金融/医疗/教育等
  "audit_type": "ENGINE|LLM",    // 审核类型
  "priority": "HIGH|MEDIUM|LOW", // 优先级
  "status": "ACTIVE|INACTIVE",   // 状态
  
  // 工程规则配置
  "engine_type": "complex_match|contains_all|regex_match",
  "engine_config": {
    "text_sources": ["ocr", "asr", "all"],
    "match_logic": "AND|OR",
    "case_sensitive": false,
    "threshold": {"confidence": 0.8}
  },
  "engine_rule": {
    "logic": "AND|OR",
    "conditions": [...]
  }
}
```

### 4. 动态规则热更新
支持规则的实时更新和版本管理，业务人员可通过管理界面直接修改规则配置，系统自动进行规则验证和灰度发布，无需重启服务。

```json
# 规则版本管理
{
  "rule_version": "v2.1.0",
  "update_time": "2024-12-20T10:30:00Z",
  "update_reason": "新增跨模态条件检查",
  "rollback_version": "v2.0.5",
  "test_coverage": {
    "passed": 156,
    "failed": 2,
    "coverage_rate": "98.7%"
  }
}
```

```yaml
# 灰度发布配置
deployment_strategy: canary
traffic_split:
  v2.0.5: "80%"  # 稳定版本
  v2.1.0: "20%"  # 新版本
rollback_threshold:
  error_rate: "5%"
  response_time: "2s"
```

## 知识库规则集设计

### 基础信息
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| rule_id | string | 规则唯一标识符 | "finance_001" |
| rule_name | string | 规则显示名称 | "金融产品收益承诺审核" |
| business_category | string | 业务分类 | "finance" |
| audit_type | enum | 审核类型：ENGINE/LLM/HYBRID | "ENGINE" |
| priority | enum | 优先级：HIGH/MEDIUM/LOW | "HIGH" |
| status | enum | 规则状态：ACTIVE/INACTIVE | "ACTIVE" |

### 工程规则配置
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| engine_type | enum | 引擎类型 | "complex_match" |
| text_sources | array | 文本来源 | ["ocr", "asr"] |
| match_logic | enum | 匹配逻辑：AND/OR | "AND" |
| conditions | array | 条件列表 | [{...}, {...}] |

### LLM规则配置
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| model | string | 模型名称 | "gpt-4" |
| temperature | float | 生成随机性 | 0.1 |
| prompt_template | string | 提示词模板 | "请判断以下内容..." |
| context | string | 业务背景和规则说明 | "医疗广告不得使用..." |

### 元数据信息
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| created_time | datetime | 规则创建时间 | "2024-12-20T10:30:00Z" |
| version | string | 规则版本号 | "v2.1.0" |
| creator | string | 规则创建者 | "张三" |
| description | string | 规则详细描述 | "用于检测金融产品..." |

## 技术选型

### AI模型技术栈
- **OCR识别**：RapidOCR - 开源免费，性能良好
- **ASR识别**：FunASR - 阿里开源，中文效果好  
- **人脸检测**：自研模型 - 需要评估准确率
- **大语言模型**：GPT-4/Claude-3 - 效果优秀

### 后端技术栈
- **Web框架**：FastAPI - 异步高性能
- **数据验证**：Pydantic - 类型安全
- **数据存储**：SQLite/MySQL - 需要考虑并发性能
- **消息队列**：Kafka - 高吞吐量

## 评审结论

### 架构优势
1. **模块化设计**：分层清晰，职责明确，扩展性强
2. **混合审核策略**：工程规则快速预筛选，LLM处理复杂语义
3. **智能规则生成**：AI解析自然语言规则文档，业务友好
4. **多模态支持**：全面覆盖视频、音频、图像、文本

### 潜在风险
1. **性能瓶颈**：大模型推理耗时较长
2. **规则冲突**：多规则并行执行可能产生冲突
3. **数据安全**：敏感业务数据的安全性保护
4. **扩展性限制**：单机部署无法满足大规模并发

### 改进建议
1. **短期**：完成性能压测和优化，实现基础监控和告警
2. **中期**：实现分布式部署架构，完善规则测试和验证机制
3. **长期**：实现智能化运维，建设完整的数据分析平台

### 评审等级：B+

**评审结论**：技术架构设计整体合理，具有创新性的AI+工程规则混合审核模式，建议按改进计划逐步完善。

---
*评审时间：2024年12月*  
*评审人员：技术架构组*
