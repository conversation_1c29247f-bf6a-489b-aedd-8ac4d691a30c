# data-video-recog
## 短视频智能审核
## 1、需求与背景

服务于X-magnet系统，对视频合规性进行智能分析。

## 2、接口定义

| 版本号 | 最后更改时间 | 撰写人 | 说明 |
| :----: | :----------: | :----: | :--- |
|  v1.0  |   20231110   |  孙杰  |      |



#### 算法描述

- 对视频截取帧。
- 人脸检测。
- OCR返回文字结果


#### 测试接口链接

- 采用kafka消息队列的方式进行任务消费。
- test环境kafka集群地址：kafka.test.za-tech.net:9092 
- pre环境kafka集群地址：kafka.pre.za-tech.net:9092
- prd环境kafka集群地址：kafka.prd.za-tech.net:9092

平台端消费：
- test: D-dm-media-audit-request-************
- pre: S-dm-media-audit-request-************
- prd: P-dm-media-audit-request-************


算法端消费：
- test: D-dm-media-audit-response-************
- pre: S-dm-media-audit-response-************
- prd: P-dm-media-audit-response-************


> kafka 消息

### 请求参数

### 请求示例

| 参数                                | 必选    | 类型           | 默认值 | 说明                                          |
|-----------------------------------|-------|--------------|-----|---------------------------------------------|
| srvLogTraceId                     | true  | string       | /   | 请求uuid。可以是视频id+时间戳                          |
| reqStartTime                      | true  | int          | /   | 请求发起时间戳（单位毫秒）                               |
| data                              | true  | dict[object] | /   | 请求内容                                        |
| data.video_info                   | true  | dict[object] | /   | 案件基本信息                                      |
| data.video_info.id                | true  | string       | /   | 视频或图片的唯一id号                                    |
| data.video_info.url               | true  | string       | /   | 视频的url                                      |
| data.video_info.data_type              | true  | int          | /   | 请求内容的类型，1:视频,2:图片                                      |
| data.optional_param               | false | dict[object] | /   | 可选参数                                        |
| data.optional_param.time_interval | false | float        | 3   | 取帧间隔时间，单位秒。                                 |
| data.optional_param.begin_margin  | false | float        | 1   | 开头跳过的时间，开始1秒时会抽第一帧，单位秒。                                |
| data.optional_param.end_margin    | false | float        | 1   | 末尾跳过的时间，倒数1秒时会加抽一帧，单位秒。                                |


```json
// 请求内容示例
{
    "srvLogTraceId": "请求UUID",
    "reqStartTime": 1675003131,
    "data": {
        "data_info": {
            "id": "video_id",
            "url": "http:/abc/123.mp4",
            "data_type": 1
        }}
    "optional_param": {
            "time_interval": 3.0,
            "begin_margin": 1.0,
            "end_margin": 1.0
        }
}

```

### 返回结果参数

| 参数                                 | 类型           | 说明                         |
|------------------------------------|--------------|----------------------------|
| srvLogTraceId                      | string       | 请求uuid，同请求参数               |
| success                            | bool         | 返回请求状态,True:成功, False:失败   |
| errcode                            | string       | 状态错误码见异常信息说明               |
| message                            | string       | 简短的描述                      |
| detail                             | string       | 具体代码报错信息，便于快速定位bug         |
| result                             | dict[object] | 返回结果内容                     |
| result.data_info                  | dict[object] | 当前视频的基本信息                  |
| result.data_info.id               | string       | 视频唯一id                     |
| result.data_info.duration         | float        | 视频时间长度，图片则为0                     |
| result.data_info.frames           | int          | 视频总帧数，图片则为1                      |
| result.data_info.fps              | float          | 视频总帧数，图片则为null                      |
| result.data_info.size             | list[object]    | 视频/图片的分辨率                      |
| result.frame_list                  | list[object] | 按帧返回结果                     |
| result.frame_list.info             | dict[object] | 当前帧的信息                     |
| result.frame_list.info.time_stamp  | float        | 当前帧时间戳                     |
| result.frame_list.info.frame_stamp | float        | 当前帧序号                      |
| result.frame_list.face             | list[object] | 当前帧的人脸结果                   |
| result.frame_list.face.face_type   | int          | 人脸类型。目前 0:素人，1:张国立，2:黄磊  |
| result.frame_list.face.name        | string       | 业务可不关注。如匹配上人脸库中的结果，这里记录匹配编号。  |
| result.frame_list.face.location    | list[object] | 人脸位置框                      |
| result.frame_list.text             | list[object] | 当前帧的ocr文字结果                |
| result.frame_list.text.words       | string       | 当前文字切片的文字内容                |
| result.frame_list.text.location    | list[object] | 当前文字切片的文字位置框               |
| result.frame_list.text.score       | float        | 当前文字切片的置信度                 |

```json
// 返回结果示例
{
    "srvLogTraceId": "请求UUID",
    "success": true,
    "errcode": 0,
    "message": "",
    "detail": "",
    "result": {
        "data_info": {
            "id": "video_id",
            "duration": 60,
            "frames": 1440,
            "fps": 29,
            "size": [720, 1280]
        },
        "frame_list": [
            {
                "info": {
                    "time_stamp": 4.0,
                    "frame_stamp": 96
                },
                "face": [
                    {"face_type": 0,
                      "name": null,
                     "location": [x1, y1, x2, y2]},
                    {"face_type": 1,
                      "name": "1-2",
                     "location": [x1, y1, x2, y2]}
                ],
                "text": [
                    {"words": "众安体验官张国立",
                     "location": [x1, y1, x2, y2],
                     "score": 0.9},
                    {"words": "每月最低仅需1元起",
                     "location": [x1, y1, x2, y2],
                     "score": 0.9}
                    ]
            },
            {
                "info": {
                    "time_stamp": 7.0,
                    "frame_stamp": 168
                },
                "face": [
                    {"face_type": 2,
                     "location": [x1, y1, x2, y2]}
                ],
                "text": [
                    {"words": "众安体验官张国立",
                     "location": [x1, y1, x2, y2],
                     "score": 0.9},
                    {"words": "每月最低仅需1元起",
                     "location": [x1, y1, x2, y2],
                     "score": 0.9}
                ]
            }
]
}
}
```




