import json
from confluent_kafka import Producer
from config import get_config
from .zalog import get_app_logger

configs = get_config()
app_logger = get_app_logger(configs)

class KafkaProducer:
    def __init__(self, servers, topic):
        self._servers = servers
        self._topic = topic
        self._producer = Producer({"bootstrap.servers": self._servers})
        
    # handle exception
    @staticmethod
    def delivery_report(err, msg):
        """ Called once for each message produced to indicate delivery result.
            Triggered by poll() or flush(). """
        if err is not None:
            app_logger.info('Message delivery failed: {}'.format(err))
        else:
            app_logger.info('Message delivered to {} [{}]'.format(msg.topic(), msg.partition()))

    def send(self, write_msg: dict):
        self._producer.poll(0)
        self._producer.produce(self._topic, json.dumps(write_msg, ensure_ascii=False).encode("utf-8"),
                     callback=self.delivery_report)
        self._producer.flush()

if __name__ == "__main__":
    # 往kafka发数据测试服务，因为是测试，所以是写入read topic
    kafka_servers = "127.0.0.1:19092"
    read_topic = "topic_read"
    producer = KafkaProducer(servers=kafka_servers, topic=read_topic)
    sample_data = ["c", "d"]
    for x in sample_data:
        producer.send(x)

