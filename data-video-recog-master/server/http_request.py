from http.server import BaseHTTPRequestHandler

class HealthRequest(BaseHTTPRequestHandler):
    """这个是应付cube健康检查做的"""
    timeout = 5

    def log_message(self, *args, **kwargs):
        """不记录访问日志"""
        pass

    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header("type","get")
            self.end_headers() 

            msg = "ok"
            msg = str(msg).encode()

            self.wfile.write(msg)
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Not Found')

    def do_POST(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header("type","post")
            self.end_headers()
            msg = "ok" 
            msg = str(msg).encode()
            self.wfile.write(msg)
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Not Found')
