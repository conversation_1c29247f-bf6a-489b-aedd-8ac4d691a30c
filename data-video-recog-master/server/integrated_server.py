#!/usr/bin/env python3
"""
集成司内已验证方案的视频审核服务器
使用现有的VideoAiAuditor、ASR服务和帧提取服务
"""
import time
import json
import tempfile
import os
import hashlib
import sys
from datetime import datetime
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
import uvicorn

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 集成司内已验证的AI服务
app = FastAPI(title="视频审核服务 - 司内集成版")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据存储
upload_records = {}

# 全局AI审核器
video_ai_auditor = None
frame_extractor = None
asr_service_available = False

def init_integrated_services():
    """初始化司内已验证的AI服务"""
    global video_ai_auditor, frame_extractor, asr_service_available
    
    print("🏢 正在初始化司内已验证的AI服务...")
    
    try:
        # 1. 初始化配置
        from config import get_config
        configs = get_config()
        model_config = configs.get("model_config", {})
        
        print("  📋 配置文件加载成功")
        
        # 2. 初始化视频AI审核器
        try:
            from algorithm.video_Ai_auditor import VideoAiAuditor
            video_ai_auditor = VideoAiAuditor(model_config)
            print("  ✅ VideoAiAuditor 初始化成功")
        except Exception as e:
            print(f"  ⚠️  VideoAiAuditor 初始化失败: {str(e)}")
            video_ai_auditor = None
        
        # 3. 初始化帧提取服务
        try:
            # 检查帧提取服务是否可用
            frame_extractor_path = "../data-frame-extractor-master"
            if os.path.exists(frame_extractor_path):
                sys.path.append(frame_extractor_path)
                from algorithm.video_frame_extraction import VideoFrameExtraction
                frame_extractor = VideoFrameExtraction()
                print("  ✅ 帧提取服务初始化成功")
            else:
                print("  ⚠️  帧提取服务路径不存在")
                frame_extractor = None
        except Exception as e:
            print(f"  ⚠️  帧提取服务初始化失败: {str(e)}")
            frame_extractor = None
        
        # 4. 检查ASR服务可用性
        try:
            import requests
            asr_config = model_config.get("FunASR", {})
            health_url = asr_config.get("heahth_url")
            if health_url:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    asr_service_available = True
                    print("  ✅ ASR服务连接成功")
                else:
                    print("  ⚠️  ASR服务连接失败")
            else:
                print("  ⚠️  ASR服务配置未找到")
        except Exception as e:
            print(f"  ⚠️  ASR服务检查失败: {str(e)}")
            asr_service_available = False
        
        # 总结初始化结果
        services_count = sum([
            video_ai_auditor is not None,
            frame_extractor is not None,
            asr_service_available
        ])
        
        print(f"🎉 司内服务初始化完成，{services_count}/3 个服务可用")
        return services_count > 0
        
    except Exception as e:
        print(f"❌ 司内服务初始化失败: {str(e)}")
        return False

def get_now_time_ms():
    return int(time.time() * 1000)

def generate_trace_id():
    return f"trace_{get_now_time_ms()}_{hash(str(datetime.now()))}"

@app.get("/")
async def root():
    return {
        "message": "视频审核服务已启动", 
        "version": "integrated", 
        "time": datetime.now(),
        "services": {
            "video_ai_auditor": video_ai_auditor is not None,
            "frame_extractor": frame_extractor is not None,
            "asr_service": asr_service_available
        }
    }

@app.get("/health")
async def health():
    return {
        "status": "ok", 
        "service": "video-audit-integrated",
        "services": {
            "video_ai_auditor": video_ai_auditor is not None,
            "frame_extractor": frame_extractor is not None,
            "asr_service": asr_service_available
        }
    }

@app.get("/demo")
async def demo_page():
    """返回Demo页面"""
    demo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Demo.html")
    if os.path.exists(demo_path):
        return FileResponse(demo_path)
    else:
        raise HTTPException(status_code=404, detail="Demo页面未找到")

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传接口 - 使用司内已验证的AI服务"""
    try:
        # 生成追踪ID
        trace_id = generate_trace_id()
        
        # 读取文件内容
        content = await file.read()
        file_hash = hashlib.md5(content).hexdigest()
        
        # 保存临时文件进行分析
        temp_file_path = None
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # 使用司内已验证的AI分析
            audit_result = await integrated_analyze_file(temp_file_path, file.filename, file.content_type, len(content), trace_id)
            
        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
        # 保存记录
        record = {
            "trace_id": trace_id,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": len(content),
            "md5": file_hash,
            "upload_time": datetime.now().isoformat(),
            "audit_result": audit_result,
            "status": "completed"
        }
        
        upload_records[trace_id] = record
        
        return {
            "success": True,
            "trace_id": trace_id,
            "filename": file.filename,
            "size": len(content),
            "audit_result": audit_result,
            "message": "文件上传并使用司内AI服务分析完成"
        }
        
    except Exception as e:
        print(f"审核过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

async def integrated_analyze_file(file_path, filename, content_type, file_size, trace_id):
    """使用司内已验证的AI服务进行文件分析"""
    result = {
        "ocr_result": [],
        "face_detection": [],
        "asr_result": [],
        "qr_code_result": [],
        "risk_evaluation": "low",
        "compliance_check": True,
        "audit_time": datetime.now().isoformat(),
        "analysis_method": "integrated_ai_services",
        "file_info": {
            "filename": filename,
            "content_type": content_type,
            "file_size": file_size,
            "file_path": file_path
        },
        "services_used": []
    }
    
    try:
        if video_ai_auditor and content_type and (content_type.startswith("video/") or content_type.startswith("image/")):
            # 使用司内VideoAiAuditor进行分析
            print(f"🎬 使用VideoAiAuditor分析: {filename}")
            
            # 构造VideoAiAuditor的输入格式
            ai_input = {
                "srvLogTraceId": trace_id,
                "reqStartTime": int(time.time()),
                "reserved_info": {},
                "data": {
                    "data_info": {
                        "id": trace_id,
                        "file_path": file_path,
                        "data_type": 1 if content_type.startswith("video/") else 2  # 1=视频, 2=图片
                    }
                },
                "optional_param": {
                    "time_interval": 3.0,
                    "begin_margin": 1.0
                }
            }
            
            # 调用VideoAiAuditor
            ai_result = video_ai_auditor.process(ai_input)
            result["services_used"].append("VideoAiAuditor")
            
            # 解析AI结果
            if ai_result and "data" in ai_result:
                data = ai_result["data"]
                
                # OCR结果
                if "ocr_result" in data and data["ocr_result"]:
                    result["ocr_result"] = [item.get("text", "") for item in data["ocr_result"] if item.get("text")]
                    result["ocr_details"] = data["ocr_result"]
                
                # 人脸检测结果
                if "face_result" in data and data["face_result"]:
                    result["face_detection"] = data["face_result"]
                
                # ASR结果
                if "asr_result" in data and data["asr_result"]:
                    result["asr_result"] = data["asr_result"]
                
                # 二维码结果
                if "qr_code_result" in data and data["qr_code_result"]:
                    result["qr_code_result"] = data["qr_code_result"]
                
                # 风险评估
                if "risk_evaluation" in data:
                    result["risk_evaluation"] = data["risk_evaluation"]
                    result["compliance_check"] = data["risk_evaluation"] in ["low", "medium"]
                
                print(f"✅ VideoAiAuditor分析完成")
            else:
                print(f"⚠️  VideoAiAuditor返回结果为空")
                result["ocr_result"] = ["VideoAiAuditor分析完成，但未返回有效结果"]
        
        else:
            # 基础文件分析
            result = basic_file_analysis(filename, content_type, file_size, result)
            result["services_used"].append("basic_analysis")
        
        # 风险评估增强
        result = enhanced_risk_evaluation(result, filename)
        
    except Exception as e:
        print(f"司内AI服务分析出错: {str(e)}")
        import traceback
        traceback.print_exc()
        result["analysis_method"] = "fallback_basic_analysis"
        result["error"] = str(e)
        result = basic_file_analysis(filename, content_type, file_size, result)
    
    return result

def basic_file_analysis(filename, content_type, file_size, result):
    """基础文件分析（当AI服务不可用时的备选方案）"""
    if content_type:
        if content_type.startswith("video/"):
            result["ocr_result"] = [f"视频文件: {filename}", "需要VideoAiAuditor进行完整分析"]
            result["asr_result"] = ["需要ASR服务进行语音识别"]
            result["face_detection"] = []
        elif content_type.startswith("image/"):
            result["ocr_result"] = [f"图片文件: {filename}", "需要VideoAiAuditor进行完整分析"]
            result["face_detection"] = []
        elif "pdf" in content_type:
            result["ocr_result"] = [f"PDF文档: {filename}", "需要PDF解析工具进行文字提取"]
        elif "text" in content_type:
            result["ocr_result"] = [f"文本文件: {filename}"]
        else:
            result["ocr_result"] = [f"文件类型: {content_type}"]

    return result

def enhanced_risk_evaluation(result, filename):
    """增强的风险评估（基于司内业务规则）"""
    risk_score = 0
    risk_factors = []

    # 司内高风险关键词
    high_risk_keywords = [
        "贷款", "借贷", "投资", "理财", "股票", "基金", "保险", "融资",
        "众安贷", "尊享e生", "百万医疗", "车险", "意外险"
    ]

    # 司内中风险关键词
    medium_risk_keywords = [
        "宣传", "广告", "营销", "推广", "产品", "服务", "优惠", "活动"
    ]

    filename_lower = filename.lower()

    # 基于文件名的风险评估
    for keyword in high_risk_keywords:
        if keyword in filename_lower:
            risk_score += 3
            risk_factors.append(f"文件名包含司内高风险关键词: {keyword}")

    for keyword in medium_risk_keywords:
        if keyword in filename_lower:
            risk_score += 1
            risk_factors.append(f"文件名包含营销关键词: {keyword}")

    # 基于OCR结果的风险评估
    if result.get("ocr_result"):
        ocr_text = " ".join(result["ocr_result"]).lower()

        for keyword in high_risk_keywords:
            if keyword in ocr_text:
                risk_score += 2
                risk_factors.append(f"内容包含司内金融关键词: {keyword}")

        # 检查联系方式
        if any(char.isdigit() for char in ocr_text):
            if any(contact in ocr_text for contact in ["电话", "微信", "qq", "客服"]):
                risk_score += 1
                risk_factors.append("检测到联系方式信息")

    # 基于人脸检测的风险评估
    if result.get("face_detection") and len(result["face_detection"]) > 0:
        risk_score += 1
        risk_factors.append(f"检测到{len(result['face_detection'])}个人脸")

    # 基于ASR结果的风险评估
    if result.get("asr_result"):
        asr_text = " ".join(result["asr_result"]).lower()
        for keyword in high_risk_keywords:
            if keyword in asr_text:
                risk_score += 2
                risk_factors.append(f"语音内容包含金融关键词: {keyword}")

    # 确定风险等级（司内标准）
    if risk_score >= 6:
        result["risk_evaluation"] = "high"
        result["compliance_check"] = False
    elif risk_score >= 3:
        result["risk_evaluation"] = "medium"
        result["compliance_check"] = True
    else:
        result["risk_evaluation"] = "low"
        result["compliance_check"] = True

    result["risk_score"] = risk_score
    result["risk_factors"] = risk_factors

    return result

@app.get("/status/{trace_id}")
async def get_status(trace_id: str):
    """查询审核状态"""
    if trace_id in upload_records:
        return upload_records[trace_id]
    else:
        raise HTTPException(status_code=404, detail="记录未找到")

@app.get("/records")
async def get_all_records():
    """获取所有审核记录"""
    return {"records": list(upload_records.values())}

@app.get("/services/status")
async def get_services_status():
    """获取所有服务状态"""
    return {
        "video_ai_auditor": {
            "available": video_ai_auditor is not None,
            "description": "司内视频AI审核器"
        },
        "frame_extractor": {
            "available": frame_extractor is not None,
            "description": "司内视频帧提取服务"
        },
        "asr_service": {
            "available": asr_service_available,
            "description": "司内ASR语音识别服务"
        }
    }

if __name__ == "__main__":
    print("=" * 60)
    print("启动司内集成版视频审核服务")
    print("=" * 60)

    # 初始化司内已验证的AI服务
    services_success = init_integrated_services()

    if services_success:
        print("🚀 服务启动模式: 司内AI集成分析")
        if video_ai_auditor:
            print("  📝 OCR: RapidOCR (司内验证)")
            print("  👤 人脸检测: RetinaFace (司内验证)")
            print("  🎬 视频处理: 司内视频AI审核器")
            print("  📊 风险评估: 司内文本风险分类模型")
            print("  🔍 二维码检测: 司内QR检测器")
        if asr_service_available:
            print("  🎤 ASR: FunASR (司内验证)")
        if frame_extractor:
            print("  🎞️  帧提取: 司内视频帧提取服务")
    else:
        print("🚀 服务启动模式: 基础分析（司内AI服务不可用）")

    print("服务地址: http://localhost:8080")
    print("API文档: http://localhost:8080/docs")
    print("健康检查: http://localhost:8080/health")
    print("Demo页面: http://localhost:8080/demo")
    print("服务状态: http://localhost:8080/services/status")
    print("=" * 60)

    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
