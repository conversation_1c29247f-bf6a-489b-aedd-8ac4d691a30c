#!/usr/bin/env python
"""
@File:      mysql_tool
@Author:    sunjie
@Time:      2024/5/14 16:58
Describe:   
"""
import pymysql


class MysqlTool:
    def __init__(self, config):
        self.config = config
        self.login_cfg = {'host': config['host'],
                          'port': config['port'],
                          'user': config['user'],
                          'password': config['password'],
                          'db': config['db'],
                          'charset': config['charset']}
        connection = pymysql.connect(**self.login_cfg)      # 尝试连接

    def insert_line(self, sql_insert, data_tuple):
        connection = pymysql.connect(**self.login_cfg)
        with connection:
            # 创建游标
            with connection.cursor() as cursor:
                cursor.execute(sql_insert, data_tuple)  # 执行插入操作
                # 提交事务
                connection.commit()
        return True

    def update_line(self, sql_line, data_tuple):
        connection = pymysql.connect(**self.login_cfg)
        with connection:
            with connection.cursor() as cursor:
                # 执行更新
                res = cursor.execute(sql_line, data_tuple)
                # 提交事务
                connection.commit()
        return res

    def query_line(self, sql_line, data_tuple):
        connection = pymysql.connect(**self.login_cfg)
        with connection:
        # 创建游标
            with connection.cursor() as cursor:
                cursor.execute(sql_line, data_tuple)
        result = cursor.fetchall()
        result = [line for line in result]
        return result
