import time
import json
import numpy as np
import enum


reserved_keys = {
    "TRACEID": "srvLogTraceId",
    "REQSTART": "reqStartTime",
    "RESSTART": "respStartTime",
    "RESEND": "respEndTime"
}


def get_now_time_ms():
    return int(time.time() * 1000)


class JsonEncoder(json.JSONEncoder):
    # 自动在json压缩时将非法字符转换
    def default(self, obj):
        if isinstance(obj, bytes):
            return str(obj, encoding='utf-8')
        elif isinstance(obj, np.int64):
            return int(str(obj))
        return json.JSONEncoder.default(self, obj)


# 任务状态码
class StatusType(enum.Enum):
    pending = 10
    processing_asr = 11
    succeed_asr = 12
    failed_asr = 13
    processing_ocr = 14
    succeed_ocr = 15
    failed_ocr = 16
