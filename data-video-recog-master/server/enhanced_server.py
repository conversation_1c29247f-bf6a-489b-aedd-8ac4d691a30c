#!/usr/bin/env python3
import time
import json
import tempfile
import os
import hashlib
import mimetypes
from datetime import datetime
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
import uvicorn

# AI分析相关导入
try:
    import cv2
    import numpy as np
    import easyocr
    from PIL import Image
    CV2_AVAILABLE = True
    EASYOCR_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  部分AI库未安装: {e}")
    CV2_AVAILABLE = False
    EASYOCR_AVAILABLE = False

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    print("⚠️  MoviePy未安装，视频分析功能受限")
    MOVIEPY_AVAILABLE = False

# 增强版视频审核服务 - 集成真实AI分析
app = FastAPI(title="视频审核服务 - AI增强版")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据存储
upload_records = {}

# 全局AI分析器
ocr_reader = None
face_cascade = None

def init_ai_models():
    """初始化AI模型"""
    global ocr_reader, face_cascade
    success_count = 0

    print("🤖 正在初始化AI模型...")

    # 初始化OCR
    if EASYOCR_AVAILABLE:
        try:
            print("  📝 初始化OCR模型...")
            ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            print("  ✅ OCR模型初始化成功")
            success_count += 1
        except Exception as e:
            print(f"  ❌ OCR模型初始化失败: {str(e)}")
    else:
        print("  ⚠️  EasyOCR未安装，跳过OCR初始化")

    # 初始化人脸检测
    if CV2_AVAILABLE:
        try:
            print("  👤 初始化人脸检测模型...")
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            if not face_cascade.empty():
                print("  ✅ 人脸检测模型初始化成功")
                success_count += 1
            else:
                print("  ❌ 人脸检测模型文件加载失败")
        except Exception as e:
            print(f"  ❌ 人脸检测模型初始化失败: {str(e)}")
    else:
        print("  ⚠️  OpenCV未安装，跳过人脸检测初始化")

    if success_count > 0:
        print(f"🎉 {success_count}个AI模型初始化完成")
        return True
    else:
        print("⚠️  所有AI模型初始化失败，将使用基础分析")
        return False

def get_now_time_ms():
    return int(time.time() * 1000)

def generate_trace_id():
    return f"trace_{get_now_time_ms()}_{hash(str(datetime.now()))}"

@app.get("/")
async def root():
    return {"message": "视频审核服务已启动", "version": "enhanced", "time": datetime.now()}

@app.get("/health")
async def health():
    return {
        "status": "ok", 
        "service": "video-audit-enhanced", 
        "ai_enabled": ocr_reader is not None and face_cascade is not None
    }

@app.get("/demo")
async def demo_page():
    """返回Demo页面"""
    demo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Demo.html")
    if os.path.exists(demo_path):
        return FileResponse(demo_path)
    else:
        raise HTTPException(status_code=404, detail="Demo页面未找到")

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传接口"""
    try:
        # 生成追踪ID
        trace_id = generate_trace_id()
        
        # 读取文件内容
        content = await file.read()
        file_hash = hashlib.md5(content).hexdigest()
        
        # 保存临时文件进行分析
        temp_file_path = None
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # AI增强分析
            audit_result = await enhanced_analyze_file(temp_file_path, file.filename, file.content_type, len(content))
            
        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
        # 保存记录
        record = {
            "trace_id": trace_id,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": len(content),
            "md5": file_hash,
            "upload_time": datetime.now().isoformat(),
            "audit_result": audit_result,
            "status": "completed"
        }
        
        upload_records[trace_id] = record
        
        return {
            "success": True,
            "trace_id": trace_id,
            "filename": file.filename,
            "size": len(content),
            "audit_result": audit_result,
            "message": "文件上传并AI分析完成"
        }
        
    except Exception as e:
        print(f"审核过程出错: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

async def enhanced_analyze_file(file_path, filename, content_type, file_size):
    """AI增强文件分析"""
    result = {
        "ocr_result": [],
        "face_detection": [],
        "asr_result": [],
        "qr_code_result": [],
        "risk_evaluation": "low",
        "compliance_check": True,
        "audit_time": datetime.now().isoformat(),
        "analysis_method": "ai_enhanced_analysis",
        "file_info": {
            "filename": filename,
            "content_type": content_type,
            "file_size": file_size,
            "file_path": file_path
        }
    }
    
    try:
        if content_type and content_type.startswith("video/"):
            # 视频分析
            result = await analyze_video(file_path, result)
        elif content_type and content_type.startswith("image/"):
            # 图片分析
            result = await analyze_image(file_path, result)
        else:
            # 其他文件类型的基础分析
            result = basic_file_analysis(filename, content_type, file_size, result)
            
    except Exception as e:
        print(f"AI分析出错: {str(e)}")
        result["analysis_method"] = "fallback_basic_analysis"
        result["error"] = str(e)
    
    # 风险评估
    result = evaluate_risk(result, filename)
    
    return result

async def analyze_video(file_path, result):
    """视频AI分析"""
    print(f"🎬 开始分析视频: {file_path}")

    if not MOVIEPY_AVAILABLE and not CV2_AVAILABLE:
        result["ocr_result"] = ["视频分析需要MoviePy和OpenCV库"]
        result["asr_result"] = ["视频分析功能不可用"]
        return result

    try:
        if MOVIEPY_AVAILABLE:
            # 使用moviepy提取视频信息
            with VideoFileClip(file_path) as video:
                duration = video.duration
                fps = video.fps

                result["file_info"]["duration"] = duration
                result["file_info"]["fps"] = fps

                # 提取关键帧进行分析
                frame_times = [duration * i / 5 for i in range(1, 5)]  # 提取4个关键帧

                all_ocr_results = []
                all_faces = []

                for i, t in enumerate(frame_times):
                    if t < duration:
                        # 提取帧
                        frame = video.get_frame(t)
                        if CV2_AVAILABLE:
                            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

                            # OCR分析
                            if ocr_reader:
                                ocr_results = ocr_reader.readtext(frame_bgr)
                                for (bbox, text, conf) in ocr_results:
                                    if conf > 0.5:  # 置信度阈值
                                        all_ocr_results.append({
                                            "text": text,
                                            "confidence": conf,
                                            "frame_time": t,
                                            "frame_index": i
                                        })

                            # 人脸检测
                            if face_cascade is not None:
                                gray = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2GRAY)
                                faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                                for (x, y, w, h) in faces:
                                    all_faces.append({
                                        "confidence": 0.8,  # 估计置信度
                                        "position": f"({x},{y},{w},{h})",
                                        "frame_time": t,
                                        "frame_index": i
                                    })

                # 整理OCR结果
                if all_ocr_results:
                    result["ocr_result"] = [item["text"] for item in all_ocr_results]
                    result["ocr_details"] = all_ocr_results
                else:
                    result["ocr_result"] = ["未检测到文字内容"]

                # 整理人脸检测结果
                if all_faces:
                    result["face_detection"] = all_faces
                else:
                    result["face_detection"] = []

                # 模拟ASR（语音识别）
                result["asr_result"] = [f"视频时长: {duration:.2f}秒", "需要专业ASR模型进行语音识别"]

        elif CV2_AVAILABLE:
            # 使用OpenCV进行基础视频分析
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                result["ocr_result"] = ["无法打开视频文件"]
                return result

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0

            result["file_info"]["duration"] = duration
            result["file_info"]["fps"] = fps
            result["file_info"]["frame_count"] = frame_count

            # 提取几个关键帧
            frame_indices = [int(frame_count * i / 5) for i in range(1, 5)]
            all_ocr_results = []
            all_faces = []

            for i, frame_idx in enumerate(frame_indices):
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()

                if ret:
                    # OCR分析
                    if ocr_reader:
                        ocr_results = ocr_reader.readtext(frame)
                        for (bbox, text, conf) in ocr_results:
                            if conf > 0.5:
                                all_ocr_results.append({
                                    "text": text,
                                    "confidence": conf,
                                    "frame_index": frame_idx
                                })

                    # 人脸检测
                    if face_cascade is not None:
                        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                        for (x, y, w, h) in faces:
                            all_faces.append({
                                "confidence": 0.8,
                                "position": f"({x},{y},{w},{h})",
                                "frame_index": frame_idx
                            })

            cap.release()

            # 整理结果
            if all_ocr_results:
                result["ocr_result"] = [item["text"] for item in all_ocr_results]
                result["ocr_details"] = all_ocr_results
            else:
                result["ocr_result"] = ["未检测到文字内容"]

            result["face_detection"] = all_faces
            result["asr_result"] = [f"视频时长: {duration:.2f}秒", "使用OpenCV基础分析"]

    except Exception as e:
        print(f"视频分析出错: {str(e)}")
        result["ocr_result"] = [f"视频分析失败: {str(e)}"]

    return result

async def analyze_image(file_path, result):
    """图片AI分析"""
    print(f"🖼️  开始分析图片: {file_path}")

    if not CV2_AVAILABLE:
        result["ocr_result"] = ["图片分析需要OpenCV库"]
        return result

    try:
        # 读取图片
        image = cv2.imread(file_path)
        if image is None:
            result["ocr_result"] = ["图片读取失败"]
            return result

        height, width = image.shape[:2]
        result["file_info"]["dimensions"] = f"{width}x{height}"

        # OCR分析
        if ocr_reader and EASYOCR_AVAILABLE:
            ocr_results = ocr_reader.readtext(image)
            ocr_texts = []
            ocr_details = []

            for (bbox, text, conf) in ocr_results:
                if conf > 0.5:  # 置信度阈值
                    ocr_texts.append(text)
                    ocr_details.append({
                        "text": text,
                        "confidence": conf,
                        "bbox": bbox
                    })

            if ocr_texts:
                result["ocr_result"] = ocr_texts
                result["ocr_details"] = ocr_details
            else:
                result["ocr_result"] = ["未检测到文字内容"]
        else:
            result["ocr_result"] = ["OCR功能不可用"]

        # 人脸检测
        if face_cascade is not None and CV2_AVAILABLE:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            face_results = []
            for (x, y, w, h) in faces:
                face_results.append({
                    "confidence": 0.8,  # 估计置信度
                    "position": f"({x},{y},{w},{h})",
                    "size": f"{w}x{h}"
                })

            result["face_detection"] = face_results
        else:
            result["face_detection"] = []

        # 二维码检测
        if CV2_AVAILABLE:
            qr_detector = cv2.QRCodeDetector()
            data, bbox, _ = qr_detector.detectAndDecode(image)
            if data:
                result["qr_code_result"] = [{"content": data, "detected": True}]
            else:
                result["qr_code_result"] = []
        else:
            result["qr_code_result"] = []

    except Exception as e:
        print(f"图片分析出错: {str(e)}")
        result["ocr_result"] = [f"图片分析失败: {str(e)}"]

    return result

def basic_file_analysis(filename, content_type, file_size, result):
    """基础文件分析"""
    if content_type:
        if "pdf" in content_type:
            result["ocr_result"] = [f"PDF文档: {filename}", "需要PDF解析工具进行文字提取"]
        elif "text" in content_type:
            result["ocr_result"] = [f"文本文件: {filename}"]
        else:
            result["ocr_result"] = [f"文件类型: {content_type}"]

    return result

def evaluate_risk(result, filename):
    """风险评估"""
    risk_score = 0
    risk_factors = []

    # 基于文件名的风险评估
    high_risk_keywords = ["贷款", "借贷", "投资", "理财", "股票", "基金", "保险", "融资"]
    medium_risk_keywords = ["宣传", "广告", "营销", "推广", "产品"]

    filename_lower = filename.lower()

    for keyword in high_risk_keywords:
        if keyword in filename_lower:
            risk_score += 3
            risk_factors.append(f"文件名包含高风险关键词: {keyword}")

    for keyword in medium_risk_keywords:
        if keyword in filename_lower:
            risk_score += 1
            risk_factors.append(f"文件名包含营销关键词: {keyword}")

    # 基于OCR结果的风险评估
    if result.get("ocr_result"):
        ocr_text = " ".join(result["ocr_result"]).lower()

        for keyword in high_risk_keywords:
            if keyword in ocr_text:
                risk_score += 2
                risk_factors.append(f"内容包含金融关键词: {keyword}")

        # 检查是否包含联系方式
        if any(char.isdigit() for char in ocr_text) and ("电话" in ocr_text or "微信" in ocr_text or "qq" in ocr_text):
            risk_score += 1
            risk_factors.append("检测到联系方式信息")

    # 基于人脸检测的风险评估
    if result.get("face_detection") and len(result["face_detection"]) > 0:
        risk_score += 1
        risk_factors.append(f"检测到{len(result['face_detection'])}个人脸")

    # 确定风险等级
    if risk_score >= 5:
        result["risk_evaluation"] = "high"
        result["compliance_check"] = False
    elif risk_score >= 2:
        result["risk_evaluation"] = "medium"
        result["compliance_check"] = True
    else:
        result["risk_evaluation"] = "low"
        result["compliance_check"] = True

    result["risk_score"] = risk_score
    result["risk_factors"] = risk_factors

    return result

@app.get("/status/{trace_id}")
async def get_status(trace_id: str):
    """查询审核状态"""
    if trace_id in upload_records:
        return upload_records[trace_id]
    else:
        raise HTTPException(status_code=404, detail="记录未找到")

@app.get("/records")
async def get_all_records():
    """获取所有审核记录"""
    return {"records": list(upload_records.values())}

if __name__ == "__main__":
    print("=" * 50)
    print("启动AI增强版视频审核服务")
    print("=" * 50)

    # 初始化AI模型
    ai_success = init_ai_models()

    if ai_success:
        print("🚀 服务启动模式: AI增强分析")
        print("📝 OCR: EasyOCR (中英文)")
        print("👤 人脸检测: OpenCV Haar Cascade")
        print("🎬 视频处理: MoviePy + OpenCV")
        print("📊 二维码检测: OpenCV QRCodeDetector")
    else:
        print("🚀 服务启动模式: 基础分析（AI模型加载失败）")

    print("服务地址: http://localhost:8080")
    print("API文档: http://localhost:8080/docs")
    print("健康检查: http://localhost:8080/health")
    print("Demo页面: http://localhost:8080/demo")
    print("=" * 50)

    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
