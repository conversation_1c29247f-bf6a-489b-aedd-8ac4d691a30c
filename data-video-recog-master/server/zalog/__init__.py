# -*- coding: utf-8 -*-

# 日志模块需要关注wiki是否有改动
# 日志接入参考地址 http://wiki.zhonganonline.com/pages/viewpage.action?pageId=36052827
#
# 模型开发人员日志记录用app_logger
# 使用方法如下
# from config import get_config
# configs = get_config()
# app_logger = get_app_logger(configs)
# app_logger.info("msg")
# app_logger.error("msg")


import copy
import json
import logging
import logging.config
import os
import re
import socket
import string
import time
from datetime import datetime

logger_configured = False

app_logger_name = "APP_LOGGER"
biz_logger_name = "BIZ_LOGGER"

app_logger = None
biz_logger = None

log_dir = "/alidata1/admin/{}/logs"
info_log_filename_template = string.Template("${hostname}_app_${app_name}_lt_info.log")
error_log_filename_template = string.Template(
    "${hostname}_app_${app_name}_lt_error.log"
)
biz_log_filename_template = string.Template(
    "${hostname}-ss_micro_app_${app_name}_lt_biz.log"
)


class CustomInfoFilter(logging.Filter):
    def filter(self, record: logging.LogRecord):
        # print(record.levelno)
        return record.levelno < logging.WARN


class CustomErrorFilter(logging.Filter):
    def filter(self, record: logging.LogRecord):
        # print(record.levelno)
        return record.levelno > logging.INFO


LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": True,
    "formatters": {
        "app_log_format": {
            # 时间格式 yyyy-MM-dd HH:mm:ss,SSS
            "format": "%(asctime)s [%(thread)d] %(levelname)s [%(module)s] [%(filename)s:%(lineno)s] [trace=,span=,parent=] - %(message)s",
        },
        "biz_log_format": {
            # 实际输出内容格式为json 包含time字段 格式为 "2017-03-19T12:41:29.590+08:00"
            "format": "%(message)s",
        },
    },
    "filters": {
        "info_filter": {
            "()": CustomInfoFilter,
        },
        "error_filter": {
            "()": CustomErrorFilter,
        },
    },
    "handlers": {
        "app_log_console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "app_log_format",
        },
        "biz_log_console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "biz_log_format",
        },
        "app_info_log_file": {
            "class": "logging.handlers.TimedRotatingFileHandler",
            # 需要修改
            "filename": "./log.txt",
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "level": "DEBUG",
            "filters": ["info_filter"],
            "formatter": "app_log_format",
        },
        "app_error_log_file": {
            "class": "logging.handlers.TimedRotatingFileHandler",
            # 需要修改
            "filename": "./log.txt",
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "level": "WARNING",
            "filters": ["error_filter"],
            "formatter": "app_log_format",
        },
        "biz_info_log_file": {
            "class": "logging.handlers.TimedRotatingFileHandler",
            # 需要修改
            "filename": "./log.txt",
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "level": "INFO",
            "filters": ["info_filter"],
            "formatter": "biz_log_format",
        },
    },
    "loggers": {
        app_logger_name: {
            "handlers": ["app_log_console", "app_info_log_file", "app_error_log_file"],
            "level": "DEBUG",
            "propagate": False,
        },
        biz_logger_name: {
            "handlers": ["biz_log_console", "biz_info_log_file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}


def check_app_name(app_name):
    if not re.match("^[a-zA-Z-]+$", app_name):
        return False
    return True


def init_srv_logging(defualt_log_dir, app_name):
    print("start config service log")
    assert len(app_name) > 0, "app_name cannot be empty"
    assert check_app_name(app_name), "app_name cannot contain special characters"
    hostname = socket.gethostname()
    # 开发环境使用本地目录，线上环境使用六翼约定的目录
    if os.getenv("DEPLOY_ENV", "local") == "local":
        log_dir_path = defualt_log_dir
    else:
        log_dir_path = log_dir.format(app_name)
    if not os.path.exists(log_dir_path):
        os.makedirs(log_dir_path)

    change_conf = {"app_name": app_name, "hostname": hostname}

    info_log_path = os.path.join(
        log_dir_path, info_log_filename_template.substitute(change_conf)
    )
    error_log_path = os.path.join(
        log_dir_path, error_log_filename_template.substitute(change_conf)
    )
    biz_log_path = os.path.join(
        log_dir_path, biz_log_filename_template.substitute(change_conf)
    )
    print(info_log_path)
    print(error_log_path)
    print(biz_log_path)

    global LOGGING_CONFIG

    LOGGING_CONFIG["handlers"]["app_info_log_file"]["filename"] = info_log_path
    LOGGING_CONFIG["handlers"]["app_error_log_file"]["filename"] = error_log_path
    LOGGING_CONFIG["handlers"]["biz_info_log_file"]["filename"] = biz_log_path

    logging.config.dictConfig(LOGGING_CONFIG)


# app log 用来记录 info, warining, error日志
def get_app_logger(config):
    global app_logger
    if app_logger:
        return app_logger
    global logger_configured
    if not logger_configured:
        assert "default_log_dir" in config, "defualt_log_dir not exists in config"
        assert "app_name" in config, "app_name not exists in config"
        init_srv_logging(config["default_log_dir"], config["app_name"])
        logger_configured = True
    app_logger = logging.getLogger(app_logger_name)
    return app_logger


# biz log只用来记录info级别的业务日志
def get_biz_logger(config):
    global biz_logger
    if biz_logger:
        return biz_logger
    global logger_configured
    if not logger_configured:
        assert "default_log_dir" in config, "defualt_log_dir not exists in config"
        assert "app_name" in config, "app_name not exists in config"
        init_srv_logging(config["default_log_dir"], config["app_name"])
        logger_configured = True
    biz_logger = ZABizLogger()
    return biz_logger


class ZABizLogger(logging.Logger):
    def __init__(self):
        self.logger = logging.getLogger(biz_logger_name)

    # biz log中只能包含业务指标，不包含其他信息
    def check_input(func):
        def wrapper(self, content):
            assert isinstance(content, dict), "biz log content must be dict"
            assert "ip" not in content
            assert "appName" not in content
            assert "hostName" not in content
            assert "agentType" not in content
            assert "source" not in content
            new_content = copy.deepcopy(content)
            new_content["time"] = ZABizLogger._get_now_time_str()
            msg = json.dumps(new_content, ensure_ascii=False)
            func(self, msg)

        return wrapper

    @check_input
    def debug(self, msg):
        self.logger.debug(msg)

    @check_input
    def info(self, msg):
        self.logger.info(msg)

    @check_input
    def warn(self, msg):
        self.logger.warn(msg)

    @check_input
    def error(self, msg):
        self.logger.error(msg)

    @check_input
    def critical(self, msg):
        self.logger.critical(msg)

    @staticmethod
    def _get_now_time_str():
        s = datetime.now().isoformat()[:-3]
        symbol = "+"
        offset = -time.timezone / 3600
        if offset < 0:
            symbol = "-"
        offset = abs(offset)
        offset_str = "{:02n}:00".format(offset)
        return "{}{}{}".format(s, symbol, offset_str)

