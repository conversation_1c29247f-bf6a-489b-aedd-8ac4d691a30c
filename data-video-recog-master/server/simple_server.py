#!/usr/bin/env python3
import time
import json
import tempfile
import os
import hashlib
import mimetypes
from datetime import datetime
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
import uvicorn

# 简化版本的视频审核服务
app = FastAPI(title="视频审核服务 - 简化版")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据存储
upload_records = {}

def get_now_time_ms():
    return int(time.time() * 1000)

def generate_trace_id():
    return f"trace_{get_now_time_ms()}_{hash(str(datetime.now()))}"

@app.get("/")
async def root():
    return {"message": "视频审核服务已启动", "version": "simple", "time": datetime.now()}

@app.get("/health")
async def health():
    return {"status": "ok", "service": "video-audit-simple", "ai_enabled": False}

@app.get("/demo")
async def demo_page():
    """返回Demo页面"""
    demo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Demo.html")
    if os.path.exists(demo_path):
        return FileResponse(demo_path)
    else:
        raise HTTPException(status_code=404, detail="Demo页面未找到")

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传接口"""
    try:
        # 生成追踪ID
        trace_id = generate_trace_id()
        
        # 读取文件内容
        content = await file.read()
        file_hash = hashlib.md5(content).hexdigest()
        
        # 保存临时文件进行分析
        temp_file_path = None
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # 基础文件分析
            audit_result = analyze_file(temp_file_path, file.filename, file.content_type, len(content))
            
        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
        # 保存记录
        record = {
            "trace_id": trace_id,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": len(content),
            "md5": file_hash,
            "upload_time": datetime.now().isoformat(),
            "audit_result": audit_result,
            "status": "completed"
        }
        
        upload_records[trace_id] = record
        
        return {
            "success": True,
            "trace_id": trace_id,
            "filename": file.filename,
            "size": len(content),
            "audit_result": audit_result,
            "message": "文件上传并审核完成"
        }
        
    except Exception as e:
        print(f"审核过程出错: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

def analyze_file(file_path, filename, content_type, file_size):
    """基础文件分析"""
    result = {
        "ocr_result": [],
        "face_detection": [],
        "asr_result": [],
        "qr_code_result": [],
        "risk_evaluation": "low",
        "compliance_check": True,
        "audit_time": datetime.now().isoformat(),
        "analysis_method": "basic_file_analysis",
        "file_info": {
            "filename": filename,
            "content_type": content_type,
            "file_size": file_size,
            "file_path": file_path
        }
    }
    
    # 基于文件类型的分析
    if content_type:
        if content_type.startswith("video/"):
            result["ocr_result"] = [f"视频文件已接收: {filename}"]
            result["asr_result"] = ["视频文件需要语音识别处理"]
            result["face_detection"] = ["视频文件需要人脸检测处理"]
        elif content_type.startswith("image/"):
            result["ocr_result"] = [f"图片文件已接收: {filename}"]
            result["face_detection"] = ["图片文件需要人脸检测处理"]
        elif "pdf" in content_type:
            result["ocr_result"] = [f"PDF文档已接收: {filename}"]
    
    # 基于文件名的风险评估
    risk_keywords = ["贷款", "投资", "理财", "股票", "基金", "保险", "借贷", "融资"]
    medium_risk_keywords = ["宣传", "广告", "营销", "推广"]
    
    filename_lower = filename.lower()
    
    if any(keyword in filename_lower for keyword in risk_keywords):
        result["risk_evaluation"] = "medium"
        result["compliance_check"] = True
        result["ocr_result"].append(f"检测到金融相关关键词，建议人工复审")
    elif any(keyword in filename_lower for keyword in medium_risk_keywords):
        result["risk_evaluation"] = "low"
        result["compliance_check"] = True
        result["ocr_result"].append(f"检测到营销相关内容")
    
    # 文件大小检查
    if file_size > 100 * 1024 * 1024:  # 100MB
        result["ocr_result"].append("文件较大，建议压缩后重新上传")
    
    # 模拟一些基础检测结果
    if content_type and content_type.startswith("video/"):
        result["asr_result"] = ["模拟语音识别: 欢迎了解我们的产品和服务"]
        result["face_detection"] = [{"confidence": 0.85, "position": "center", "note": "检测到人脸区域"}]
    
    return result

@app.get("/status/{trace_id}")
async def get_status(trace_id: str):
    """查询审核状态"""
    if trace_id in upload_records:
        return upload_records[trace_id]
    else:
        raise HTTPException(status_code=404, detail="记录未找到")

@app.get("/records")
async def get_all_records():
    """获取所有审核记录"""
    return {"records": list(upload_records.values())}

if __name__ == "__main__":
    print("=" * 50)
    print("启动简化版视频审核服务")
    print("🚀 服务启动模式: 基础文件分析")
    print("服务地址: http://localhost:8080")
    print("API文档: http://localhost:8080/docs")
    print("健康检查: http://localhost:8080/health")
    print("Demo页面: http://localhost:8080/demo")
    print("=" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
