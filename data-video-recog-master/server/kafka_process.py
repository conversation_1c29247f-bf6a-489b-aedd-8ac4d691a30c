import time
from confluent_kafka import Consumer
from algorithm.video_Ai_auditor import <PERSON><PERSON>i<PERSON><PERSON><PERSON>
from config import get_config
from server.kafka_producer import KafkaProducer
from server.schemas import InputModel
from server.zalog import get_app_logger, get_biz_logger
from http.server import HTTPServer
from server.http_request import HealthRe<PERSON>
from threading import Thread

from server.request_cache import Request<PERSON>ache
from server.asr_loop import Asr<PERSON>oop
from server.response_loop import Response<PERSON>oop
from server.mysql_tool import MysqlTool

from algorithm.basic_model_module.risk_evaluation_module.fasttext_classifier import FasttextClassifier
configs = get_config()
app_logger = get_app_logger(configs)
biz_logger = get_biz_logger(configs)

video_ai_auditor = VideoAiAuditor(configs["model_config"])

reserved_keys = {
    "TRACEID": "srvLogTraceId",
    "REQSTART": "reqStartTime",
    "RESSTART": "respStartTime",
    "RESEND": "respEndTime"
}

def get_now_time_ms():
    return int(time.time() * 1000)


def set_request_biz_log(srv_trace_id, req_start_time, resp_start_time, request):
    req_log = dict()
    req_log["log_type"] = "request"
    req_log["request"] = request
    # request biz log 记录srvLogTraceId, reqStartTime(请求开始时间), resStartTime(处理开始时间)
    req_log[reserved_keys["TRACEID"]] = srv_trace_id
    req_log[reserved_keys["REQSTART"]] = req_start_time
    req_log[reserved_keys["RESSTART"]] = resp_start_time
    biz_logger.info(req_log)
    return req_log


def set_response_biz_log(srv_trace_id, req_start_time, resp_start_time, resp_end_time, response):
    resp_log = dict()
    resp_log["log_type"] = "response"
    resp_log["success"] = response["success"]
    resp_log["errcode"] = response["errcode"]
    resp_log["message"] = response["message"]
    resp_log["detail"] = response["detail"]
    # response biz log 记录srvLogTraceId, reqStartTime(请求开始时间), resStartTime(处理开始时间),respEndTime(处理结束时间)，costTime(耗时)
    resp_log[reserved_keys["TRACEID"]] = srv_trace_id
    resp_log[reserved_keys["REQSTART"]] = req_start_time
    resp_log[reserved_keys["RESSTART"]] = resp_start_time
    resp_log[reserved_keys["RESEND"]] = resp_end_time
    resp_log["costTime"] = resp_end_time - resp_start_time
    biz_logger.info(resp_log)
    return resp_log


if __name__ == "__main__":
    app_logger.info(configs)

    # kafka连接
    read_topic = configs['kafka']["read_topic"]
    write_topic = configs['kafka']["write_topic"]
    kafka_servers = configs['kafka']["kafka_servers"]

    consumer = Consumer({
        "bootstrap.servers": kafka_servers,
        "auto.offset.reset": "latest",
        "group.id": configs["app_name"],
        "enable.auto.commit": False
    })
    consumer.subscribe([read_topic])
    producer = KafkaProducer(servers=kafka_servers, topic=write_topic)

    request_cache_db = MysqlTool(configs["mysql"]["request_cache"])
    asr_cache_db = MysqlTool(configs["mysql"]["asr_ache"])

    # 子线程1-健康检查服务
    host = ("0.0.0.0", 8080)
    server = HTTPServer(host, HealthRequest)
    Thread(target=server.serve_forever, args=(1,)).start()

    # 子线程2-接收kafka信息并存入mysql
    request_cache_tool = RequestCache(request_cache_db, consumer, producer)
    Thread(target=request_cache_tool.process).start()

    # 子线程3-完成ASR任务并落库
    asr_process_tool = AsrLoop(request_cache_db, asr_cache_db, consumer, producer, video_ai_auditor)
    Thread(target=asr_process_tool.process).start()

    # 循环的主线程-完成ocr等任务
    response_loop_tool = ResponseLoop(request_cache_db, asr_cache_db, consumer, producer, video_ai_auditor)
    response_loop_tool.kafka_loop()
