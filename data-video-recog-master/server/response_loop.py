#!/usr/bin/env python
"""
@File:      response_loop
@Author:    sunjie
@Time:      2024/5/15 11:41
Describe:   服务层入口，从mysql中拿取数据
"""
import json
from datetime import datetime
import time
import tempfile
import requests
import traceback
import cv2
import os
import uuid

from server import StatusType
from server import reserved_keys, get_now_time_ms
from algorithm.utils import try_except
from config import get_config
from server.zalog import get_app_logger, get_biz_logger
from algorithm.utils.util import npImage, read_img_PIL, download_url



configs = get_config()
app_logger = get_app_logger(configs)
biz_logger = get_biz_logger(configs)


def set_request_biz_log(srv_trace_id, req_start_time, resp_start_time, request):
    req_log = dict()
    req_log["log_type"] = "request"
    req_log["request"] = json.dumps(request)
    # request biz log 记录srvLogTraceId, reqStartTime(请求开始时间), resStartTime(处理开始时间)
    req_log[reserved_keys["TRACEID"]] = srv_trace_id
    req_log[reserved_keys["REQSTART"]] = req_start_time
    req_log[reserved_keys["RESSTART"]] = resp_start_time
    biz_logger.info(req_log)
    return req_log


def set_response_biz_log(srv_trace_id, req_start_time, resp_start_time, resp_end_time, response):
    resp_log = dict()
    resp_log["log_type"] = "response"
    resp_log["success"] = response["success"]
    resp_log["errcode"] = response["errcode"]
    resp_log["message"] = response["message"]
    resp_log["detail"] = json.dumps(response["detail"])
    # response biz log 记录srvLogTraceId, reqStartTime(请求开始时间), resStartTime(处理开始时间),respEndTime(处理结束时间)，costTime(耗时)
    resp_log[reserved_keys["TRACEID"]] = srv_trace_id
    resp_log[reserved_keys["REQSTART"]] = req_start_time
    resp_log[reserved_keys["RESSTART"]] = resp_start_time
    resp_log[reserved_keys["RESEND"]] = resp_end_time
    resp_log["costTime"] = resp_end_time - resp_start_time
    biz_logger.info(resp_log)
    return resp_log


def img_download(req):
    data_url = req["data"]["data_info"].get("url")
    data_type = req["data"]["data_info"].get("data_type")

    if data_type == 1:  # 视频输入
        # encoded_url = quote(data_url, safe="/:")    # 以防有中文字导致报错
        video_obj = None
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_filename = "tmp_{}.mp4".format(str(uuid.uuid4()))
            tmp_save_path = os.path.join(tmpdir, tmp_filename)
            flag = download_url(data_url, tmp_save_path)
            assert flag
            video_obj = cv2.VideoCapture(tmp_save_path)

        req["data"]["data_info"]["data_obj"] = video_obj
    elif data_type == 2:    # 图片输入
        res = requests.get(data_url, timeout=10)
        image = res.content
        npimage = npImage(image)

        if npimage is None:     # 防止cv2读取为None
            npimage = read_img_PIL(image)

        req["data"]["data_info"]["data_obj"] = npimage
    return req



class ResponseLoop:
    def __init__(self, request_cache_db, asr_cache_db, consumer, producer, video_ai_auditor):
        self.request_cache_db = request_cache_db
        self.asr_cache_db = asr_cache_db
        self.consumer = consumer
        self.producer = producer
        self.algorithm_agent = video_ai_auditor

    def process_req(self, srvLogTraceId: str, req: dict, asr_list: list):
        res = self.algorithm_agent.process(req)
        return_result = res.get("result")
        errcode = res.get("errcode")
        message = res.get("message")
        detail = res.get("detail")

        # 20241119 添加王吉地的视频风险评估模型，入参是ocr和asr文字
        risk_flag_dict = self.algorithm_agent.process_risk_evaluation(req, asr_list, res)
        return_result["data_info"]["risk_flag"] = risk_flag_dict

        # 添加asr的结果
        return_result["ASR_list"] = asr_list

        success = True if errcode == 0 else False
        ret = {
            "success": success,
            "errcode": errcode,
            "message": message,
            "detail": detail,
            "srvLogTraceId": req.get("srvLogTraceId"),
            "reserved_info": req.get("reserved_info"),
            "result": return_result
        }
        return ret

    def get_asr_history(self, request):
        # 检查数据库中历史上做好的结果
        table_name = self.asr_cache_db.config["table"]
        sql_query = "SELECT video_id, asr_value FROM `{}` WHERE video_md5 = %s limit 1".format(table_name)
        video_md5 = request["data"]["data_info"]["md5"]
        request_cache = self.request_cache_db.query_line(sql_query, (video_md5))
        asr_res_list = json.loads(request_cache[0][1])      # 默认成功，万一失败在外层做兜底。
        return asr_res_list

    def kafka_loop(self):
        while True:
            time.sleep(0.5)
            try:
                try:
                    # 查数据库确定没有积压的视频审核ASR任务
                    table_name = self.request_cache_db.config["table"]
                    creator_value = "magnetaudit"
                    sql_query = "SELECT trace_id, request, gmt_created, status, gmt_modified  FROM `{}` WHERE status in ({}, {}) AND creator = '{}' limit 1".format(
                        table_name, StatusType.succeed_asr.value, StatusType.failed_asr.value, creator_value)
                    request_cache = self.request_cache_db.query_line(sql_query, ())
                    # 如果没有已完成asr的请求堆积，则休息
                    if len(request_cache) == 0:
                        time.sleep(self.request_cache_db.config["query_interval"])
                        continue

                    # 如有，则取值
                    this_msg = request_cache[0]  # 取时间最早的
                    srvLogTraceId = this_msg[0]
                    req = json.loads(this_msg[1])
                    reqStartTime = this_msg[2].timestamp()
                    task_status = this_msg[3]
                    gmt_modified = this_msg[4]      # 用于核对任务状态

                    # 记录处理状态
                    resp_start_time = datetime.now()
                    sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s AND gmt_modified = %s".format(
                        table_name)
                    status = StatusType.processing_ocr.value        # 正在处理ocr
                    sql_data_tuple = (status, resp_start_time, srvLogTraceId, gmt_modified)
                    res = self.request_cache_db.update_line(sql_update_line, sql_data_tuple)
                    if res ==0:
                        continue    # 该信息已经被其他线程处理，无需继续做
                    resp_start_time = resp_start_time.timestamp()

                    # 开始处理
                    set_request_biz_log(srvLogTraceId, reqStartTime, resp_start_time, req)
                    with tempfile.TemporaryDirectory() as tmpdir:
                        # 下载数据
                        try:
                            req = img_download(req)
                        except Exception as e:
                            ret = {
                                reserved_keys["TRACEID"]: srvLogTraceId,
                                "success": False,
                                "errcode": 500,
                                "message": "Failed to download the video.",
                                "detail": str(e)
                            }
                            self.producer.send(ret)

                            # 记录失败结果
                            update_time = datetime.now()
                            sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s".format(
                                table_name)
                            status = StatusType.failed_ocr.value
                            sql_data_tuple = (status, update_time, srvLogTraceId)  # 修改状态，处理出错
                            self.request_cache_db.update_line(sql_update_line, sql_data_tuple)
                            continue

                        # 查找asr结果，如果失败了就给空值
                        try:
                            app_logger.info("traceId: {}, ASR_status is {}.".format(srvLogTraceId, task_status))
                            if task_status == StatusType.succeed_asr.value:
                                # 取出历史asr结果
                                asr_list = self.get_asr_history(req)
                            else:
                                asr_list = []
                        except Exception as e:
                            asr_list = []
                            app_logger.error("traceId: {}, failed to fetch ASR history, detail Exception: {} {}".format(
                                    srvLogTraceId, req, str(e), traceback.format_exc()))

                        # 算法层处理
                        res = self.process_req(srvLogTraceId, req, asr_list)

                        resp_end_time = datetime.now().timestamp()
                        biz_resp_log = set_response_biz_log(srvLogTraceId, reqStartTime,
                                                            resp_start_time, resp_end_time, res)
                        del biz_resp_log["log_type"]
                        ret = res

                    # 返回到kafka
                    self.producer.send(ret)
                    # mysql记录处理状态
                    update_time = datetime.now()
                    if ret['errcode'] == 0:
                        status_flag = StatusType.succeed_ocr.value
                    else:
                        status_flag = StatusType.failed_ocr.value
                    sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s".format(
                        table_name)
                    sql_data_tuple = (status_flag, update_time, srvLogTraceId)  # 修改状态，完成处理
                    self.request_cache_db.update_line(sql_update_line, sql_data_tuple)

                except Exception as e:
                    app_logger.error("traceId: {}, Unexpected Exception, input: {}, detail Exception: {} {}".format(
                        srvLogTraceId, req, str(e), traceback.format_exc()))
                    ret = {
                        "success": False,
                        "errcode": 500,
                        reserved_keys["TRACEID"]: srvLogTraceId,
                        reserved_keys["REQSTART"]: reqStartTime,
                        reserved_keys["RESSTART"]: resp_start_time,
                        "message": "internal ocr process failed",
                        "detail": str(e)
                    }
                    self.producer.send(ret)

                    # mysql记录失败状态
                    update_time = datetime.now()
                    sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s".format(
                        table_name)
                    status = StatusType.failed_ocr.value
                    sql_data_tuple = (status, update_time, srvLogTraceId)  # 修改状态，处理出错
                    self.request_cache_db.update_line(sql_update_line, sql_data_tuple)
            except Exception as e:
                app_logger.error(
                    "ERROR occurs in kafak_ocr_loop(), detail Exception: {} {}".format(str(e), traceback.format_exc()))
