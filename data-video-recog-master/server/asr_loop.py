#!/usr/bin/env python
"""
@File:      asr_loop
@Author:    sunjie
@Time:      2024/8/5 18:23
Describe:   
"""
import json
from datetime import datetime
import time
import tempfile
import os
import requests
import traceback

from server import reserved_keys, get_now_time_ms
from algorithm.utils import try_except
from config import get_config
from server.zalog import get_app_logger, get_biz_logger
from server import StatusType
from urllib.parse import quote

configs = get_config()
app_logger = get_app_logger(configs)
biz_logger = get_biz_logger(configs)


def set_request_biz_log(srv_trace_id, req_start_time, resp_start_time, request):
    req_log = dict()
    req_log["log_type"] = "ASR"
    req_log["request"] = json.dumps(request)
    # request biz log 记录srvLogTraceId, reqStartTime(请求开始时间), resStartTime(处理开始时间)
    req_log[reserved_keys["TRACEID"]] = srv_trace_id
    req_log[reserved_keys["REQSTART"]] = req_start_time
    req_log[reserved_keys["RESSTART"]] = resp_start_time
    biz_logger.info(req_log)
    return req_log


def set_response_biz_log(srv_trace_id, req_start_time, resp_start_time, resp_end_time, response):
    resp_log = dict()
    resp_log["log_type"] = "ASR"
    resp_log["success"] = response["success"]
    resp_log["errcode"] = response["errcode"]
    resp_log["message"] = response["message"]
    resp_log["detail"] = json.dumps(response["detail"])
    # response biz log 记录srvLogTraceId, reqStartTime(请求开始时间), resStartTime(处理开始时间),respEndTime(处理结束时间)，costTime(耗时)
    resp_log[reserved_keys["TRACEID"]] = srv_trace_id
    resp_log[reserved_keys["REQSTART"]] = req_start_time
    resp_log[reserved_keys["RESSTART"]] = resp_start_time
    resp_log[reserved_keys["RESEND"]] = resp_end_time
    resp_log["costTime"] = resp_end_time - resp_start_time
    biz_logger.info(resp_log)
    return resp_log


def img_download(req, tmpdir):
    # 下载每张案例图片，并求其md5码、exif信息
    data_url = req["data"]["data_info"].get("url")
    data_type = req["data"]["data_info"].get("data_type")

    if data_type == 1:  # 视频输入
        encoded_url = quote(data_url, safe="/:")  # 以防有中文字导致报错
        download_file_path = os.path.join(tmpdir, "{}.mp4".format(req["data"]["data_info"]["id"]))
        res = requests.get(encoded_url, verify=False)
        with open(download_file_path, "wb") as f:
            f.write(res.content)
        req["data"]["data_info"]["file_path"] = download_file_path

    elif data_type == 2:  # 图片输入
        pass  # 图片不需要ASR
    return req


class AsrLoop:
    def __init__(self, request_cache_db, asr_cache_db, consumer, producer, video_ai_auditor):
        self.request_cache_db = request_cache_db
        self.asr_cache_db = asr_cache_db
        self.consumer = consumer
        self.producer = producer
        self.algorithm_agent = video_ai_auditor

    def process_req(self, srvLogTraceId: str, req: dict):
        try_except.OCR_CASE_CONTEXT_ID = srvLogTraceId
        asr_res = self.algorithm_agent.process_asr(req)
        return asr_res

    def process(self):
        while True:
            time.sleep(1)
            try:
                try:
                    # 查数据库确定没有积压的视频审核未处理任务
                    table_name = self.request_cache_db.config["table"]
                    # sql_query = "SELECT trace_id, request, gmt_created  FROM `{}` WHERE status = %s AND creator = %s limit 1".format(table_name)
                    sql_query = "SELECT trace_id, request, gmt_created, gmt_modified FROM `{}` WHERE status = %s AND creator = %s order by gmt_created desc limit 1".format(table_name)
                    condition_value = StatusType.pending.value  # 等待处理
                    creator_value = "magnetaudit"
                    request_cache = self.request_cache_db.query_line(sql_query, (condition_value, creator_value))
                    # 如果没有请求堆积，则休息
                    if len(request_cache) == 0:
                        time.sleep(self.request_cache_db.config["query_interval"])
                        continue

                    # 如有，则取值
                    this_msg = request_cache[0]  # 取时间最早的
                    srvLogTraceId = this_msg[0]
                    req = json.loads(this_msg[1])
                    reqStartTime = this_msg[2].timestamp()
                    gmt_modified = this_msg[3]
                    # 记录处理状态

                    # 记录处理状态
                    resp_start_time = datetime.now()
                    # sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s".format(table_name)
                    sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s AND gmt_modified = %s".format(table_name)
                    sql_data_tuple = (StatusType.processing_asr.value, resp_start_time, srvLogTraceId, gmt_modified)  # 修改状态，asr正在处理
                    res = self.request_cache_db.update_line(sql_update_line, sql_data_tuple)
                    if res ==0:
                        continue    # 该信息已经被其他线程处理，无需继续做

                    resp_start_time = resp_start_time.timestamp()

                    # 检查数据库中是否已经做过，做过则不重新做
                    table_name = self.asr_cache_db.config["table"]
                    sql_query = "SELECT video_id, asr_value FROM `{}` WHERE video_md5 = %s limit 1".format(table_name)
                    video_md5 = req["data"]["data_info"]["md5"]
                    request_cache = self.request_cache_db.query_line(sql_query, (video_md5))

                    # asr库里还没有结果
                    if len(request_cache) == 0:
                        # asr库里还没有结果，开始做asr
                        # 开始处理
                        set_request_biz_log(srvLogTraceId, reqStartTime, resp_start_time, req)
                        with tempfile.TemporaryDirectory() as tmpdir:

                            # 下载
                            req = img_download(req, tmpdir)

                            # 算法层处理
                            asr_value, request_flag = self.process_req(srvLogTraceId, req)
                            app_logger.info("video_md5 = {}, asr server request_flag = {}.".format(video_md5, request_flag))
                            assert request_flag   # ASR请求返回异常
                            asr_value = json.dumps(asr_value)

                        # 保存ASR结果
                        video_id = req["data"]["data_info"]["id"]
                        table_name = self.asr_cache_db.config["table"]
                        # sql_update_line = "INSERT INTO  `{}` (video_md5,video_id,asr_value,creator,gmt_created) VALUES (%s,%s,%s,%s,%s);".format(table_name)
                        sql_update_line = "INSERT INTO  `{}` (video_md5,video_id,asr_value,creator,gmt_created) VALUES (%s,%s,%s,%s,%s) ON DUPLICATE KEY UPDATE asr_value=VALUES(asr_value) ;".format(
                            table_name)     # 如果有重复的，则更新asr_value内容，而不报错

                        update_time = datetime.now()  # 更新时间
                        creator = "magnetaudit"
                        sql_data_tuple = (video_md5, video_id, asr_value, creator, update_time)  # 保存asr结果
                        res = self.asr_cache_db.update_line(sql_update_line, sql_data_tuple)
                        app_logger.info("video_md5 = {}, asr_result update num = {}.".format(video_md5, res))

                    # 修改请求状态
                    table_name = self.request_cache_db.config["table"]
                    sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s;".format(
                        table_name)
                    status_flag = StatusType.succeed_asr.value  # 失败则13
                    update_time = datetime.now() # 更新时间
                    sql_data_tuple = (status_flag, update_time, srvLogTraceId)  # 修改状态，完成处理
                    self.request_cache_db.update_line(sql_update_line, sql_data_tuple)

                except Exception as e:
                    app_logger.error("ASR traceId: {}, Unexpected Exception, input: {}, detail Exception: {} {}".format(
                        srvLogTraceId, req, str(e), traceback.format_exc()))

                    # mysql记录失败状态
                    update_time = datetime.now()
                    table_name = self.request_cache_db.config["table"]
                    sql_update_line = "UPDATE `{}` SET status = %s, gmt_modified = %s  WHERE trace_id = %s".format(
                        table_name)
                    sql_data_tuple = (StatusType.failed_asr.value, update_time, srvLogTraceId)  # 修改状态，处理出错
                    self.request_cache_db.update_line(sql_update_line, sql_data_tuple)
            except Exception as e:
                app_logger.error(
                    "ERROR occurs in kafak_loop(), detail Exception: {} {}".format(str(e), traceback.format_exc()))
