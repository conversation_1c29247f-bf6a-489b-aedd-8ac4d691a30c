#!/usr/bin/env python
"""
@File:      request_cache
@Author:    sunjie
@Time:      2024/5/14 16:54
Describe:   将kafka收到的请求暂存到mysql表中
"""
from confluent_kafka import Consumer, KafkaError
from server.zalog import get_app_logger, get_biz_logger
from config import get_config
from server.schemas import InputModel
from pydantic import ValidationError
from server import reserved_keys, get_now_time_ms
from server import StatusType

import traceback
from typing import Optional
import time
import json
from datetime import datetime

configs = get_config()
app_logger = get_app_logger(configs)
biz_logger = get_biz_logger(configs)

def set_request_biz_log(srv_trace_id, req_start_time, request):
    req_log = dict()
    req_log["log_type"] = "cache"
    req_log["request"] = json.dumps(request)
    # request biz log 记录srvLogTraceId, reqStartTime(请求开始时间)
    req_log[reserved_keys["TRACEID"]] = srv_trace_id
    req_log[reserved_keys["REQSTART"]] = req_start_time
    biz_logger.info(req_log)
    return req_log


class RequestCache:
    def __init__(self, request_cache_db, consumer, producer):
        self.mysql_tool = request_cache_db
        self.consumer = consumer
        self.producer = producer
        self.config = request_cache_db.config

    def insert_data_to_mysql(self, req):
        # 检查入参是图片还是视频
        data_type = req["data"]["data_info"]["data_type"]
        if int(data_type) == 1:
            status = StatusType.pending.value  # 视频则进入待处理状态
        else:
            status = StatusType.failed_asr.value  # 照片则跳过待处理状态，直接进入asr完成状态

        table_name = self.config["table"]
        # sql_insert = "INSERT INTO `{}` (trace_id, request, status, creator, gmt_created, gmt_modified) VALUES " \
        #              "(%s,%s,%s,%s,%s,%s);".format(table_name)
        sql_insert = "INSERT INTO `{}` (trace_id, request, status, creator, gmt_created, gmt_modified) VALUES " \
                     "(%s,%s,%s,%s,%s,%s) ON DUPLICATE KEY UPDATE trace_id=trace_id ;".format(table_name)

        time_now = datetime.now()
        data_line = (req["srvLogTraceId"],
                     json.dumps(req),
                     status,  # 10:待处理    11:asr正在处理   12:asr处理完成   13:asr处理失败
                     "magnetaudit",
                     time_now,
                     time_now
                     )
        flag = self.mysql_tool.insert_line(sql_insert, data_line)
        return flag

    def process(self):
        while True:
            time.sleep(0.1)
            try:
                msg = self.consumer.poll(5.0)
                if msg is None:
                    time.sleep(1)
                    continue
                if msg.error() and msg.error() != KafkaError._PARTITION_EOF:
                    app_logger.error(
                        "kafka connect error: error occurred when pulling from kafka: {}".format(str(msg.error())))
                    continue
                elif msg.error() and msg.error() == KafkaError._PARTITION_EOF:
                    app_logger.error(
                        "kafka connect error: error occurred when pulling from kafka: kafka_partition_eof error")
                    continue

                resp_start_time = get_now_time_ms()
                ret: Optional[dict] = None
                req = dict()

                try:
                    recieve_msg = msg.value().decode('utf-8')
                    req = json.loads(recieve_msg)
                    input_data = InputModel(**req)
                except ValidationError as e:
                    app_logger.error("invalid data format: {}, {}".format(str(e), traceback.format_exc()))
                    ret = {
                        reserved_keys["TRACEID"]: input_data.srvLogTraceId,
                        "success": False,
                        "errcode": 400,
                        "message": "invalid data format",
                        "detail": str(e)
                    }
                    self.producer.send(ret)
                    self.consumer.commit(msg)
                    continue
                except json.JSONDecodeError as e:
                    app_logger.error("json decode failed: {}, {}".format(str(e), traceback.format_exc()))
                    ret = {
                        reserved_keys["TRACEID"]: input_data.srvLogTraceId,
                        "success": False,
                        "errcode": 500,
                        "message": "json decode failed",
                        "detail": str(e)
                    }
                    self.producer.send(ret)
                    self.consumer.commit(msg)
                    continue
                except Exception as e:
                    app_logger.error("Unexpected Exception: {}, {}".format(str(e), traceback.format_exc()))
                    ret = {
                        reserved_keys["TRACEID"]: input_data.srvLogTraceId,
                        "success": False,
                        "errcode": 500,
                        "message": "Unknow Exception when parsing input data",
                        "detail": str(e)
                    }
                    self.producer.send(ret)
                    self.consumer.commit(msg)
                    continue

                # 数据校验完成，开始存mysql数据库
                flag = self.insert_data_to_mysql(req)
                self.consumer.commit(msg)
                set_request_biz_log(input_data.srvLogTraceId, input_data.reqStartTime, req)

            except Exception as e:
                app_logger.error("traceId: {}, Unexpected Exception, input: {}, detail Exception: {} {}".format(
                    input_data.srvLogTraceId, recieve_msg, str(e), traceback.format_exc()))
                ret = {
                    "success": False,
                    "errcode": 500,
                    reserved_keys["TRACEID"]: input_data.srvLogTraceId,
                    reserved_keys["REQSTART"]: input_data.reqStartTime,
                    reserved_keys["RESSTART"]: resp_start_time,
                    "message": "internal process failed, or error in MySQL.",
                    "detail": str(e)
                }
                self.producer.send(ret)
                self.consumer.commit(msg)
        self.consumer.close()
