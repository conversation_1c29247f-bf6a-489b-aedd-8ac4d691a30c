from pydantic import BaseModel, Field
from config import get_config

configs = get_config()

class OcrInfo(BaseModel):
    type: int

class ImageModel(BaseModel):
    url: str
    image_no: str
    ocr_info: OcrInfo

class CaseInfo(BaseModel):
    id: str
    url: str
    data_type: int


class InputData(BaseModel):
    data_info: CaseInfo


class OptionalParam(BaseModel):
    time_interval: float = Field(default=configs["model_config"]["default_parse_config"]["default_param"]["time_interval"])
    begin_margin: float = Field(default=configs["model_config"]["default_parse_config"]["default_param"]["begin_margin"])
    end_margin: float = Field(default=configs["model_config"]["default_parse_config"]["default_param"]["end_margin"])


class InputModel(BaseModel):
    srvLogTraceId: str
    reqStartTime: float
    data: InputData
    optional_param: OptionalParam = Field(default_factory=OptionalParam)

if __name__ == "__main__":
    InputModel(**{"a":1})
