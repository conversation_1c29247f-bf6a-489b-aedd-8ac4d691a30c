#!/usr/bin/env python3
import time
import json
import tempfile
import shutil
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from datetime import datetime
import os
import hashlib
import sys
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 本地版本 - 不使用Kafka
app = FastAPI(title="视频审核服务 - 本地版")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据存储
upload_records = {}

# 全局变量存储AI审核器
ai_auditor = None

def init_ai_auditor():
    """初始化AI审核器"""
    global ai_auditor
    try:
        from config import get_config
        from algorithm.video_Ai_auditor import VideoAiAuditor

        configs = get_config()
        ai_auditor = VideoAiAuditor(configs["model_config"])
        print("✅ AI审核器初始化成功")
        return True
    except Exception as e:
        print(f"⚠️  AI审核器初始化失败，将使用基础分析: {str(e)}")
        ai_auditor = None
        return False

def get_now_time_ms():
    return int(time.time() * 1000)

def generate_trace_id():
    return f"trace_{get_now_time_ms()}_{hash(str(datetime.now()))}"

@app.get("/")
async def root():
    return {"message": "视频审核服务已启动", "version": "local", "time": datetime.now()}

@app.get("/health")
async def health():
    return {"status": "ok", "service": "video-audit-local", "ai_enabled": ai_auditor is not None}

@app.get("/demo")
async def demo_page():
    """返回Demo页面"""
    from fastapi.responses import FileResponse
    demo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Demo.html")
    if os.path.exists(demo_path):
        return FileResponse(demo_path)
    else:
        raise HTTPException(status_code=404, detail="Demo页面未找到")

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传接口"""
    try:
        # 生成追踪ID
        trace_id = generate_trace_id()

        # 读取文件内容
        content = await file.read()
        file_hash = hashlib.md5(content).hexdigest()

        # 保存临时文件
        temp_file_path = None
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name

            # 真实审核过程
            audit_result = await real_audit_process(temp_file_path, file.filename, file.content_type, len(content))

        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

        # 保存记录
        record = {
            "trace_id": trace_id,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": len(content),
            "md5": file_hash,
            "upload_time": datetime.now().isoformat(),
            "audit_result": audit_result,
            "status": "completed"
        }

        upload_records[trace_id] = record

        return {
            "success": True,
            "trace_id": trace_id,
            "filename": file.filename,
            "size": len(content),
            "audit_result": audit_result,
            "message": "文件上传并审核完成"
        }

    except Exception as e:
        print(f"审核过程出错: {str(e)}")
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

async def real_audit_process(file_path, filename, content_type, file_size):
    """真实审核逻辑"""
    try:
        result = {
            "ocr_result": [],
            "face_detection": [],
            "asr_result": [],
            "qr_code_result": [],
            "risk_evaluation": "low",
            "compliance_check": True,
            "audit_time": datetime.now().isoformat(),
            "file_info": {
                "filename": filename,
                "content_type": content_type,
                "file_size": file_size
            }
        }

        # 如果AI审核器可用，使用真实算法
        if ai_auditor:
            try:
                # 构造输入数据格式
                mock_input = {
                    "srvLogTraceId": f"audit_{int(time.time())}",
                    "reqStartTime": int(time.time()),
                    "reserved_info": {},
                    "data": {
                        "data_info": {
                            "id": f"file_{int(time.time())}",
                            "file_path": file_path,
                            "data_type": 1 if content_type and content_type.startswith("video/") else 2
                        }
                    },
                    "optional_param": {
                        "time_interval": 3.0,
                        "begin_margin": 1.0
                    }
                }

                # 调用真实AI审核
                ai_result = ai_auditor.process(mock_input)

                # 解析AI结果
                if ai_result and "data" in ai_result:
                    data = ai_result["data"]

                    # OCR结果
                    if "ocr_result" in data:
                        result["ocr_result"] = [item.get("text", "") for item in data["ocr_result"] if item.get("text")]

                    # 人脸检测结果
                    if "face_result" in data:
                        result["face_detection"] = data["face_result"]

                    # ASR结果
                    if "asr_result" in data:
                        result["asr_result"] = data["asr_result"]

                    # 二维码结果
                    if "qr_code_result" in data:
                        result["qr_code_result"] = data["qr_code_result"]

                    # 风险评估
                    if "risk_evaluation" in data:
                        result["risk_evaluation"] = data["risk_evaluation"]
                        result["compliance_check"] = data["risk_evaluation"] in ["low", "medium"]

            except Exception as e:
                print(f"AI审核出错，使用基础分析: {str(e)}")
                result = basic_file_analysis(file_path, filename, content_type, file_size)
        else:
            # 使用基础文件分析
            result = basic_file_analysis(file_path, filename, content_type, file_size)

        return result

    except Exception as e:
        print(f"审核过程出错: {str(e)}")
        return {
            "ocr_result": [],
            "face_detection": [],
            "asr_result": [],
            "qr_code_result": [],
            "risk_evaluation": "unknown",
            "compliance_check": False,
            "audit_time": datetime.now().isoformat(),
            "error": str(e)
        }

def basic_file_analysis(file_path, filename, content_type, file_size):
    """基础文件分析（不依赖AI模型）"""
    result = {
        "ocr_result": [],
        "face_detection": [],
        "asr_result": [],
        "qr_code_result": [],
        "risk_evaluation": "low",
        "compliance_check": True,
        "audit_time": datetime.now().isoformat(),
        "analysis_method": "basic_analysis"
    }

    # 基于文件名和类型的简单分析
    if content_type:
        if content_type.startswith("video/"):
            result["ocr_result"] = [f"视频文件: {filename}"]
            result["asr_result"] = ["需要语音识别处理"]
        elif content_type.startswith("image/"):
            result["ocr_result"] = [f"图片文件: {filename}"]
        elif "pdf" in content_type:
            result["ocr_result"] = [f"PDF文档: {filename}"]

    # 基于文件名的风险评估
    risk_keywords = ["贷款", "投资", "理财", "股票", "基金", "保险"]
    if any(keyword in filename for keyword in risk_keywords):
        result["risk_evaluation"] = "medium"
        result["compliance_check"] = True

    return result

@app.get("/status/{trace_id}")
async def get_status(trace_id: str):
    """查询审核状态"""
    if trace_id in upload_records:
        return upload_records[trace_id]
    else:
        raise HTTPException(status_code=404, detail="记录未找到")

@app.get("/records")
async def get_all_records():
    """获取所有审核记录"""
    return {"records": list(upload_records.values())}

if __name__ == "__main__":
    print("=" * 50)
    print("启动本地视频审核服务")
    print("=" * 50)

    # 初始化AI审核器
    print("正在初始化AI审核器...")
    ai_success = init_ai_auditor()

    if ai_success:
        print("🚀 服务启动模式: 完整AI审核")
    else:
        print("🚀 服务启动模式: 基础文件分析")

    print("服务地址: http://localhost:8080")
    print("API文档: http://localhost:8080/docs")
    print("健康检查: http://localhost:8080/health")
    print("Demo页面: http://localhost:8080/demo")
    print("=" * 50)

    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")