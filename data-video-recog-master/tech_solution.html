<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>素材审核Agent技术方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #4a5568;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .architecture-diagram {
            width: 100%;
            height: 600px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .flow-diagram {
            width: 100%;
            height: 400px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .feature-card h3 {
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .tech-item h4 {
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .tech-item ul {
            list-style: none;
            padding-left: 0;
        }
        
        .tech-item li {
            padding: 2px 0;
            color: #4a5568;
        }
        
        .tech-item li:before {
            content: "▸ ";
            color: #667eea;
            font-weight: bold;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .rule-schema {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .schema-group {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .schema-group h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.2em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 8px;
        }
        
        .schema-fields {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .field-item {
            background: #f7fafc;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #4299e1;
        }
        
        .field-name {
            font-weight: bold;
            color: #2b6cb0;
            font-size: 1.1em;
            margin-bottom: 5px;
        }
        
        .field-type {
            display: inline-block;
            background: #bee3f8;
            color: #2c5282;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .field-desc {
            color: #4a5568;
            line-height: 1.5;
            margin-bottom: 10px;
        }
        
        .field-subfields {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
        }
        
        .subfield-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .subfield-item:last-child {
            border-bottom: none;
        }
        
        .subfield-name {
            font-weight: bold;
            color: #553c9a;
            min-width: 140px;
            font-size: 0.9em;
        }
        
        .subfield-type {
            background: #e6fffa;
            color: #234e52;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            min-width: 80px;
            text-align: center;
            margin: 0 10px;
        }
        
        .subfield-desc {
            color: #718096;
            font-size: 0.9em;
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 素材审核Agent</h1>
            <p>基于大模型+工程规则的智能多模态内容审核系统</p>
        </div>

        <!-- 系统架构图 -->
        <div class="section">
            <h2>🏗️ 系统架构图</h2>
            <svg class="architecture-diagram" viewBox="0 0 1000 500">
                <!-- 背景 -->
                <defs>
                    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f093fb;stop-opacity:0.1" />
                        <stop offset="100%" style="stop-color:#f5576c;stop-opacity:0.1" />
                    </linearGradient>
                </defs>
                <rect width="1000" height="500" fill="url(#bgGradient)" />
                
                <!-- 素材审核应用端 -->
                <g id="frontend">
                    <rect x="50" y="50" width="200" height="120" rx="10" fill="#4299e1" stroke="#2b6cb0" stroke-width="2"/>
                    <text x="150" y="80" text-anchor="middle" fill="white" font-size="14" font-weight="bold">素材审核应用端</text>
                    <text x="150" y="100" text-anchor="middle" fill="white" font-size="11">规则文档上传</text>
                    <text x="150" y="115" text-anchor="middle" fill="white" font-size="11">素材文件上传</text>
                    <text x="150" y="130" text-anchor="middle" fill="white" font-size="11">审核结果展示</text>
                    <text x="150" y="145" text-anchor="middle" fill="white" font-size="11">规则管理</text>
                </g>
                
                <!-- 本地小模型服务 -->
                <g id="local-models">
                    <rect x="50" y="220" width="200" height="120" rx="10" fill="#48bb78" stroke="#2f855a" stroke-width="2"/>
                    <text x="150" y="250" text-anchor="middle" fill="white" font-size="14" font-weight="bold">本地小模型服务</text>
                    <text x="150" y="270" text-anchor="middle" fill="white" font-size="11">视频切片</text>
                    <text x="150" y="285" text-anchor="middle" fill="white" font-size="11">OCR识别</text>
                    <text x="150" y="300" text-anchor="middle" fill="white" font-size="11">ASR语音识别</text>
                    <text x="150" y="315" text-anchor="middle" fill="white" font-size="11">人脸检测</text>
                </g>
                
                <!-- AI中台 -->
                <g id="ai-platform">
                    <rect x="400" y="50" width="500" height="290" rx="15" fill="#ed8936" stroke="#c05621" stroke-width="3"/>
                    <text x="650" y="80" text-anchor="middle" fill="white" font-size="16" font-weight="bold">AI中台 - 核心审核引擎</text>
                    
                    <!-- 知识库 -->
                    <rect x="420" y="100" width="140" height="80" rx="8" fill="#f6e05e" stroke="#d69e2e" stroke-width="2"/>
                    <text x="490" y="125" text-anchor="middle" fill="#744210" font-size="12" font-weight="bold">知识库</text>
                    <text x="490" y="140" text-anchor="middle" fill="#744210" font-size="10">业务分类</text>
                    <text x="490" y="155" text-anchor="middle" fill="#744210" font-size="10">规则维护</text>
                    <text x="490" y="170" text-anchor="middle" fill="#744210" font-size="10">RAG召回</text>
                    
                    <!-- 审核引擎 -->
                    <rect x="580" y="100" width="140" height="80" rx="8" fill="#9f7aea" stroke="#805ad5" stroke-width="2"/>
                    <text x="650" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">审核Agent</text>
                    <text x="650" y="140" text-anchor="middle" fill="white" font-size="10">规则解析</text>
                    <text x="650" y="155" text-anchor="middle" fill="white" font-size="10">任务调度</text>
                    <text x="650" y="170" text-anchor="middle" fill="white" font-size="10">结果聚合</text>
                    
                    <!-- 规则引擎 -->
                    <rect x="740" y="100" width="140" height="80" rx="8" fill="#f56565" stroke="#e53e3e" stroke-width="2"/>
                    <text x="810" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">规则Agent</text>
                    <text x="810" y="140" text-anchor="middle" fill="white" font-size="10">文档解析</text>
                    <text x="810" y="155" text-anchor="middle" fill="white" font-size="10">规则分类</text>
                    <text x="810" y="170" text-anchor="middle" fill="white" font-size="10">生成规则</text>
                    
                    <!-- 大模型审核 -->
                    <rect x="420" y="200" width="140" height="80" rx="8" fill="#38b2ac" stroke="#319795" stroke-width="2"/>
                    <text x="490" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">大模型审核</text>
                    <text x="490" y="240" text-anchor="middle" fill="white" font-size="10">复杂场景理解</text>
                    <text x="490" y="255" text-anchor="middle" fill="white" font-size="10">剧情逻辑判断</text>
                    <text x="490" y="270" text-anchor="middle" fill="white" font-size="10">结构化分析</text>
                    
                    <!-- 工程审核 -->
                    <rect x="580" y="200" width="140" height="80" rx="8" fill="#667eea" stroke="#5a67d8" stroke-width="2"/>
                    <text x="650" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">工程审核</text>
                    <text x="650" y="240" text-anchor="middle" fill="white" font-size="10">文本匹配</text>
                    <text x="650" y="255" text-anchor="middle" fill="white" font-size="10">正则表达式</text>
                    <text x="650" y="270" text-anchor="middle" fill="white" font-size="10">基础特征检测</text>
                    
                    <!-- 结果输出 -->
                    <rect x="740" y="200" width="140" height="80" rx="8" fill="#68d391" stroke="#48bb78" stroke-width="2"/>
                    <text x="810" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">结果输出</text>
                    <text x="810" y="240" text-anchor="middle" fill="white" font-size="10">审核报告</text>
                    <text x="810" y="255" text-anchor="middle" fill="white" font-size="10">风险评级</text>
                    <text x="810" y="270" text-anchor="middle" fill="white" font-size="10">建议措施</text>
                </g>
                
                <!-- 连接线 -->
                <!-- 应用端到AI中台 -->
                <line x1="250" y1="110" x2="400" y2="140" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="325" y="120" text-anchor="middle" fill="#4a5568" font-size="10">规则文档</text>
                
                <!-- 应用端到本地小模型服务 -->
                <line x1="150" y1="170" x2="150" y2="220" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="180" y="195" text-anchor="middle" fill="#4a5568" font-size="10">素材文件</text>
                
                <!-- 本地模型到AI中台 -->
                <line x1="250" y1="280" x2="400" y2="200" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="325" y="235" text-anchor="middle" fill="#4a5568" font-size="10">解析结果</text>
                
                <!-- AI中台内部连接 -->
                
                <!-- 箭头定义 -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568" />
                    </marker>
                </defs>
            </svg>
        </div>

        <!-- 业务流程图 -->
        <div class="section">
            <h2>🔄 业务流程图</h2>
            <svg class="flow-diagram" viewBox="0 0 1000 450">
                <!-- 背景 -->
                <rect width="1000" height="450" fill="url(#bgGradient)" />
                
                <!-- 链路标题 -->
                <text x="500" y="30" text-anchor="middle" fill="#2d3748" font-size="16" font-weight="bold">双链路业务流程</text>
                
                <!-- 链路一：规则配置链路 -->
                <text x="50" y="70" fill="#4299e1" font-size="14" font-weight="bold">链路一：规则配置流程</text>
                
                <!-- 规则文档上传 -->
                <g id="rule-step1">
                    <rect x="50" y="85" width="120" height="60" rx="8" fill="#4299e1" stroke="#2b6cb0" stroke-width="2"/>
                    <text x="110" y="110" text-anchor="middle" fill="white" font-size="11" font-weight="bold">规则文档上传</text>
                    <text x="110" y="125" text-anchor="middle" fill="white" font-size="9">应用端</text>
                </g>
                
                <!-- AI解析生成 -->
                <g id="rule-step2">
                    <rect x="230" y="85" width="120" height="60" rx="8" fill="#9f7aea" stroke="#805ad5" stroke-width="2"/>
                    <text x="290" y="110" text-anchor="middle" fill="white" font-size="11" font-weight="bold">AI一键生成</text>
                    <text x="290" y="125" text-anchor="middle" fill="white" font-size="9">规则知识集</text>
                </g>
                
                <!-- 客户勾选 -->
                <g id="rule-step3">
                    <rect x="410" y="85" width="120" height="60" rx="8" fill="#48bb78" stroke="#2f855a" stroke-width="2"/>
                    <text x="470" y="110" text-anchor="middle" fill="white" font-size="11" font-weight="bold">客户勾选</text>
                    <text x="470" y="125" text-anchor="middle" fill="white" font-size="9">确认规则</text>
                </g>
                
                <!-- 导入知识库 -->
                <g id="rule-step4">
                    <rect x="590" y="85" width="120" height="60" rx="8" fill="#f6e05e" stroke="#d69e2e" stroke-width="2"/>
                    <text x="650" y="110" text-anchor="middle" fill="#744210" font-size="11" font-weight="bold">一键导入</text>
                    <text x="650" y="125" text-anchor="middle" fill="#744210" font-size="9">知识库</text>
                </g>
                
                <!-- 链路一连接线 -->
                <line x1="170" y1="115" x2="230" y2="115" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="350" y1="115" x2="410" y2="115" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="530" y1="115" x2="590" y2="115" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- 分隔线 -->
                <line x1="50" y1="200" x2="950" y2="200" stroke="#e2e8f0" stroke-width="2" stroke-dasharray="10,5"/>
                
                <!-- 链路二：素材审核链路 -->
                <text x="50" y="240" fill="#ed8936" font-size="14" font-weight="bold">链路二：素材审核流程</text>
                
                <!-- 素材文件上传 -->
                <g id="audit-step1">
                    <rect x="50" y="255" width="120" height="60" rx="8" fill="#ed8936" stroke="#c05621" stroke-width="2"/>
                    <text x="110" y="280" text-anchor="middle" fill="white" font-size="11" font-weight="bold">素材文件上传</text>
                    <text x="110" y="295" text-anchor="middle" fill="white" font-size="9">应用端</text>
                </g>
                
                <!-- 小模型解析 -->
                <g id="audit-step2">
                    <rect x="230" y="255" width="120" height="60" rx="8" fill="#48bb78" stroke="#2f855a" stroke-width="2"/>
                    <text x="290" y="280" text-anchor="middle" fill="white" font-size="11" font-weight="bold">小模型解析</text>
                    <text x="290" y="295" text-anchor="middle" fill="white" font-size="9">生成JSON</text>
                </g>
                
                <!-- AI中台审核 -->
                <g id="audit-step3">
                    <rect x="410" y="255" width="120" height="60" rx="8" fill="#f56565" stroke="#e53e3e" stroke-width="2"/>
                    <text x="470" y="280" text-anchor="middle" fill="white" font-size="11" font-weight="bold">AI中台审核</text>
                    <text x="470" y="295" text-anchor="middle" fill="white" font-size="9">JSON数据</text>
                </g>
                
                <!-- 规则召回 -->
                <g id="audit-step4">
                    <rect x="590" y="255" width="120" height="60" rx="8" fill="#9f7aea" stroke="#805ad5" stroke-width="2"/>
                    <text x="650" y="280" text-anchor="middle" fill="white" font-size="11" font-weight="bold">规则召回</text>
                    <text x="650" y="295" text-anchor="middle" fill="white" font-size="9">匹配执行</text>
                </g>
                
                <!-- 结果返回 -->
                <g id="audit-step5">
                    <rect x="770" y="255" width="120" height="60" rx="8" fill="#68d391" stroke="#48bb78" stroke-width="2"/>
                    <text x="830" y="280" text-anchor="middle" fill="white" font-size="11" font-weight="bold">结果展示</text>
                    <text x="830" y="295" text-anchor="middle" fill="white" font-size="9">应用端</text>
                </g>
                
                <!-- 链路二连接线 -->
                <line x1="170" y1="285" x2="230" y2="285" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="350" y1="285" x2="410" y2="285" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="530" y1="285" x2="590" y2="285" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="710" y1="285" x2="770" y2="285" stroke="#4a5568" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- 详细说明框 -->
                <g id="detail-box1">
                    <rect x="750" y="80" width="200" height="80" rx="8" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1"/>
                    <text x="850" y="100" text-anchor="middle" fill="#2d3748" font-size="11" font-weight="bold">小模型解析内容</text>
                    <text x="850" y="115" text-anchor="middle" fill="#4a5568" font-size="9">• 视频切片提取</text>
                    <text x="850" y="128" text-anchor="middle" fill="#4a5568" font-size="9">• OCR文字识别</text>
                    <text x="850" y="141" text-anchor="middle" fill="#4a5568" font-size="9">• ASR语音识别</text>
                    <text x="850" y="154" text-anchor="middle" fill="#4a5568" font-size="9">• 人脸/场景检测</text>
                </g>
                
                <g id="detail-box2">
                    <rect x="50" y="350" width="900" height="80" rx="8" fill="#f0fff4" stroke="#68d391" stroke-width="2"/>
                    <text x="500" y="375" text-anchor="middle" fill="#2d3748" font-size="12" font-weight="bold">核心优势：规则与审核分离，支持动态配置</text>
                    <text x="250" y="395" text-anchor="middle" fill="#4a5568" font-size="10">✓ 业务人员可自主配置规则</text>
                    <text x="500" y="395" text-anchor="middle" fill="#4a5568" font-size="10">✓ 工程规则+AI规则混合执行</text>
                    <text x="750" y="395" text-anchor="middle" fill="#4a5568" font-size="10">✓ 规则热更新无需重启服务</text>
                    <text x="250" y="410" text-anchor="middle" fill="#4a5568" font-size="10">✓ 多模态数据统一处理</text>
                    <text x="500" y="410" text-anchor="middle" fill="#4a5568" font-size="10">✓ 审核结果可追溯可解释</text>
                    <text x="750" y="410" text-anchor="middle" fill="#4a5568" font-size="10">✓ 支持批量和实时审核</text>
                </g>
                
                <!-- 链路间的关联线 -->
                <line x1="650" y1="145" x2="650" y2="255" stroke="#9f7aea" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
                <text x="660" y="200" fill="#9f7aea" font-size="10">知识库供给</text>
                
                <!-- 箭头定义 -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568" />
                    </marker>
                </defs>
            </svg>
        </div>

        <!-- 知识库规则集设计 -->
        <div class="section">
            <h2>📋 知识库规则集设计</h2>
            <p style="text-align: center; color: #4a5568; margin-bottom: 30px;">层次化、可扩展的规则集数据结构，支持复杂业务逻辑的精确表达</p>
            
            <div class="rule-schema">
                <!-- 基础信息字段 -->
                <div class="schema-group">
                    <h3>🏷️ 基础信息</h3>
                    <div class="schema-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                    <th>示例值</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="field-name">rule_name</td>
                                    <td class="field-type">string</td>
                                    <td>规则显示名称，便于业务人员理解和管理</td>
                                    <td class="field-example">"金融产品收益承诺审核"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">business_category</td>
                                    <td class="field-type">string</td>
                                    <td>业务分类：通用业务（适用所有金融产品广告）/信用卡业务​等</td>
                                    <td class="field-example">"通用业务（适用所有金融产品广告）"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">rule_category</td>
                                    <td class="field-type">string</td>
                                    <td>规则大类：法规/公司等</td>
                                    <td class="field-example">"法规"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">rule_description</td>
                                    <td class="field-type">string</td>
                                    <td>规则详细描述和使用说明</td>
                                    <td class="field-example">"用于检测金融产品..."</td>
                                </tr>
                                <tr>
                                    <td class="field-name">priority</td>
                                    <td class="field-type">enum</td>
                                    <td>优先级：HIGH | MEDIUM | LOW，影响执行顺序</td>
                                    <td class="field-example">"HIGH"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">audit_type</td>
                                    <td class="field-type">enum</td>
                                    <td>审核类型：ENGINE(工程规则) | LLM(大模型) | HYBRID(混合)</td>
                                    <td class="field-example">"ENGINE"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">status</td>
                                    <td class="field-type">enum</td>
                                    <td>规则状态：ACTIVE(生效) | INACTIVE(停用)</td>
                                    <td class="field-example">"ACTIVE"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">violation_template</td>
                                    <td class="field-type">string</td>
                                    <td>违规提示模板</td>
                                    <td class="field-example">​​违规​​：老年客群广告未添加风险弹窗，且使用"{高收益词}"</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 工程规则配置 -->
                <div class="schema-group">
                    <h3>⚙️ 工程规则配置</h3>
                    <div class="schema-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                    <th>示例值</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="field-name">engine_type</td>
                                    <td class="field-type">enum</td>
                                    <td>引擎类型：complex_match | contains_all | regex_match | keyword_logic</td>
                                    <td class="field-example">"complex_match"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">engine_config</td>
                                    <td class="field-type">object</td>
                                    <td>引擎配置参数</td>
                                    <td class="field-example">见下表详细配置</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ text_sources</td>
                                    <td class="field-type">array</td>
                                    <td>文本来源：["ocr", "asr", "all"]</td>
                                    <td class="field-example">["ocr", "asr"]</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ match_logic</td>
                                    <td class="field-type">enum</td>
                                    <td>匹配逻辑：AND | OR</td>
                                    <td class="field-example">"AND"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ case_sensitive</td>
                                    <td class="field-type">boolean</td>
                                    <td>是否区分大小写</td>
                                    <td class="field-example">false</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ threshold</td>
                                    <td class="field-type">object</td>
                                    <td>置信度阈值配置</td>
                                    <td class="field-example">{"confidence": 0.8}</td>
                                </tr>
                                <tr>
                                    <td class="field-name">engine_rule</td>
                                    <td class="field-type">object</td>
                                    <td>具体的工程规则定义</td>
                                    <td class="field-example">见下表详细配置</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ logic</td>
                                    <td class="field-type">enum</td>
                                    <td>条件间逻辑：AND | OR</td>
                                    <td class="field-example">"OR"</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ conditions</td>
                                    <td class="field-type">array</td>
                                    <td>条件列表，支持多种匹配类型</td>
                                    <td class="field-example">[{...}, {...}]</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- LLM规则配置 -->
                <div class="schema-group">
                    <h3>🤖 LLM规则配置</h3>
                    <div class="schema-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                    <th>示例值</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="field-name">llm_config</td>
                                    <td class="field-type">object</td>
                                    <td>大语言模型配置参数</td>
                                    <td class="field-example">见下表详细配置</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ model</td>
                                    <td class="field-type">string</td>
                                    <td>模型名称：qwen-plus/qwen3-32b</td>
                                    <td class="field-example">"qwen3-32b"</td>
                                </tr>
                                <!-- <tr>
                                    <td class="field-name">└─ temperature</td>
                                    <td class="field-type">float</td>
                                    <td>生成随机性：0.0-1.0，越低越确定</td>
                                    <td class="field-example">0.1</td>
                                </tr> -->
                                <!-- <tr>
                                    <td class="field-name">└─ max_tokens</td>
                                    <td class="field-type">integer</td>
                                    <td>最大输出token数</td>
                                    <td class="field-example">1000</td>
                                </tr> -->
                                <tr>
                                    <td class="field-name">llm_rule</td>
                                    <td class="field-type">object</td>
                                    <td>LLM审核规则定义</td>
                                    <td class="field-example">见下表详细配置</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ context</td>
                                    <td class="field-type">string</td>
                                    <td>业务背景和规则说明</td>
                                    <td class="field-example">"医疗广告不得使用..."</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ prompt_template</td>
                                    <td class="field-type">string</td>
                                    <td>提示词模板</td>
                                    <td class="field-example">"请判断以下内容..."</td>
                                </tr>
                                <tr>
                                    <td class="field-name">└─ examples</td>
                                    <td class="field-type">array</td>
                                    <td>示例样本，用于few-shot学习</td>
                                    <td class="field-example">[{...}, {...}]</td>
                                </tr>
                                <!-- <tr>
                                    <td class="field-name">└─ evaluation_criteria</td>
                                    <td class="field-type">array</td>
                                    <td>评估标准列表</td>
                                    <td class="field-example">["是否夸大", "是否违规"]</td>
                                </tr> -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术栈 -->
        <div class="section">
            <h2>🛠️ 技术栈</h2>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>前端技术</h4>
                    <ul>
                        <li>HTML5 + CSS3 + JavaScript</li>
                        <li>FastAPI + Uvicorn</li>
                        <li>响应式设计</li>
                        <li>文件拖拽上传</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>AI模型</h4>
                    <ul>
                        <li>FunASR (语音识别)</li>
                        <li>RapidOCR (文字识别)</li>
                        <li>人脸检测模型</li>
                        <li>大语言模型 (规则理解)</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>后端架构</h4>
                    <ul>
                        <li>Python 3.9+</li>
                        <li>FastAPI 异步框架</li>
                        <li>Pydantic 数据验证</li>
                        <li>SQLite/MySQL 数据存储</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>部署运维</h4>
                    <ul>
                        <li>Docker 容器化</li>
                        <li>GPU 加速支持</li>
                        <li>负载均衡</li>
                        <li>监控告警</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 实现亮点 -->
        <div class="section">
            <h2>🌟 实现亮点</h2>
            
            <div class="highlight">
                <h3>1. AI智能规则生成</h3>
                <p>基于大模型的规则文档理解能力，自动解析业务规则文档，生成结构化规则集。支持自然语言描述的复杂审核逻辑自动转换为可执行规则。</p>
                <div class="code-block"># 输入：自然语言规则文档
"金融产品宣传视频中，如果画面出现具体收益率数字，
 则语音必须同时包含风险提示语，如'投资有风险'等"

# AI自动生成规则集
{
  "rule_id": "finance_risk_disclosure",
  "rule_name": "收益率风险提示配对规则",
  "audit_type": "ENGINE",
  "engine_type": "complex_match",
  "engine_config": {
    "text_sources": ["ocr", "asr"],
    "match_logic": "AND"
  },
  "engine_rule": {
    "logic": "AND",
    "conditions": [
      {
        "type": "conditional_cross_source",
        "if_ocr_contains": ["年化收益", "%", "收益率"],
        "then_asr_must_contain": ["投资有风险", "风险提示"]
      }
    ]
  }
}</div>
            </div>

            <div class="highlight">
                <h3>2. LLM+工程规则混合审核</h3>
                <p>创新性地将大语言模型的语义理解能力与工程规则的精确匹配能力相结合，实现高效且准确的内容审核。工程规则负责快速筛选，LLM负责复杂语义判断。</p>
                <div class="code-block">
# 混合审核流程
1. 工程规则预筛选（毫秒级）
   - 关键词匹配：快速识别明显违规内容
   - 正则表达式：检测格式化违规信息
   - 基础特征检测：人脸、身份证等敏感元素

2. LLM语义审核（秒级）
   - 上下文理解：判断是否真实违规
   - 语义推理：识别隐晦表达和暗示
   - 场景分析：结合视频剧情判断合规性

3. 结果融合决策
   - 工程规则：高置信度直接判定
   - LLM审核：处理边界案例和复杂场景
   - 综合评分：多维度风险评估
                </div>
            </div>

            <div class="highlight">
                <h3>3. 规则集技术设计</h3>
                <p>设计了层次化、可扩展的规则集数据结构，支持复杂业务逻辑的精确表达。采用JSON Schema规范，确保规则的标准化和可维护性。</p>
                <div class="code-block">
# 规则集核心字段设计
{
  "rule_id": "string",           // 规则唯一标识
  "rule_name": "string",         // 规则名称
  "business_category": "string", // 业务分类：金融/医疗/教育等
  "audit_type": "ENGINE|LLM",    // 审核类型
  "priority": "HIGH|MEDIUM|LOW", // 优先级
  "status": "ACTIVE|INACTIVE",   // 状态
  
  // 工程规则配置
  "engine_type": "complex_match|contains_all|regex_match",
  "engine_config": {
    "text_sources": ["ocr", "asr", "all"],
    "match_logic": "AND|OR",
    "case_sensitive": false,
    "threshold": {"confidence": 0.8}
  },
  "engine_rule": {
    "logic": "AND|OR",
    "conditions": [...]
  },
  
  // LLM规则配置  
  "llm_config": {
    "model": "gpt-4|claude-3",
    "temperature": 0.1,
    "max_tokens": 1000,
    "prompt_template": "string"
  },
  "llm_rule": {
    "context": "string",
    "examples": [...],
    "evaluation_criteria": [...]
  }
}
                </div>
            </div>

            <div class="highlight">
                <h3>4. 规则集实际样例</h3>
                <p>以下是针对不同业务场景的完整规则集样例，展示了系统的实际应用能力。</p>
                
                <h4>📊 金融产品合规审核规则</h4>
                <div class="code-block">
{
  "rule_id": "finance_compliance_001",
  "rule_name": "金融产品收益承诺审核",
  "business_category": "finance",
  "audit_type": "ENGINE",
  "priority": "HIGH",
  "status": "ACTIVE",
  
  "engine_type": "complex_match",
  "engine_config": {
    "text_sources": ["all"],
    "match_logic": "OR",
    "case_sensitive": false
  },
  "engine_rule": {
    "logic": "OR",
    "conditions": [
      {
        "type": "contains",
        "logic": "OR",
        "keywords": [
          "保本保息", "稳赚不赔", "零风险",
          "100%收益", "保证赚钱", "无风险投资"
        ],
        "description": "违规收益承诺词汇"
      },
      {
        "type": "regex_match",
        "patterns": [
          "年化收益\\d+%以上",
          "月收益率\\d+%",
          "日息\\d+‰"
        ],
        "description": "具体收益率承诺"
      }
    ]
  }
}
                </div>

                <h4>🏥 医疗广告审核规则</h4>
                <div class="code-block">
{
  "rule_id": "medical_ad_001", 
  "rule_name": "医疗广告夸大宣传审核",
  "business_category": "medical",
  "audit_type": "LLM",
  "priority": "HIGH",
  "status": "ACTIVE",
  
  "llm_config": {
    "model": "gpt-4",
    "temperature": 0.1,
    "max_tokens": 500,
    "prompt_template": "请判断以下医疗广告内容是否存在夸大宣传..."
  },
  "llm_rule": {
    "context": "医疗广告不得使用'根治'、'彻底治愈'等绝对化用词",
    "examples": [
      {
        "input": "我们的药物可以根治糖尿病",
        "output": "违规：使用了'根治'等绝对化用词",
        "violation": true
      }
    ],
    "evaluation_criteria": [
      "是否使用绝对化治疗承诺",
      "是否夸大治疗效果", 
      "是否包含必要的风险提示"
    ]
  }
}
                </div>

                <h4>🎓 教育培训审核规则</h4>
                <div class="code-block">
{
  "rule_id": "education_001",
  "rule_name": "教育培训效果承诺审核", 
  "business_category": "education",
  "audit_type": "ENGINE",
  "priority": "MEDIUM",
  "status": "ACTIVE",
  
  "engine_type": "complex_match",
  "engine_config": {
    "text_sources": ["ocr", "asr"],
    "match_logic": "AND"
  },
  "engine_rule": {
    "logic": "OR",
    "conditions": [
      {
        "type": "contains",
        "logic": "OR", 
        "keywords": [
          "包过", "保过", "不过退费",
          "100%通过率", "零基础包会"
        ]
      },
      {
        "type": "must_together",
        "keyword_pairs": [
          {"trigger": "高薪就业", "required": "就业情况因人而异"},
          {"trigger": "月薪", "required": "薪资水平仅供参考"}
        ]
      }
    ]
  }
}
                </div>
            </div>

            <div class="highlight">
                <h3>5. 动态规则热更新</h3>
                <p>支持规则的实时更新和版本管理，业务人员可通过管理界面直接修改规则配置，系统自动进行规则验证和灰度发布，无需重启服务。</p>
                <div class="code-block">
# 规则版本管理
{
  "rule_version": "v2.1.0",
  "update_time": "2024-12-20T10:30:00Z",
  "update_reason": "新增跨模态条件检查",
  "rollback_version": "v2.0.5",
  "test_coverage": {
    "passed": 156,
    "failed": 2,
    "coverage_rate": "98.7%"
  }
}

# 灰度发布配置
{
  "deployment_strategy": "canary",
  "traffic_split": {
    "v2.0.5": "80%",  // 稳定版本
    "v2.1.0": "20%"   // 新版本
  },
  "rollback_threshold": {
    "error_rate": "5%",
    "response_time": "2s"
  }
}
                </div>
            </div>
        </div>

        <!-- 应用场景 -->
        <div class="section">
            <h2>🎯 应用场景</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>📺 营销视频审核</h3>
                    <p>金融产品宣传视频的合规性检查，确保符合监管要求</p>
                </div>
                <div class="feature-card">
                    <h3>📄 文档内容审核</h3>
                    <p>产品说明书、宣传册等文档的规范性审核</p>
                </div>
                <div class="feature-card">
                    <h3>🎙️ 音频内容审核</h3>
                    <p>客服录音、营销话术的合规性检查</p>
                </div>
                <div class="feature-card">
                    <h3>🖼️ 图像素材审核</h3>
                    <p>海报、广告图片的内容审核和风险评估</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
