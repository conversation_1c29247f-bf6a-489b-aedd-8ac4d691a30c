#!/usr/bin/env python3
import requests
import json

def test_image_upload():
    url = "http://localhost:8080/upload"
    
    print("测试图片上传")
    try:
        with open("test_files/银行宣传图.png", "rb") as f:
            files = {"file": ("银行宣传图.png", f, "image/png")}
            response = requests.post(url, files=files)
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    except FileNotFoundError:
        print("图片文件未找到，请先运行 create_test_image.py")
    except Exception as e:
        print(f"上传失败: {e}")

if __name__ == "__main__":
    test_image_upload()
