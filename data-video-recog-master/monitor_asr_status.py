#!/usr/bin/env python3
"""
监控ASR服务状态
"""
import requests
import time

def check_asr_service():
    """检查ASR服务状态"""
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            return True, "ASR服务运行正常"
        else:
            return False, f"ASR服务返回错误: {response.status_code}"
    except requests.exceptions.ConnectionError:
        return False, "ASR服务连接被拒绝（可能还在启动中）"
    except Exception as e:
        return False, f"ASR服务检查异常: {e}"

def check_video_service():
    """检查视频分析服务状态"""
    try:
        response = requests.get("http://localhost:8081/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            asr_available = result.get('capabilities', {}).get('asr_service', False)
            return True, asr_available
        else:
            return False, False
    except Exception as e:
        return False, False

def main():
    print("🔍 ASR服务状态监控")
    print("=" * 50)
    
    while True:
        print(f"\n⏰ {time.strftime('%H:%M:%S')}")
        
        # 检查ASR服务
        asr_running, asr_msg = check_asr_service()
        if asr_running:
            print(f"🎤 ASR服务: ✅ {asr_msg}")
        else:
            print(f"🎤 ASR服务: ❌ {asr_msg}")
        
        # 检查视频分析服务
        video_running, asr_detected = check_video_service()
        if video_running:
            if asr_detected:
                print(f"🎬 视频服务: ✅ 运行中，ASR服务已连接")
                print("\n🎉 所有服务就绪！现在可以进行完整的视频分析了！")
                print("🌐 访问: http://localhost:8081/demo")
                break
            else:
                print(f"🎬 视频服务: ✅ 运行中，等待ASR服务连接...")
        else:
            print(f"🎬 视频服务: ❌ 不可用")
        
        print("⏳ 等待10秒后重新检查...")
        time.sleep(10)

if __name__ == "__main__":
    main()
