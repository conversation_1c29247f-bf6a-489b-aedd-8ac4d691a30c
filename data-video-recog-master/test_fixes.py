#!/usr/bin/env python3
import requests
import json
import time

def test_single_upload():
    """测试单次上传是否正常工作"""
    url = "http://localhost:8080/upload"
    
    print("🧪 测试修复后的上传功能")
    print("=" * 50)
    
    # 测试1：单次上传
    print("\n📄 测试1: 单次上传理财产品说明书")
    try:
        with open("test_files/理财产品说明书.txt", "rb") as f:
            files = {"file": ("理财产品说明书.txt", f, "text/plain")}
            response = requests.post(url, files=files)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 上传成功")
                print(f"📊 风险等级: {result['audit_result']['risk_evaluation']}")
                print(f"🔒 追踪ID: {result['trace_id']}")
                return result['trace_id']
            else:
                print(f"❌ 上传失败: {response.status_code}")
                return None
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def test_multiple_uploads():
    """测试多次上传不同文件"""
    url = "http://localhost:8080/upload"
    
    print("\n📁 测试2: 连续上传多个文件")
    
    files_to_test = [
        ("test_files/理财产品说明书.txt", "text/plain"),
        ("test_files/银行宣传图.png", "image/png"),
        ("test_files/贷款产品介绍.txt", "text/plain")
    ]
    
    trace_ids = []
    
    for i, (file_path, content_type) in enumerate(files_to_test, 1):
        print(f"\n  📤 上传文件 {i}: {file_path.split('/')[-1]}")
        try:
            with open(file_path, "rb") as f:
                files = {"file": (file_path.split('/')[-1], f, content_type)}
                response = requests.post(url, files=files)
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"    ✅ 成功 - 风险等级: {result['audit_result']['risk_evaluation']}")
                    trace_ids.append(result['trace_id'])
                else:
                    print(f"    ❌ 失败: {response.status_code}")
        except Exception as e:
            print(f"    ❌ 异常: {e}")
        
        # 短暂延迟，模拟真实使用场景
        time.sleep(0.5)
    
    return trace_ids

def test_records():
    """测试记录查询功能"""
    print("\n📋 测试3: 查询所有审核记录")
    try:
        response = requests.get("http://localhost:8080/records")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 查询成功，共 {len(result['records'])} 条记录")
            
            # 显示最近的3条记录
            recent_records = result['records'][-3:]
            for i, record in enumerate(recent_records, 1):
                print(f"  记录 {i}:")
                print(f"    📁 文件: {record['filename']}")
                print(f"    📊 风险: {record['audit_result']['risk_evaluation']}")
                print(f"    🕒 时间: {record['upload_time']}")
        else:
            print(f"❌ 查询失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 查询异常: {e}")

def test_health():
    """测试服务健康状态"""
    print("\n🏥 测试4: 服务健康检查")
    try:
        response = requests.get("http://localhost:8080/health")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务状态: {result['status']}")
            print(f"🤖 AI启用: {result['ai_enabled']}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

def main():
    print("🔧 银行素材审核系统 - 修复验证测试")
    print("=" * 60)
    
    # 测试服务健康状态
    test_health()
    
    # 测试单次上传
    trace_id = test_single_upload()
    
    # 测试多次上传
    trace_ids = test_multiple_uploads()
    
    # 测试记录查询
    test_records()
    
    print("\n" + "=" * 60)
    print("🎉 修复验证测试完成！")
    print("\n📝 修复内容:")
    print("  ✅ 修复了formatFileSize函数作用域问题")
    print("  ✅ 修复了文件需要连续上传两次的问题")
    print("  ✅ 修复了事件处理器冲突问题")
    print("  ✅ 添加了状态清理逻辑")
    print("\n🌐 现在可以正常使用Demo页面: http://localhost:8080/demo")
    print("=" * 60)

if __name__ == "__main__":
    main()
