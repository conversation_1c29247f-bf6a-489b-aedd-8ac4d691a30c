import os
import random
import numpy as np
from sklearn.base import BaseEstimator
import torch
import torch.nn as nn
from torch.nn.functional import one_hot, softmax
from torch.utils.data import DataLoader, RandomSampler, SequentialSampler, TensorDataset
from transformers import Bert<PERSON>okenizer, AdamW, get_linear_schedule_with_warmup
from transformers.models.bert.modeling_bert import BertPreTrainedModel, BertModel, BertOnlyMLMHead
from transformers.data.data_collator import DataCollatorForLanguageModeling
from zalearn.utils.train_utils import ProcessStatus, Metrics


MODEL_DIR_BASE = os.path.expanduser("~/.zalearn/models/hf/")


class DialogLM(BaseEstimator):
    def __init__(self, num_epochs=1, init_lr=5e-5, batch_size=10, max_len=512,
                 base_model="zhongan/qalm-tinyp-chinese", **kwargs):
        self.num_epochs = num_epochs
        self.init_lr = init_lr
        self.batch_size = batch_size
        self.max_len = max_len
        self.base_model = base_model
        self.kwargs = kwargs

        self.data_util = None
        self.qa_net = None

    def fit(self, dialogs, dev_dialogs, device=None):
        device = self._use_device(device)

        tmp_save_path = _gen_tmp_path()
        print("tmp model path:", tmp_save_path)

        base_model_dir = MODEL_DIR_BASE + "/" + self.base_model
        self.data_util = QALMDataUtil(seq_len=self.max_len, tokenizer_dir=base_model_dir)
        self.qa_net = QALMNet.from_pretrained(base_model_dir)

        num_eval_steps = self.kwargs.get("num_eval_steps", 400)
        num_progress_steps = self.kwargs.get("num_progress_steps", 20)
        trainner = QALMTrainer(self.qa_net, self.init_lr, self.num_epochs,
                               self.batch_size, self.data_util, device,
                               num_eval_steps, num_progress_steps)
        trainner.train(dialogs, dev_dialogs, tmp_save_path)

        if os.path.exists(tmp_save_path):
            os.remove(tmp_save_path)
        return self

    def predict(self, texts, device=None):
        device = self._use_device(device)

        preds = []
        self.qa_net.to(device)
        self.qa_net.eval()
        for inputs, in self.data_util.get_loader(texts, batch_size=self.batch_size, device=device):
            sub_preds = self.qa_net(inputs).cpu().detach().numpy()
            preds.extend(sub_preds)
        return np.array(preds)

    def eval(self, dialogs, device=None):
        device = self._use_device(device)

        self.qa_net.to(device)
        self.qa_net.eval()
        metrics = Metrics()
        q_texts, a_texts = _dialog_to_qa(dialogs)
        for inputs in self.data_util.get_loader(q_texts, a_texts, batch_size=self.batch_size,
                                                device=device):
            loss, acc = self.qa_net(*inputs)
            metrics.add_metrics(loss=loss.item(), acc=acc.item())
        return metrics.get_result()

    @staticmethod
    def _use_device(device):
        device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        print("Use device:", device)
        return device


def _gen_tmp_path():
    import uuid
    folder = os.path.expanduser("~/.zalearn/tmp/")
    if not os.path.exists(folder):
        os.mkdir(folder)
    return folder + uuid.uuid4().hex


# noinspection PyAbstractClass,PyUnresolvedReferences
class QALMNet(BertPreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.bert = BertModel(config, add_pooling_layer=False)
        self.cls = BertOnlyMLMHead(config)
        self.pooled = nn.Linear(config.hidden_size, config.hidden_size)
        self.proj = nn.Linear(config.hidden_size, config.hidden_size)
        self.drop = nn.Dropout(config.hidden_dropout_prob)
        self.init_weights()

    def encode_input(self, input_ids):
        sequence_output = self.bert(input_ids)[0]

        first_token_tensor = sequence_output[:, 0]
        pooled_output = self.pooled(first_token_tensor)

        prediction_scores = self.cls(sequence_output)
        return prediction_scores, pooled_output

    def qa_loss(self, encoded_q, encoded_a):
        proj_q = self.proj(encoded_q)

        exp_q = proj_q.unsqueeze(1)
        exp_a = encoded_a.unsqueeze(0)
        pair_scores = self.drop((exp_q * exp_a).sum(axis=2))
        pair_scores = softmax(pair_scores, dim=-1)

        _batch_size = encoded_q.size(0)
        ranges = torch.arange(0, _batch_size, dtype=torch.int64, device=pair_scores.device)
        pair_labels = one_hot(ranges, _batch_size)

        corrects = torch.eq(pair_scores.argmax(1), pair_labels.argmax(1))
        acc = corrects.type(torch.float32).mean()

        losses = (-1 * pair_labels * torch.log(pair_scores + 1e-10) -
                  (1 - pair_labels) * torch.log(1 - pair_scores + 1e-10))
        loss = losses.mean()
        return loss, acc

    def mlm_loss(self, preds, labels):
        loss_fct = nn.CrossEntropyLoss()  # -100 index = padding token
        losses = loss_fct(preds.view(-1, self.bert.config.vocab_size), labels.view(-1))
        loss = losses.mean()
        return loss

    def forward(self, input_q, input_a=None):
        mlm_pred_q, encoded_q = self.encode_input(input_q["input_ids"])
        if input_a is None:
            return encoded_q
        else:
            mlm_pred_a, encoded_a = self.encode_input(input_a["input_ids"])
            loss_qa, acc_qa = self.qa_loss(encoded_q, encoded_a)
            loss_mlm_q = self.mlm_loss(mlm_pred_q, input_q["labels"])
            loss_mlm_a = self.mlm_loss(mlm_pred_a, input_a["labels"])
            loss = loss_qa + loss_mlm_q + loss_mlm_a
            return loss, acc_qa


class QALMDataUtil(object):
    def __init__(self, seq_len=150, tokenizer_dir=None):
        self.seq_len = seq_len
        tokenizer_dir = tokenizer_dir or os.path.expanduser(
            "~/.zalearn/models/hf/bert-base-chinese")
        self.tokenizer = BertTokenizer.from_pretrained(tokenizer_dir)

    def get_loader(self, *texts_list, shuffle=False, drop_last=False, batch_size=10, do_mask=False,
                   device="cpu"):
        indices = [list(range(len(texts))) for texts in texts_list]

        # noinspection PyCallingNonCallable, PyUnresolvedReferences
        dataset = TensorDataset(*[torch.tensor(idx) for idx in indices])
        if shuffle:
            sampler = RandomSampler(dataset)
        else:
            sampler = SequentialSampler(dataset)
        dataloader = DataLoader(dataset, batch_size, sampler=sampler, drop_last=drop_last)

        data_collator = DataCollatorForLanguageModeling(self.tokenizer, mlm=do_mask)
        for tensors in dataloader:
            tensors = [[texts_list[ti][i] for i in tensor] for ti, tensor in enumerate(tensors)]
            tensors = [self.text_to_ids(texts) for texts in tensors]
            yield [{k: v.to(device) for k, v in data_collator(list(tensor)).items()}
                   for tensor in tensors]

    def text_to_ids(self, texts):
        input_ids = np.zeros((len(texts), self.seq_len), dtype=np.int32)
        for i, text in enumerate(texts):
            ids = self.tokenizer.encode(
                text, add_special_tokens=True, max_length=self.seq_len, truncation=True)
            input_ids[i, :len(ids)] = ids
        return input_ids

    def text_to_inputs(self, texts, device="cpu"):
        input_ids = self.text_to_ids(texts)
        # noinspection PyCallingNonCallable,PyUnresolvedReferences
        input_ids = torch.tensor(input_ids, dtype=torch.long).to(device)
        return {"input_ids": input_ids, "labels": input_ids}


class QALMTrainer(object):
    def __init__(self, model, init_lr, num_epochs, batch_size, data_util, device="cpu",
                 num_eval_steps=400, num_progress_steps=20):
        self.model = model
        self.init_lr = init_lr
        self.num_epochs = num_epochs
        self.batch_size = batch_size
        self.data_util = data_util
        self.device = device
        self.num_eval_steps = num_eval_steps
        self.num_progress_steps = num_progress_steps

    def _get_optimizer_scheduler(self, init_lr, num_training_steps, num_warmup_steps):
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_params = [
            {
                "params": [p for n, p in self.model.named_parameters()
                           if not any(nd in n for nd in no_decay)],
                "weight_decay": 0.01
            },
            {
                "params": [p for n, p in self.model.named_parameters()
                           if any(nd in n for nd in no_decay)],
                "weight_decay": 0.0
            }
        ]

        optimizer = AdamW(optimizer_params, lr=init_lr)
        scheduler = get_linear_schedule_with_warmup(
            optimizer, num_warmup_steps=num_warmup_steps,
            num_training_steps=num_training_steps
        )
        return optimizer, scheduler

    def eval(self, dev_qs, dev_as):
        self.model.to(self.device)
        self.model.eval()
        metrics = Metrics()
        for batch in self.data_util.get_loader(dev_qs, dev_as, shuffle=False,
                                               drop_last=True, device=self.device,
                                               batch_size=self.batch_size):
            loss, acc = self.model(*batch)
            metrics.add_metrics(loss=loss.item(), acc=acc.item())
        result = metrics.get_result()
        print("\n[EVAL]\t%s" % "\t".join(["%s=%f" % (k, v) for k, v in result.items()]))
        return result["acc"]

    def train(self, train_dialogs, dev_dialogs, save_path):
        train_qs, train_as = _dialog_to_qa(train_dialogs)
        num_training_steps = self.num_epochs * len(train_qs) // self.batch_size
        print("steps:", num_training_steps)

        optimizer, scheduler = self._get_optimizer_scheduler(
            self.init_lr, num_training_steps, num_warmup_steps=num_training_steps // 10)

        model = self.model.to(self.device)
        model.train()

        best_score = 0
        status = ProcessStatus(total_steps=num_training_steps, update_steps=self.num_progress_steps)
        dev_qs, dev_as = _dialog_to_qa(dev_dialogs)
        for epoch in range(self.num_epochs):
            random.shuffle(train_dialogs)
            train_qs, train_as = _dialog_to_qa(train_dialogs)

            for batch in self.data_util.get_loader(train_qs, train_as, shuffle=False,
                                                   drop_last=True, device=self.device,
                                                   batch_size=self.batch_size):
                loss, acc = model(*batch)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

                optimizer.step()
                scheduler.step()
                model.zero_grad()

                status.update(loss=loss.item(), acc=acc.item(), lr=scheduler.get_last_lr()[0])

                if status.global_step % self.num_eval_steps == 0:
                    eval_score = self.eval(dev_qs, dev_as)
                    if eval_score > best_score:
                        print("Training Improved:  score=%f step=%d" % (
                            eval_score, status.global_step))
                        torch.save(model.state_dict(), save_path)
                        best_score = eval_score
                    model.train()

        status.finish()


def _dialog_to_qa(dialogs):
    def uniq_with_order(seq):
        seen = set()
        seen_add = seen.add
        return [x for x in seq if not (x in seen or seen_add(x))]

    datas = []
    for dialog in dialogs:
        for offset in range(0, len(dialog), 2):
            if offset + 1 >= len(dialog):
                continue
            datas.append((dialog[offset], dialog[offset + 1]))
    datas = uniq_with_order(datas)
    input_qs, input_as = [list(_) for _ in zip(*datas)]
    return input_qs, input_as
