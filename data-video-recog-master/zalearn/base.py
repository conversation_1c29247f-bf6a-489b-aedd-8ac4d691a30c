from sklearn.base import BaseEstimator, ClassifierMixin
import numpy as np
import pandas as pd
from sklearn.metrics import roc_auc_score, accuracy_score, f1_score, classification_report, confusion_matrix
from lime.lime_text import LimeTextExplainer


class TextClassifier(BaseEstimator, ClassifierMixin):
    def __init__(self):
        super(TextClassifier, self).__init__()
        self.classes_ = None

    def fit(self, texts, labels):
        assert len(texts) == len(labels)
        return self

    def predict(self, texts) -> np.ndarray:
        pass

    def predict_proba(self, texts) -> np.ndarray:
        pass

    def rank_metrics(self, texts, labels):
        probs = self.predict_proba(texts)[:, 1]
        print("auc =", roc_auc_score(labels, probs))

        sort_indices = np.argsort(-probs)
        output = []
        for label_rate in np.arange(0.1, 1.1, 0.1):
            label_num = min(int(len(labels) * label_rate), len(labels) - 1)

            threshold = probs[sort_indices[label_num]]
            __preds = [1.0] * label_num
            __y_test = labels[sort_indices[:label_num]]

            recall_rate = sum(__y_test) / sum(labels)
            recall_num = sum(__y_test)

            output.append([threshold, label_rate, label_num, recall_rate, recall_num])
        return pd.DataFrame(output, columns=["threshold", "label_rate", "label_num", "recall_rate", "recall_num"])

    def classify_metrics(self, texts, labels):
        preds = self.predict(texts)
        print("f1 =", f1_score(labels, preds, average="macro"))
        print("acc =", accuracy_score(labels, preds))
        print(classification_report(labels, preds, digits=4))
        # noinspection PyBroadException
        try:
            return pd.DataFrame(confusion_matrix(labels, preds), columns=self.classes_, index=self.classes_)
        except:
            return pd.DataFrame(confusion_matrix(labels, preds))

    def explain(self, text, num_features=20, tokenizer=r'\W+'):
        explainer = LimeTextExplainer(class_names=self.classes_, split_expression=tokenizer)
        exp = explainer.explain_instance(text, self.predict_proba, num_features=num_features, top_labels=1)
        for w, s in sorted(exp.as_list(label=exp.available_labels()[0]), key=lambda x: -x[1]):
            print("%.4f\t%s" % (s, w))
        exp.show_in_notebook()
        return exp
