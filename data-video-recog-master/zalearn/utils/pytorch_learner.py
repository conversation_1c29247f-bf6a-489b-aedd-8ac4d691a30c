import numpy as np
import torch
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>, RandomSampler, SequentialSampler, TensorDataset
from sklearn.utils.extmath import softmax
from transformers import AdamW, get_linear_schedule_with_warmup
from zalearn.utils.train_utils import ProcessStatus, procstatus


class PytorchLearner(object):
    def __init__(self, init_lr, num_epochs, batch_size, is_hf=False):
        self.init_lr = init_lr
        self.num_epochs = num_epochs
        self.batch_size = batch_size
        self.is_hf = is_hf
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    def to_data_loader(self, *inputs, shuffle=False, drop_last=False):
        # noinspection PyCallingNonCallable, PyUnresolvedReferences
        dataset = TensorDataset(*[torch.tensor(x, dtype=torch.long) for x in inputs])
        if shuffle:
            train_sampler = RandomSampler(dataset)
        else:
            train_sampler = SequentialSampler(dataset)
        dataloader = DataLoader(dataset, self.batch_size, sampler=train_sampler,
                                drop_last=drop_last)
        return dataloader

    @staticmethod
    def get_optimizer_scheduler(model, init_lr, num_training_steps, num_warmup_steps):
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_params = [
            {
                "params": [p for n, p in model.named_parameters()
                           if not any(nd in n for nd in no_decay)],
                "weight_decay": 0.01
            },
            {
                "params": [p for n, p in model.named_parameters()
                           if any(nd in n for nd in no_decay)],
                "weight_decay": 0.0
            }
        ]

        optimizer = AdamW(optimizer_params, lr=init_lr)
        scheduler = get_linear_schedule_with_warmup(
            optimizer, num_warmup_steps=num_warmup_steps,
            num_training_steps=num_training_steps
        )
        return optimizer, scheduler

    def train(self, texts, labels, model):
        num_training_steps = self.num_epochs * len(texts) // self.batch_size
        print("steps:", num_training_steps)
        num_warmup_steps = num_training_steps // 10 if self.is_hf else 0
        optimizer, scheduler = self.get_optimizer_scheduler(
            model, self.init_lr, num_training_steps, num_warmup_steps)

        model.to(self.device)
        model.train()

        if self.is_hf:
            def loss_fn(_input_ids, _label_ids):
                return model(input_ids=_input_ids, labels=_label_ids)[0]
        else:
            criterion = torch.nn.CrossEntropyLoss().to(self.device)

            def loss_fn(_input_ids, _label_ids):
                pred = model(input_ids=_input_ids)
                return criterion(pred, _label_ids)

        status = ProcessStatus(total_steps=num_training_steps)
        for epoch in range(self.num_epochs):
            for step, batch in enumerate(
                    self.to_data_loader(texts, labels, shuffle=True, drop_last=True)):
                input_ids, label_ids = [t.to(self.device) for t in batch]
                loss = loss_fn(input_ids, label_ids)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

                optimizer.step()
                scheduler.step()
                model.zero_grad()
                status.update(loss=loss.item(), lr=scheduler.get_last_lr()[0])
        status.finish()

    def predict(self, texts, model):
        probs = self.predict_proba(texts, model)
        return np.argmax(probs, axis=1)

    def predict_proba(self, texts, model):
        model.to(self.device)
        model = model.eval()

        all_probs = []
        for batch in procstatus(self.to_data_loader(texts)):
            input_ids, = [t.to(self.device) for t in batch]

            with torch.no_grad():
                if self.is_hf:
                    logits = model(input_ids=input_ids)[0]
                else:
                    logits = model(input_ids=input_ids)
            logits = logits.detach().cpu().numpy()
            probs = softmax(logits)
            all_probs.extend(probs)
        return all_probs
