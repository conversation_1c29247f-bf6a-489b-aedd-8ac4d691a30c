import sys
import time
import inspect
import numpy as np


class Metrics(object):
    def __init__(self):
        self.metrics = {}
        self.cnt = {}

    def add_metrics(self, **kwargs):
        for k, v in kwargs.items():
            if isinstance(v, (tuple, list)) and len(v) == 2:
                v, cnt = v
            else:
                cnt = 1
            self.metrics.setdefault(k, [])
            self.metrics[k].append(v)
            self.cnt.setdefault(k, [])
            self.cnt[k].append(cnt)

    def get_result(self):
        output = {}
        for k, vs in self.metrics.items():
            if len(vs) == 0:
                output[k] = 0
            else:
                output[k] = np.sum(vs) / np.sum(self.cnt[k])

        for k in self.metrics:
            self.metrics[k] = []
            self.cnt[k] = []
        return output

    def formated_result(self):
        output = self.get_result()
        return "\t".join(["%s=%f" % (k, v) for k, v in output.items()])

    def size(self):
        values = list(self.metrics.values())
        if len(values) == 0:
            return 0
        else:
            return len(values[0])


class ProcessStatus(object):
    enabled = True

    def __init__(self, total_steps, task=None, update_steps=10):
        self.task = task
        self.total_steps = total_steps
        self.update_steps = update_steps
        self.global_step = 0
        self.metrics = Metrics()
        self.begin_time = time.time()
        if self.task is None:
            caller_frame = inspect.stack()[1]
            self.task = "%s.%s:%s" % (inspect.getmodule(caller_frame[0]).__name__,
                                      caller_frame.function, caller_frame.lineno)

    def update(self, **kwargs):
        self.metrics.add_metrics(**kwargs)

        self.global_step += 1
        if self.global_step % self.update_steps != 0:
            return

        percentage = 100 * self.global_step // self.total_steps
        seconds = int(time.time() - self.begin_time)
        m, s = divmod(seconds, 60)
        h, m = divmod(m, 60)

        if self.enabled:
            sys.stdout.write(
                "\r[%s]\t%d%%\ttime=%02d:%02d:%02d\tstep=%d\t%s" %
                (self.task, percentage,
                 h, m, s, self.global_step,
                 self.metrics.formated_result()))
            sys.stdout.flush()

    def finish(self):
        if self.enabled:
            sys.stdout.write("\n")

    @classmethod
    def disable(cls):
        cls.enabled = False

    @classmethod
    def enable(cls):
        cls.enabled = True


def procstatus(list_, name=None, update_steps=10):
    if name is None:
        caller_frame = inspect.stack()[1]
        name = "%s.%s:%s" % (inspect.getmodule(caller_frame[0]).__name__,
                             caller_frame.function, caller_frame.lineno)

    status = ProcessStatus(task=name, total_steps=len(list_), update_steps=update_steps)
    for x in list_:
        status.update()
        yield x
    status.finish()
