import inspect
import multiprocessing
from collections import Counter
import jieba as jieba
from zalearn.utils.train_utils import ProcessStatus


def _count_word(texts):
    counter = Counter()
    for ws in texts:
        counter.update(ws)
    return counter


def _jieba_cut(text):
    return " ".join(jieba.lcut(text))


def _jieba_cut_as_list(text):
    return jieba.lcut(text)


class JobRuner(object):
    _pool = None

    @classmethod
    def disable_parallel(cls):
        if cls._pool:
            cls._pool.close()
            cls._pool = None

    @classmethod
    def enable_parallel(cls, ncpus=None):
        ncpus = ncpus or multiprocessing.cpu_count()
        cls._pool = multiprocessing.Pool(ncpus)

    @classmethod
    def run(cls, func, inputs):
        caller_frame = inspect.stack()[1]
        name = "%s.%s:%s" % (inspect.getmodule(caller_frame[0]).__name__,
                             caller_frame.function, caller_frame.lineno)
        status = ProcessStatus(task=name, total_steps=len(inputs))

        outputs = []
        if cls._pool is not None:
            for output in cls._pool.imap_unordered(func, inputs):
                outputs.append(output)
                status.update()
        else:
            for output in map(func, inputs):
                outputs.append(output)
                status.update()
        status.finish()
        return outputs

    @classmethod
    def jieba_cut(cls, texts):
        return cls.run(_jieba_cut, texts)

    @classmethod
    def jieba_cut_as_list(cls, texts):
        return cls.run(_jieba_cut_as_list, texts)

    @classmethod
    def count_word(cls, texts):
        n_splits = 100
        texts_list = [[t for i, t in enumerate(texts) if i % n_splits == offset]
                      for offset in range(n_splits)]
        counters = cls.run(_count_word, texts_list)

        word_cnts = Counter()
        for counter in counters:
            word_cnts.update(counter)
        return word_cnts
