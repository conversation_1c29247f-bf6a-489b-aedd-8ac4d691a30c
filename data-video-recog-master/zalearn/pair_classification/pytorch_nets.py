import torch.nn as nn


# noinspection PyAbstractClass
class ComplaintCNN(nn.Module):
    def __init__(self, num_classes, vocab_size,
                 embedding_size=32, hidden_size=64,
                 drop=0.0):
        super(ComplaintCNN, self).__init__()
        assert embedding_size % 4 == 0
        kernel_size = (2, 2)

        self.embedding_layer = nn.Embedding(vocab_size, embedding_size)
        self.conv1 = nn.Conv2d(1, hidden_size, kernel_size)
        self.conv2 = nn.Conv2d(hidden_size, hidden_size, kernel_size)
        self.max_pooling = nn.MaxPool2d(kernel_size)
        self.flatten = nn.Flatten(start_dim=1)

        kernel_h, kernel_w = kernel_size
        final_conv_h = 1
        final_conv_w = ((embedding_size // 4 - kernel_h // 2) // 2 - kernel_h // 2) // 2

        self.fc1 = nn.Linear(hidden_size * final_conv_h * final_conv_w, 512)
        self.fc2 = nn.Linear(512, 128)
        self.fc3 = nn.Linear(128, num_classes)

        self.drop = nn.Dropout(drop)
        self.relu = nn.ReLU()

    def forward(self, input_ids):
        # awe
        batch_size = input_ids.size(0)
        embedding = self.embedding_layer(input_ids)  # batch X 2 X seq_len X emb_size
        mean_pooled = embedding.mean(dim=2)  # batch X 2 X emb_size

        # convs
        output = mean_pooled.reshape([batch_size, 1, 8, -1])  # batch X 1 X 8 X (emb_size/4)
        output = self.conv1(output)  # batch X hidden_size X 7 X (emb_size/4-1)
        output = self.max_pooling(output)  # batch X hidden_size X 3 X (emb_size/4-1)/2
        output = self.conv2(output)  # batch X hidden_size X 2 X (emb_size/4-1)/2-1
        output = self.max_pooling(output)  # batch X hidden_size X 1 X [(emb_size/4-1)/2-1]/2
        output = self.flatten(output)  # batch X hidden_size*[(emb_size/4-1)/2-1]/2

        # fcs
        output = self.fc1(output)
        output = self.relu(output)

        output = self.fc2(output)
        output = self.relu(output)
        output = self.drop(output)

        return self.fc3(output)
