import os
import sys
import joblib
from tqdm import trange
import numpy as np


def main():
    if len(sys.argv) < 2:
        print("%s [sent2vec|clustering] ..." % sys.argv[0])
        quit(-1)

    if sys.argv[1] == "sent2vec":
        return sent2vec()
    elif sys.argv[1] == "clustering":
        return clustering()
    elif sys.argv[1] == "sort":
        return sort()
    else:
        print("%s [sent2vec|clustering|sort] ..." % sys.argv[0])
        quit(-1)


def sent2vec():
    args = ["@", "sent2vec", "sent_file", "vec_file"]
    if len(sys.argv) < len(args):
        args[0] = sys.argv[0]
        print(" ".join(args))
        quit(-1)
    else:
        args = dict(zip(args, sys.argv))

    if len(sys.argv) < len(args) + 1:
        model_path = os.path.expanduser("~/work/models/dialogs/lm/mix_tiny_10.pkl")
    else:
        model_path = sys.argv[len(args)]

    with open(args["sent_file"]) as fin:
        sents = [_.rstrip("\n") for _ in fin]

    model = joblib.load(model_path)
    with open(args["vec_file"], "w") as fout:
        batch_size = 10000
        for offset in trange(0, len(sents), batch_size, desc='sent2vec'):
            vecs = model.predict(sents[offset: offset + batch_size])
            for v in vecs:
                fout.write(",".join([str(x) for x in v]) + "\n")


def clustering():
    from sklearn.cluster import MiniBatchKMeans as Kmeans
    args = ["@", "clustering", "vec_file", "cluster_file", "n_clusters"]
    if len(sys.argv) < len(args):
        args[0] = sys.argv[0]
        print(" ".join(args))
        quit(-1)
    else:
        args = dict(zip(args, sys.argv))

    def std(data):
        mu = np.mean(data, axis=0)
        sigma = np.std(data, axis=0)
        return (data - mu) / sigma

    with open(args["vec_file"]) as fin:
        vecs = np.array([np.fromstring(_, sep=",") for _ in fin])

    vecs = std(vecs)
    n_clusters = int(args["n_clusters"])
    km = Kmeans(n_clusters=n_clusters, init_size=max(300, n_clusters * 3))
    clusters = km.fit_predict(vecs)
    with open(args["cluster_file"], "w") as fout:
        for c in clusters:
            fout.write("%s\n" % c)


def sort():
    from sklearn.metrics import pairwise_distances
    from sklearn.manifold import TSNE

    args = ["@", "sort", "vec_file", "order_file"]
    if len(sys.argv) < len(args):
        args[0] = sys.argv[0]
        print(" ".join(args))
        quit(-1)
    else:
        args = dict(zip(args, sys.argv))

    with open(args["vec_file"]) as fin:
        vecs = np.array([np.fromstring(_, sep=",") for _ in fin])

    def vec_to_1d(data):
        if len(data) == 0:
            return None

        if len(data) == 1:
            return 0

        distance_matrix = pairwise_distances(data, data, metric='euclidean', n_jobs=-1)
        mapper = TSNE(n_components=1, random_state=0, metric="precomputed", init="random")
        return mapper.fit_transform(distance_matrix)[:, 0]

    orders = vec_to_1d(vecs)
    with open(args["order_file"], "w") as fout:
        for c in orders:
            fout.write("%s\n" % c)


def sort():
    from sklearn.metrics import pairwise_distances
    from sklearn.manifold import TSNE

    args = ["@", "sort", "vec_file", "order_file"]
    if len(sys.argv) < len(args):
        args[0] = sys.argv[0]
        print(" ".join(args))
        quit(-1)
    else:
        args = dict(zip(args, sys.argv))

    with open(args["vec_file"]) as fin:
        vecs = np.array([np.fromstring(_, sep=",") for _ in fin])

    def vec_to_1d(data):
        if len(data) == 0:
            return None

        if len(data) == 1:
            return 0

        distance_matrix = pairwise_distances(data, data, metric='euclidean', n_jobs=-1)
        mapper = TSNE(n_components=1, random_state=0, metric="precomputed", init="random")
        return mapper.fit_transform(distance_matrix)[:, 0]

    orders = vec_to_1d(vecs)
    with open(args["order_file"], "w") as fout:
        for c in orders:
            fout.write("%s\n" % c)

#

#