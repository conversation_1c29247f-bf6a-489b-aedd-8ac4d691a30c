import random
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from zalearn.base import TextClassifier
import cjieba as jieba
import numpy as np


class TfidfLRClassifier(TextClassifier):
    def __init__(self, num_epochs=100, tokenizer=None, max_features=2000, **kwargs):
        super(TfidfLRClassifier, self).__init__()
        self.tokenizer = tokenizer
        self.kwargs = kwargs
        self.model = None
        self.tfidf = TfidfVectorizer(
            max_features=max_features, analyzer="word" if tokenizer else "char")

        kwargs.setdefault("solver", "liblinear")
        kwargs.setdefault("multi_class", "ovr")
        kwargs.setdefault("class_weight", "balanced")
        self.lr = LogisticRegression(max_iter=num_epochs, **kwargs)

    def fit(self, texts, labels):
        texts = self._split(texts)

        self.tfidf.fit(random.choices(texts, k=10000))
        texts = self.tfidf.transform(texts)

        self.lr.fit(texts, labels)
        self.classes_ = self.lr.classes_
        return self

    def predict(self, texts):
        texts = self._split(texts)
        texts = self.tfidf.transform(texts)
        return self.lr.predict(texts)

    def predict_proba(self, texts):
        texts = self._split(texts)
        texts = self.tfidf.transform(texts)
        return self.lr.predict_proba(texts)

    def _split(self, texts):
        if self.tokenizer == "jieba":
            return list(map(" ".join, map(jieba.lcut, texts)))
        else:
            return texts


class SeqTfidfLRClassifier(TextClassifier):
    def __init__(self, num_epochs=100, tokenizer=None, max_features=4000, **kwargs):
        super(SeqTfidfLRClassifier, self).__init__()
        self.num_epochs = num_epochs
        self.tokenizer = tokenizer
        self.max_features = max_features
        self.kwargs = kwargs

        kwargs.setdefault("solver", "liblinear")
        kwargs.setdefault("multi_class", "ovr")
        kwargs.setdefault("class_weight", "balanced")

        self.tfidf = None
        self.lr = LogisticRegression(max_iter=num_epochs, **kwargs)

    def fit(self, seq, labels):
        seq = [list(_) for _ in zip(*seq)]  # records to seqs
        seq = [self._split(texts) for texts in seq]
        all_texts = sum(seq, [])

        seq_len = len(seq)
        self.tfidf = TfidfVectorizer(
            max_features=self.max_features // seq_len, analyzer="word" if self.tokenizer else "char")
        self.tfidf.fit(random.choices(all_texts, k=10000))

        seq = [self.tfidf.transform(tokens).A for tokens in seq]
        features = np.concatenate(seq, axis=1)

        self.lr.fit(features, labels)
        self.classes_ = self.lr.classes_
        return self

    def predict(self, seq):
        seq = list(zip(*seq))  # records to seqs
        seq = [self._split(texts) for texts in seq]
        seq = [self.tfidf.transform(tokens).A for tokens in seq]
        features = np.concatenate(seq, axis=1)
        return self.lr.predict(features)

    def predict_proba(self, seq):
        seq = list(zip(*seq))  # records to seqs
        seq = [self._split(texts) for texts in seq]
        seq = [self.tfidf.transform(tokens).A for tokens in seq]
        features = np.concatenate(seq, axis=1)
        return self.lr.predict_proba(features)

    def _split(self, texts):
        if self.tokenizer == "jieba":
            return list(map(" ".join, map(jieba.lcut, texts)))
        else:
            return texts
