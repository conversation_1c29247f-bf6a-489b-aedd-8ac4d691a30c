import tempfile
import fasttext
import numpy as np
from zalearn.base import TextClassifier
from zalearn.utils.multicore_utils import JobRuner


class FasttextClassifier(TextClassifier):
    def __init__(self, num_epochs=100, tokenizer="jieba", **kwargs):
        super(FasttextClassifier, self).__init__()
        self.tokenizer = tokenizer
        self.kwargs = kwargs
        self.kwargs["epoch"] = num_epochs
        self.label_prefix = self.kwargs.get("label", "__label__")
        self.model = None
        self.classes_ = None
        self.class_type_ = None

    def fit(self, texts, labels):
        with tempfile.NamedTemporaryFile(mode="w") as fout:
            self._dump_file(texts, labels, fout)
            self.model = fasttext.train_supervised(fout.name, **self.kwargs)
            self.classes_ = sorted(set(labels))
            self.class_type_ = type(self.classes_[0])
        return self

    def _raw_predict(self, texts, k=1):
        texts = [t.replace("\n", "") for t in texts]
        texts = self._split(texts)
        preds, probs = self.model.predict(texts, k=k)
        preds = [
            [self.class_type_(pred.replace(self.label_prefix, "")) for pred in preds_]
            for preds_ in preds
        ]
        return preds, probs

    def predict(self, texts):
        preds, _ = self._raw_predict(texts, k=1)
        return np.asarray([preds_[0] for preds_ in preds])

    def predict_proba(self, texts):
        preds, probs = self._raw_predict(texts, k=len(self.classes_))
        sorted_probs = []
        for preds_, probs_ in zip(preds, probs):
            pred2prob = dict(zip(preds_, probs_))
            probs_ = [pred2prob.get(c, 0.0) for c in self.classes_]
            sorted_probs.append(probs_)
        return np.asarray(sorted_probs)

    def _dump_file(self, texts, labels, fout):
        num_samples = len(texts)
        batch_size = 100000

        for offset in range(0, num_samples, batch_size):
            sub_texts = texts[offset: offset + batch_size]
            sub_labels = labels[offset: offset + batch_size]

            sub_texts = [t.replace("\n", "") for t in sub_texts]
            sub_texts = self._split(sub_texts)
            for text, label in zip(sub_texts, sub_labels):
                fout.write("%s\t%s%s\n" % (text, self.label_prefix, label))

    def _split(self, texts):
        if self.tokenizer == "jieba":
            return JobRuner.jieba_cut(texts)
        else:
            return [" ".join(text) for text in texts]

    def __getstate__(self):
        params = self.__dict__
        with tempfile.NamedTemporaryFile() as fout:
            self.model.save_model(fout.name)
            model = open(fout.name, 'rb').read()
        params["model"] = model
        return params

    def __setstate__(self, state):
        self.__dict__.update(state)
        with tempfile.NamedTemporaryFile() as fout:
            fout.write(self.model)
            self.model = fasttext.load_model(fout.name)
