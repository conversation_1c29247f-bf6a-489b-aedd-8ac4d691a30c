import os
import numpy as np
from transformers import logging
from transformers import <PERSON>Tokenizer, AutoModelForSequenceClassification
from transformers import <PERSON><PERSON>oken<PERSON>, BertForSequenceClassification
from transformers import RobertaTokenizer
from zalearn.base import TextClassifier
from zalearn.utils.train_utils import procstatus
from zalearn.utils.pytorch_learner import PytorchLearner


logging.set_verbosity_error()
MODEL_DIR_BASE = os.path.expanduser("~/.zalearn/models/hf/")


class LabelIDEncoder(object):
    def __init__(self):
        self.labels = None

    def fit(self, labels):
        self.labels = list(set(labels))
        return self

    def transform(self, labels):
        return [self.labels.index(t) for t in labels]

    @property
    def num_classes(self):
        return len(self.labels)

    def __getitem__(self, key):
        return self.labels[key]


# noinspection PyBroadException
class HFTransformerClassifier(TextClassifier):
    def __init__(self, num_epochs=10, init_lr=1e-2, batch_size=8, max_len=150,
                 model_dir=None, model_type=None, **kwargs):
        super(HFTransformerClassifier).__init__()
        self.max_len = max_len
        self.model_dir = model_dir
        self.model_type = model_type
        self.kwargs = kwargs

        if self.model_type == "bert":
            self.textenc = BertTokenizer.from_pretrained(model_dir)
        elif self.model_type == "roberta":
            self.textenc = RobertaTokenizer.from_pretrained(model_dir)
        else:
            self.textenc = AutoTokenizer.from_pretrained(model_dir)

        self.net_learner = PytorchLearner(init_lr, num_epochs, batch_size, is_hf=True)
        self.net = None
        self.labelenc = None

    def fit(self, texts, labels):
        self.labelenc = LabelIDEncoder().fit(labels)
        self.classes_ = self.labelenc.labels
        if self.model_type == "bert":
            self.net = BertForSequenceClassification.from_pretrained(
                self.model_dir, num_labels=self.labelenc.num_classes)
        else:
            self.net = AutoModelForSequenceClassification.from_pretrained(
                self.model_dir, num_labels=self.labelenc.num_classes)

        texts = self.encode(texts)
        labels = self.labelenc.transform(labels)
        self.net_learner.train(texts, labels, self.net)

        return self

    def predict(self, texts):
        texts = self.encode(texts)
        preds = self.net_learner.predict(texts, self.net)
        return np.asarray([self.labelenc[p] for p in preds])

    def predict_proba(self, texts):
        texts = self.encode(texts)
        return np.asarray(self.net_learner.predict_proba(texts, self.net))

    def encode(self, texts):
        outputs = np.zeros((len(texts), self.max_len), dtype=np.int32)
        for i, text in enumerate(procstatus(texts)):
            ids = self.textenc.encode(text, add_special_tokens=True,
                                      max_length=self.max_len, truncation=True)
            outputs[i, :len(ids)] = ids
        return outputs


class BertClassifier(HFTransformerClassifier):
    def __init__(self, num_epochs=3, init_lr=5e-5, batch_size=8, max_len=150,
                 model_dir=None, **kwargs):
        model_dir = model_dir or MODEL_DIR_BASE + "/google/bert-base-chinese"
        super(BertClassifier, self).__init__(
            num_epochs, init_lr, batch_size, max_len, model_dir, **kwargs)


class BertInsurClassifier(HFTransformerClassifier):
    def __init__(self, num_epochs=3, init_lr=5e-5, batch_size=8, max_len=150,
                 model_dir=None, **kwargs):
        model_dir = model_dir or MODEL_DIR_BASE + "/zhongan/bert-base-insur"
        super(BertInsurClassifier, self).__init__(
            num_epochs, init_lr, batch_size, max_len, model_dir, **kwargs)


class RobertaClassifier(HFTransformerClassifier):
    def __init__(self, num_epochs=3, init_lr=5e-5, batch_size=8, max_len=150,
                 model_dir=None, **kwargs):
        model_dir = model_dir or MODEL_DIR_BASE + "/hfl/chinese-roberta-wwm-ext"
        super(RobertaClassifier, self).__init__(
            num_epochs, init_lr, batch_size, max_len, model_dir, model_type="bert", **kwargs)


class ErnieClassifier(HFTransformerClassifier):
    def __init__(self, num_epochs=3, init_lr=1.1e-4, batch_size=8, max_len=150,
                 model_dir=None, **kwargs):
        model_dir = model_dir or MODEL_DIR_BASE + "/baidu/ernie2"
        super(ErnieClassifier, self).__init__(
            num_epochs, init_lr, batch_size, max_len, model_dir, model_type="bert", **kwargs)


class XlnetClassifier(HFTransformerClassifier):
    def __init__(self, num_epochs=3, init_lr=5e-5, batch_size=8, max_len=150,
                 model_dir=None, **kwargs):
        model_dir = model_dir or MODEL_DIR_BASE + "/hfl/chinese-xlnet-base"
        super(XlnetClassifier, self).__init__(
            num_epochs, init_lr, batch_size, max_len, model_dir, **kwargs)
