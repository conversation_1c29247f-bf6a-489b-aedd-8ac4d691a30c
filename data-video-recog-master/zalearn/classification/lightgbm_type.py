import numpy as np
import pandas as pd
from lightgbm.sklearn import LGBMClassifier
from sklearn.metrics import roc_auc_score, accuracy_score, f1_score, classification_report, confusion_matrix
from typing import Callable, Dict, Optional, Union


class LGBMExtClassifier(LGBMClassifier):
    def __init__(
            self,
            boosting_type: str = 'gbdt',
            num_leaves: int = 31,
            max_depth: int = -1,
            learning_rate: float = 0.1,
            n_estimators: int = 100,
            subsample_for_bin: int = 200000,
            objective: Optional[Union[str, Callable]] = None,
            class_weight: Optional[Union[Dict, str]] = None,
            min_split_gain: float = 0.,
            min_child_weight: float = 1e-3,
            min_child_samples: int = 20,
            subsample: float = 1.,
            subsample_freq: int = 0,
            colsample_bytree: float = 1.,
            reg_alpha: float = 0.,
            reg_lambda: float = 0.,
            random_state: Optional[Union[int, np.random.RandomState]] = None,
            n_jobs: int = -1,
            silent: Union[bool, str] = 'warn',
            importance_type: str = 'split',
            **kwargs
    ):
        super(LGBMExtClassifier, self).__init__(
            boosting_type, num_leaves, max_depth, learning_rate, n_estimators, subsample_for_bin, objective,
            class_weight, min_split_gain, min_child_weight, min_child_samples, subsample, subsample_freq,
            colsample_bytree, reg_alpha, reg_lambda, random_state, n_jobs, silent, importance_type, **kwargs)
        self.float_cols = []
        self.cate_cols = []
        self.skip_cols = []
        self.cate_map = {}

    def fit(self, x: pd.DataFrame, y,
            sample_weight=None, init_score=None,
            eval_set=None, eval_names=None, eval_sample_weight=None,
            eval_class_weight=None, eval_init_score=None, eval_metric=None,
            early_stopping_rounds=None, verbose=True,
            feature_name='auto', categorical_feature='auto', callbacks=None,
            col_max_values=50, split_eval_radio=None):

        for col in list(x):
            if all([self._isnumber(v) for v in x[col].values]):
                self.float_cols.append(col)
            elif x[col].nunique() <= col_max_values:
                self.cate_cols.append(col)
            else:
                self.skip_cols.append(col)

        for col in self.cate_cols:
            mapping = {v: i for i, v in enumerate(x[col].value_counts().index) if v not in ("",)}
            self.cate_map[col] = mapping

        if split_eval_radio and eval_set is None:
            indices = np.arange(len(x))
            np.random.shuffle(indices)
            num_evals = int(len(indices) * split_eval_radio)
            eval_set = [(x.loc[indices[:num_evals]], y[indices[:num_evals]])]
            x, y = x.loc[indices[num_evals:]], y[indices[num_evals:]]

        if eval_set is not None:
            eval_set = [(self.feature_preprocess(x), y) for x, y in eval_set]

        categorical_indices = [(self.cate_cols + self.float_cols).index(col) for col in self.cate_cols]
        super(LGBMExtClassifier, self).fit(
            self.feature_preprocess(x), y,
            sample_weight, init_score,
            eval_set, eval_names, eval_sample_weight,
            eval_class_weight, eval_init_score, eval_metric,
            early_stopping_rounds, verbose,
            feature_name, categorical_indices,
            callbacks
        )

    def predict(self, x, raw_score=False, start_iteration=0, num_iteration=None,
                pred_leaf=False, pred_contrib=False, **kwargs):
        return super(LGBMExtClassifier, self).predict(
            self.feature_preprocess(x),
            raw_score, num_iteration, pred_leaf, pred_contrib, **kwargs)

    def predict_proba(self, x, raw_score=False, start_iteration=0, num_iteration=None,
                      pred_leaf=False, pred_contrib=False, **kwargs):
        return super(LGBMExtClassifier, self).predict_proba(
            self.feature_preprocess(x),
            raw_score, num_iteration, pred_leaf, pred_contrib, **kwargs)

    def feature_preprocess(self, x):
        for col in self.float_cols:
            x.loc[:, col] = x[col].astype(float)
        for col in self.cate_cols:
            x.loc[:, col] = x[col].map(self.cate_map[col])
        return x[self.cate_cols + self.float_cols]

    # noinspection PyBroadException
    @classmethod
    def _isnumber(cls, v):
        if v is None:
            return True
        elif isinstance(v, (float, int, np.int64, np.float64)):
            return True
        elif isinstance(v, (str, bytes)) and v.isdigit():
            return True
        try:
            float(v)
            return True
        except Exception as _:
            return False

    def feature_importances(self, min_value=1):
        output = []
        coef = self.feature_importances_
        fields = self.cate_cols + self.float_cols
        sort_indices = np.argsort(-coef)
        for i in sort_indices:
            if coef[i] < min_value:
                break
            output.append([fields[i], coef[i]])
        return pd.DataFrame(output, columns=["col", "imp"])

    def rank_metrics(self, x_test, y_test):
        probs = self.predict_proba(x_test)[:, 1]
        print("auc =", roc_auc_score(y_test, probs))

        sort_indices = np.argsort(-probs)
        output = []
        for label_rate in np.arange(0.1, 1.1, 0.1):
            label_num = min(int(len(y_test) * label_rate), len(y_test) - 1)

            threshold = probs[sort_indices[label_num]]
            __preds = [1.0] * label_num
            __y_test = y_test[sort_indices[:label_num]]

            recall_rate = sum(__y_test) / sum(y_test)
            recall_num = sum(__y_test)

            output.append([threshold, label_rate, label_num, recall_rate, recall_num])
        return pd.DataFrame(output, columns=["threshold", "label_rate", "label_num", "recall_rate", "recall_num"])

    def classify_metrics(self, x_test, y_test):
        preds = self.predict(x_test)
        print("f1 =", f1_score(y_test, preds, average="macro"))
        print("acc =", accuracy_score(y_test, preds))
        print(classification_report(y_test, preds, digits=4))
        return pd.DataFrame(confusion_matrix(y_test, preds), columns=self.classes_, index=self.classes_)
