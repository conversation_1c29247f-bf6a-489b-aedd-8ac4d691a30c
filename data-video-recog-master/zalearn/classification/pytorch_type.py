import numpy as np
from zalearn.base import TextClassifier
from zalearn.utils.train_utils import procstatus
from zalearn.utils.pytorch_learner import PytorchLearner
from zalearn.utils.multicore_utils import JobRuner
from zalearn.classification import pytorch_nets as nets


class TextIDEncoder(object):
    def __init__(self, seq_len, tokenizer="jieba", vocab_capicity=10000):
        self.seq_len = seq_len
        self.tokenizer = tokenizer
        self.vocab_capicity = vocab_capicity
        self.vocabs = None

    def fit(self, texts):
        texts = self._split(texts)  # list(map(self._split, texts))

        word_cnts = JobRuner.count_word(texts)
        word_cnts = word_cnts.most_common(self.vocab_capicity)

        vocabs = {c[0]: i + 2 for i, c in enumerate(word_cnts)}
        vocabs['PAD'] = 0
        vocabs["UNK"] = 1
        self.vocabs = vocabs
        return self

    def transform(self, texts):
        texts = self._split(texts)
        outputs = np.zeros((len(texts), self.seq_len), dtype=np.int32)
        for i, text in enumerate(procstatus(texts)):
            ids = [self.vocabs.get(w, self.vocabs["UNK"]) for w in text]
            ids = ids[:self.seq_len]
            outputs[i, :len(ids)] = ids
        return outputs

    @property
    def vocab_size(self):
        return len(self.vocabs)

    def _split(self, texts):
        if self.tokenizer == "jieba":
            return JobRuner.jieba_cut_as_list(texts)
        else:
            return texts


class LabelIDEncoder(object):
    def __init__(self):
        self.labels = None

    def fit(self, labels):
        self.labels = list(set(labels))
        return self

    def transform(self, labels):
        return [self.labels.index(t) for t in labels]

    @property
    def num_classes(self):
        return len(self.labels)

    def __getitem__(self, key):
        return self.labels[key]


class NNClassifier(TextClassifier):
    def __init__(self, num_epochs=10, init_lr=1e-2, batch_size=8, max_len=150,
                 tokenizer=None, **kwargs):
        super(NNClassifier, self).__init__()
        self.max_len = max_len
        self.tokenizer = tokenizer
        self.kwargs = kwargs

        self.net_learner = PytorchLearner(init_lr, num_epochs, batch_size)
        self.net = None
        self.labelenc = None
        self.textenc = None

    def fit(self, texts, labels):
        self.labelenc = LabelIDEncoder().fit(labels)
        self.classes_ = self.labelenc.labels
        self.textenc = TextIDEncoder(self.max_len, self.tokenizer).fit(texts)
        texts = self.textenc.transform(texts)
        labels = self.labelenc.transform(labels)

        self.net = self.get_net()
        self.net_learner.train(texts, labels, self.net)
        return self

    def predict(self, texts):
        texts = self.textenc.transform(texts)
        preds = self.net_learner.predict(texts, self.net)
        return np.asarray([self.labelenc[p] for p in preds])

    def predict_proba(self, texts):
        texts = self.textenc.transform(texts)
        return np.asarray(self.net_learner.predict_proba(texts, self.net))

    def get_net(self):
        raise NotImplementedError


class TextCNNClassifier(NNClassifier):
    def get_net(self):
        return nets.TextCNN(
            num_classes=self.labelenc.num_classes,
            vocab_size=self.textenc.vocab_size,
            **self.kwargs
        )


class TextRNNClassifier(NNClassifier):
    def get_net(self):
        return nets.TextRNN(
            num_classes=self.labelenc.num_classes,
            vocab_size=self.textenc.vocab_capicity,
            **self.kwargs
        )


class TextDANClassifier(NNClassifier):
    def get_net(self):
        return nets.TextDAN(
            num_classes=self.labelenc.num_classes,
            vocab_size=self.textenc.vocab_size,
            **self.kwargs
        )


class TextTransfomerClassifier(NNClassifier):
    def __init__(self, num_epochs=10, init_lr=5e-4, batch_size=8, max_len=150,
                 tokenizer=None, **kwargs):
        super(TextTransfomerClassifier, self).__init__(
            num_epochs, init_lr, batch_size, max_len, tokenizer, **kwargs)

    def get_net(self):
        return nets.Transformer(
            num_classes=self.labelenc.num_classes,
            vocab_size=self.textenc.vocab_size,
            **self.kwargs
        )


class TextDPCNNClassifier(NNClassifier):
    def __init__(self, num_epochs=10, init_lr=1e-3, batch_size=8, max_len=150,
                 tokenizer=None, **kwargs):
        super(TextDPCNNClassifier, self).__init__(
            num_epochs, init_lr, batch_size, max_len, tokenizer, **kwargs)

    def get_net(self):
        return nets.DPCNN(
            num_classes=self.labelenc.num_classes,
            vocab_size=self.textenc.vocab_size,
            **self.kwargs
        )
