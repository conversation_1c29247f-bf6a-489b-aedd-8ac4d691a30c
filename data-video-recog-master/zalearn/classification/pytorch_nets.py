import math
import torch
import torch.nn as nn
from torch.nn.functional import max_pool1d
from sklearn.linear_model import Ridge, LinearRegression


# noinspection PyAbstractClass,PyUnresolvedReferences
class TextCNN(nn.Module):
    def __init__(self, num_classes, vocab_size,
                 embedding_size=128, hidden_size=128,
                 drop=0, kernel_sizes=(3, )):
        super(TextCNN, self).__init__()
        self.emb = nn.Embedding(vocab_size, embedding_size)
        self.convs = nn.ModuleList([
            nn.Conv2d(1, hidden_size, (kernel_size, embedding_size), padding=(1, 0))
            for kernel_size in kernel_sizes
        ])

        self.drop = nn.Dropout(drop)
        self.relu = nn.ReLU()
        self.fc = nn.Linear(hidden_size * len(kernel_sizes), num_classes)

    def forward(self, input_ids):
        embeddings = self.emb(input_ids).unsqueeze(1)

        conved_list = [conv(embeddings).squeeze(-1) for conv in self.convs]
        conved_list = [conved.max(axis=2)[0] for conved in conved_list]

        flatten = torch.cat(conved_list, 1).squeeze(-1)
        droped = self.drop(flatten)
        activated = self.relu(droped)
        return self.fc(activated)


# noinspection PyAbstractClass
class TextRNN(nn.Module):
    rec_types = {'lstm': nn.LSTM, 'gru': nn.GRU}

    def __init__(self, num_classes, vocab_size,
                 embedding_size=128, hidden_size=128,
                 drop=0, num_layers=1, rec_type='lstm'):
        super(TextRNN, self).__init__()
        self.emb = nn.Embedding(vocab_size, embedding_size)
        self.rec = self.rec_types[rec_type.lower()](
            embedding_size, hidden_size, num_layers, batch_first=True)

        self.drop = nn.Dropout(drop)
        self.relu = nn.ReLU()
        self.fc = nn.Linear(hidden_size, num_classes)

    def forward(self, input_ids):
        embeddings = self.emb(input_ids)
        rec_out, _ = self.rec(embeddings)
        pooled = rec_out.max(axis=1)[0]

        droped = self.drop(pooled)
        activated = self.relu(droped)
        return self.fc(activated)


# noinspection PyAbstractClass
class TextDAN(nn.Module):
    """
    https://aclanthology.org/P15-1162.pdf
    """
    def __init__(self, num_classes, vocab_size,
                 embedding_size=128, hidden_size=128,
                 drop=0, num_layers=1):
        super(TextDAN, self).__init__()
        self.emb = nn.Embedding(vocab_size, embedding_size)
        self.dropout = nn.Dropout(drop)

        encoder_layers = []
        for i in range(num_layers):
            input_dim = embedding_size if i == 0 else hidden_size
            encoder_layers.extend([
                nn.Linear(input_dim, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.ELU(),
                nn.Dropout(drop),
            ])
        self.encoder = nn.Sequential(*encoder_layers)

        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, num_classes),
            nn.BatchNorm1d(num_classes),
            nn.Dropout(drop),
        )

    def forward(self, input_ids):
        avg = self.emb(input_ids).mean(dim=1)
        droped = self.dropout(avg)
        encoded = self.encoder(droped)
        return self.classifier(encoded)


# noinspection PyAbstractClass
class DPCNN(nn.Module):
    """
    https://github.com/Tencent/NeuralNLP-NeuralClassifier/blob/master/model/classification/dpcnn.py
    """

    def __init__(self, num_classes, vocab_size,
                 embedding_size=128, hidden_size=128, drop=0.0, num_blocks=15):
        super(DPCNN, self).__init__()
        self.num_kernels = hidden_size
        self.pooling_stride = 2
        self.kernel_size = 3
        self.radius = int(self.kernel_size / 2)
        assert self.kernel_size % 2 == 1, "DPCNN kernel should be odd!"
        self.embedding = nn.Embedding(vocab_size, embedding_size)
        self.convert_conv = torch.nn.Sequential(
            torch.nn.Conv1d(
                embedding_size, self.num_kernels,
                self.kernel_size, padding=self.radius)
        )

        self.convs = torch.nn.ModuleList([torch.nn.Sequential(
            torch.nn.ReLU(),
            torch.nn.Conv1d(
                self.num_kernels, self.num_kernels,
                self.kernel_size, padding=self.radius),
            torch.nn.ReLU(),
            torch.nn.Conv1d(
                self.num_kernels, self.num_kernels,
                self.kernel_size, padding=self.radius)
        ) for _ in range(num_blocks)])
        self.dropout = nn.Dropout(drop)
        self.linear = torch.nn.Linear(self.num_kernels, num_classes)

    def forward(self, input_ids):
        x = self.embedding(input_ids)
        x = x.permute(0, 2, 1)
        conv_embedding = self.convert_conv(x)
        conv_features = self.convs[0](conv_embedding)
        conv_features = conv_embedding + conv_features
        for i in range(1, len(self.convs)):
            block_features = max_pool1d(
                conv_features, min(conv_features.size(2), self.kernel_size), self.pooling_stride)
            conv_features = self.convs[i](block_features)
            conv_features = conv_features + block_features
            if conv_features.size(2) == 1:
                break
        doc_embedding = max_pool1d(
            conv_features, conv_features.size(2)).squeeze(-1)
        return self.dropout(self.linear(doc_embedding))


# noinspection PyAbstractClass,PyUnresolvedReferences
class PositionalEncoding(nn.Module):
    """https://github.com/pytorch/examples/blob/main/word_language_model/model.py"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


# noinspection PyAbstractClass,PyUnresolvedReferences
class Transformer(nn.Module):
    """https://github.com/pytorch/examples/blob/main/word_language_model/model.py"""
    def __init__(self, num_classes, vocab_size,
                 embedding_size=128, hidden_size=128,
                 drop=0, num_layers=1, num_head=4,
                 internal_size=128):

        super(Transformer, self).__init__()
        self.pos_encoder = PositionalEncoding(embedding_size, drop)
        encoder_layers = nn.TransformerEncoderLayer(hidden_size, num_head, internal_size, drop)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_layers)
        self.encoder = nn.Embedding(vocab_size, embedding_size)
        self.ninp = embedding_size
        self.linear = nn.Linear(hidden_size, num_classes)

        self.init_weights()

    def init_weights(self):
        initrange = 0.1
        nn.init.uniform_(self.encoder.weight, -initrange, initrange)
        nn.init.zeros_(self.linear.bias)
        nn.init.uniform_(self.linear.weight, -initrange, initrange)

    def forward(self, input_ids):
        src = self.encoder(input_ids) * math.sqrt(self.ninp)
        src = self.pos_encoder(src)
        output = self.transformer_encoder(src)
        output, _ = torch.max(output, dim=1)
        return self.linear(output)
