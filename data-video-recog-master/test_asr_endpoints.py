#!/usr/bin/env python3
"""
测试不同ASR服务端点的可用性
"""
import requests

def test_asr_endpoint(base_url, name):
    """测试ASR服务端点"""
    print(f"\n🎤 测试 {name}")
    print(f"地址: {base_url}")
    
    # 测试健康检查
    health_url = f"{base_url}/health"
    try:
        response = requests.get(health_url, timeout=10)
        print(f"健康检查: {response.status_code} - {response.text[:100]}")
        
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 测试推理接口
    infer_url = f"{base_url}/inference_funasr"
    try:
        response = requests.get(infer_url, timeout=10)
        print(f"推理接口: {response.status_code} - {response.text[:100]}")
    except Exception as e:
        print(f"推理接口异常: {e}")
    
    return False

def main():
    print("🔍 ASR服务端点可用性测试")
    print("=" * 50)
    
    # 测试不同的ASR服务地址
    endpoints = [
        ("http://za-video-asr.test.za.biz", "测试环境ASR服务"),
        ("http://*************:18091", "内网ASR服务"),
        ("http://localhost:8080", "本地ASR服务"),
    ]
    
    available_services = []
    
    for base_url, name in endpoints:
        if test_asr_endpoint(base_url, name):
            available_services.append((base_url, name))
    
    print(f"\n📊 测试结果总结:")
    if available_services:
        print(f"✅ 可用的ASR服务:")
        for base_url, name in available_services:
            print(f"  - {name}: {base_url}")
    else:
        print("❌ 没有找到可用的ASR服务")
        print("\n💡 建议:")
        print("1. 检查网络连接")
        print("2. 确认ASR服务是否已启动")
        print("3. 验证服务地址和端口")
        print("4. 考虑使用本地ASR服务")

if __name__ == "__main__":
    main()
