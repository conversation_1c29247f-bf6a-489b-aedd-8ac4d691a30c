
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>银行素材审核系统</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#4568DC", secondary: "#B06AB3" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      background-color: #F5F7FA;
      font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
      }
      .gradient-bg {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      }
      .gradient-text {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      }
      .upload-area {
      border: 2px dashed #CBD5E0;
      transition: all 0.3s ease;
      }
      .upload-area:hover, .upload-area.dragging {
      border-color: #4568DC;
      background-color: rgba(69, 104, 220, 0.05);
      }
      .progress-circle {
      transform: rotate(-90deg);
      }
      .progress-circle-bg {
      fill: none;
      stroke: #E2E8F0;
      stroke-width: 4;
      }
      .progress-circle-path {
      fill: none;
      stroke-width: 4;
      stroke-linecap: round;
      transition: stroke-dashoffset 0.5s ease;
      }
      .file-input {
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      position: absolute;
      z-index: -1;
      }
      .step-connector {
      height: 2px;
      background-color: #CBD5E0;
      flex-grow: 1;
      margin: 0 8px;
      }
      .step-connector.active {
      background: linear-gradient(90deg, #4568DC, #B06AB3);
      }
      .step-circle {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #E2E8F0;
      color: #718096;
      font-weight: 600;
      }
      .step-circle.active {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      color: white;
      }
      .step-circle.completed {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      color: white;
      }
    </style>
  </head>
  <body class="min-h-screen">
    <header
      class="gradient-bg text-white py-4 px-6 shadow-md fixed w-full top-0 z-10"
    >
      <div class="container mx-auto flex items-center justify-between">
        <div class="flex items-center">
          <span class="text-2xl font-['Pacifico']">logo</span>
          <span class="ml-4 text-xl font-medium">银行素材审核系统</span>
        </div>
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <div
              class="w-8 h-8 flex items-center justify-center rounded-full bg-white/20"
            >
              <i class="ri-notification-3-line text-white"></i>
            </div>
            <div
              class="w-8 h-8 flex items-center justify-center rounded-full bg-white/20"
            >
              <i class="ri-settings-3-line text-white"></i>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div
              class="w-8 h-8 rounded-full bg-white/90 flex items-center justify-center text-primary font-bold"
            >
              张
            </div>
            <span>张经理</span>
          </div>
        </div>
      </div>
    </header>
    <main class="container mx-auto pt-24 pb-12 px-6 max-w-5xl">
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">素材审核</h1>
        <p class="text-gray-600">
          上传您需要审核的素材文件，支持图片、视频和文档格式
        </p>
      </div>
      <!-- 上传区域 -->
      <div id="upload-container" class="mb-12">
        <div
          id="upload-area"
          class="upload-area rounded-2xl p-10 flex flex-col items-center justify-center text-center cursor-pointer bg-white shadow-sm"
        >
          <div
            class="w-20 h-20 flex items-center justify-center rounded-full bg-blue-50 mb-4"
          >
            <i class="ri-upload-cloud-2-line ri-3x gradient-text"></i>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">
            点击或拖拽文件到此处上传
          </h3>
          <p class="text-gray-500 mb-6 max-w-md">
            支持单个或批量上传，每个文件大小不超过500MB
          </p>
          <div class="flex items-center justify-center space-x-4 mb-6">
            <div class="flex flex-col items-center">
              <div
                class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mb-2"
              >
                <i class="ri-image-line ri-lg text-primary"></i>
              </div>
              <span class="text-sm text-gray-600">图片</span>
            </div>
            <div class="flex flex-col items-center">
              <div
                class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mb-2"
              >
                <i class="ri-video-line ri-lg text-primary"></i>
              </div>
              <span class="text-sm text-gray-600">视频</span>
            </div>
            <div class="flex flex-col items-center">
              <div
                class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mb-2"
              >
                <i class="ri-file-text-line ri-lg text-primary"></i>
              </div>
              <span class="text-sm text-gray-600">文档</span>
            </div>
          </div>
          <button
            id="select-file-btn"
            class="gradient-bg text-white px-6 py-2.5 rounded-button font-medium shadow-sm hover:shadow-md transition-all !rounded-button"
          >
            选择文件
          </button>
          <input
            type="file"
            id="file-input"
            class="file-input"
            multiple
            accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
          />
          <p class="text-xs text-gray-500 mt-4">
            支持格式：JPG、PNG、MP4、PDF、DOCX、XLSX、PPTX等
          </p>
        </div>
      </div>
      <!-- 上传进度区域 -->
      <div id="upload-progress-container" class="mb-12 hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">上传进度</h2>
        <div id="upload-progress-list" class="space-y-4">
          <!-- 上传进度项会在这里动态添加 -->
        </div>
      </div>
      <!-- 审核流程 -->
      <div id="review-process-container" class="mb-12 hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">审核流程</h2>
        <div class="bg-white rounded-2xl p-6 shadow-sm">
          <div class="flex items-center mb-8">
            <div class="flex flex-col items-center">
              <div id="step-1-circle" class="step-circle active">1</div>
              <div class="mt-2 text-center">
                <p id="step-1-text" class="text-sm font-medium text-primary">
                  文件上传
                </p>
                <p id="step-1-time" class="text-xs text-gray-500 mt-1">
                  2025-07-04 14:30
                </p>
              </div>
            </div>
            <div id="connector-1" class="step-connector"></div>
            <div class="flex flex-col items-center">
              <div id="step-2-circle" class="step-circle">2</div>
              <div class="mt-2 text-center">
                <p id="step-2-text" class="text-sm font-medium text-gray-500">
                  系统校验
                </p>
                <p id="step-2-time" class="text-xs text-gray-500 mt-1">
                  等待中
                </p>
              </div>
            </div>
            <div id="connector-2" class="step-connector"></div>
            <div class="flex flex-col items-center">
              <div id="step-3-circle" class="step-circle">3</div>
              <div class="mt-2 text-center">
                <p id="step-3-text" class="text-sm font-medium text-gray-500">
                  人工审核
                </p>
                <p id="step-3-time" class="text-xs text-gray-500 mt-1">
                  等待中
                </p>
              </div>
            </div>
            <div id="connector-3" class="step-connector"></div>
            <div class="flex flex-col items-center">
              <div id="step-4-circle" class="step-circle">4</div>
              <div class="mt-2 text-center">
                <p id="step-4-text" class="text-sm font-medium text-gray-500">
                  审核完成
                </p>
                <p id="step-4-time" class="text-xs text-gray-500 mt-1">
                  等待中
                </p>
              </div>
            </div>
          </div>
          <div class="p-4 bg-blue-50 rounded-lg">
            <div class="flex items-start">
              <div
                class="w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 mt-0.5"
              >
                <i class="ri-information-line text-primary"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-700" id="status-message">
                  文件已上传成功，正在等待系统校验...
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- JSON数据展示 -->
      <div id="json-data-container" class="mb-12 hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">解析数据详情</h2>
        <div class="bg-white rounded-2xl p-6 shadow-sm">
          <div class="mb-4">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-medium text-gray-800">AI分析结果 (JSON格式)</h3>
              <button id="copy-json-btn" class="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                <i class="ri-file-copy-line mr-1"></i>复制JSON
              </button>
            </div>
            <div class="bg-gray-50 rounded-lg p-4 border">
              <pre id="json-display" class="text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto max-h-96 overflow-y-auto"></pre>
            </div>
          </div>
          <div class="text-xs text-gray-500">
            <p>💡 提示：此JSON数据展示了AI系统对上传文件的完整分析结果，包括OCR识别、人脸检测、风险评估等信息。</p>
          </div>
        </div>
      </div>

      <!-- 审核结果 -->
      <div id="review-result-container" class="hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">审核结果</h2>
        <div class="space-y-4" id="review-result-list">
          <!-- 审核结果项会在这里动态添加 -->
        </div>
      </div>
      <!-- 无审核记录提示 -->
      <div
        id="no-records"
        class="hidden bg-white rounded-2xl p-10 text-center shadow-sm"
      >
        <div
          class="w-20 h-20 mx-auto flex items-center justify-center rounded-full bg-gray-100 mb-4"
        >
          <i class="ri-file-list-3-line ri-3x text-gray-400"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-2">暂无审核记录</h3>
        <p class="text-gray-500 mb-6">您还没有提交任何素材进行审核</p>
      </div>
    </main>
    <!-- 审核详情弹窗 -->
    <div
      id="review-detail-modal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
    >
      <div
        class="bg-white rounded-2xl max-w-3xl w-full max-h-[90vh] overflow-auto p-6 mx-4"
      >
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-gray-800">审核详情</h3>
          <button
            id="close-modal"
            class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 cursor-pointer"
          >
            <i class="ri-close-line ri-lg text-gray-500"></i>
          </button>
        </div>
        <div id="modal-content" class="space-y-6">
          <!-- 弹窗内容会在这里动态添加 -->
        </div>
        <div class="mt-8 flex justify-end space-x-4">
          <button
            id="modal-cancel"
            class="px-6 py-2.5 border border-gray-300 rounded-button text-gray-700 font-medium hover:bg-gray-50 transition-all !rounded-button"
          >
            关闭
          </button>
          <button
            id="modal-confirm"
            class="gradient-bg text-white px-6 py-2.5 rounded-button font-medium shadow-sm hover:shadow-md transition-all !rounded-button"
          >
            确认
          </button>
        </div>
      </div>
    </div>
    <script id="upload-handler">
      document.addEventListener("DOMContentLoaded", function () {
        const uploadArea = document.getElementById("upload-area");
        const fileInput = document.getElementById("file-input");
        const selectFileBtn = document.getElementById("select-file-btn");
        const uploadProgressContainer = document.getElementById(
          "upload-progress-container",
        );
        const uploadProgressList = document.getElementById("upload-progress-list");
        selectFileBtn.addEventListener("click", function (e) {
          e.stopPropagation(); // 阻止事件冒泡
          fileInput.click();
        });
        const reviewProcessContainer = document.getElementById(
          "review-process-container",
        );
        const reviewResultContainer = document.getElementById(
          "review-result-container",
        );
        const reviewResultList = document.getElementById("review-result-list");
        const noRecords = document.getElementById("no-records");
        // 拖拽上传
        uploadArea.addEventListener("dragover", function (e) {
          e.preventDefault();
          uploadArea.classList.add("dragging");
        });
        uploadArea.addEventListener("dragleave", function () {
          uploadArea.classList.remove("dragging");
        });
        uploadArea.addEventListener("drop", function (e) {
          e.preventDefault();
          uploadArea.classList.remove("dragging");
          if (e.dataTransfer.files.length > 0) {
            handleFiles(e.dataTransfer.files);
          }
        });
        // 点击上传区域（但不包括按钮）
        uploadArea.addEventListener("click", function (e) {
          // 如果点击的是按钮，不处理（按钮有自己的事件处理器）
          if (e.target.closest('#select-file-btn')) {
            return;
          }
          fileInput.click();
        });
        fileInput.addEventListener("change", function () {
          if (fileInput.files.length > 0) {
            handleFiles(fileInput.files);
            noRecords.classList.add("hidden");
            // 重置文件输入框，避免重复上传问题
            fileInput.value = '';
          }
        });
        function handleFiles(files) {
          // 清理之前的状态
          uploadProgressContainer.classList.remove("hidden");
          reviewProcessContainer.classList.add("hidden");
          reviewResultContainer.classList.add("hidden");
          document.getElementById("json-data-container").classList.add("hidden");
          uploadProgressList.innerHTML = "";

          // 清理之前的审核结果
          window.currentAuditResults = [];

          // 验证文件
          const validFiles = Array.from(files).filter(file => {
            const validTypes = [
              'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp',
              'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
              'application/pdf',
              'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              'text/plain'
            ];

            const isValidType = validTypes.includes(file.type) ||
                               file.name.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|mp4|avi|mov|wmv|flv|pdf|doc|docx|xls|xlsx|ppt|pptx|txt)$/);

            if (!isValidType) {
              alert(`不支持的文件类型: ${file.name}`);
              return false;
            }

            if (file.size > 500 * 1024 * 1024) { // 500MB
              alert(`文件过大: ${file.name} (最大支持500MB)`);
              return false;
            }

            return true;
          });

          if (validFiles.length === 0) {
            uploadProgressContainer.classList.add("hidden");
            return;
          }

          validFiles.forEach((file, index) => {
            const fileId = Date.now() + index;
            createProgressItem(file, fileId);
            realUpload(file, fileId);
          });
        }
        function createProgressItem(file, fileId) {
          const fileSize = formatFileSize(file.size);
          const fileType = getFileType(file.type);
          const fileIcon = getFileIcon(fileType);
          const progressItem = document.createElement("div");
          progressItem.id = `progress-item-${fileId}`;
          progressItem.className =
            "bg-white rounded-xl p-4 shadow-sm flex items-center";
          progressItem.innerHTML = `
      <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
      <i class="${fileIcon} text-primary"></i>
      </div>
      <div class="flex-grow mr-4">
      <div class="flex justify-between mb-1">
      <span class="text-sm font-medium text-gray-800">${file.name}</span>
      <span class="text-xs text-gray-500" id="progress-percentage-${fileId}">0%</span>
      </div>
      <div class="flex justify-between items-center">
      <div class="w-full bg-gray-200 rounded-full h-1.5 mr-4">
      <div id="progress-bar-${fileId}" class="gradient-bg h-1.5 rounded-full" style="width: 0%"></div>
      </div>
      <span class="text-xs text-gray-500 whitespace-nowrap">${fileSize}</span>
      </div>
      <div class="flex justify-between mt-1">
      <span class="text-xs text-gray-500" id="upload-speed-${fileId}">0 KB/s</span>
      <span class="text-xs text-gray-500" id="upload-time-${fileId}">剩余时间: 计算中...</span>
      </div>
      </div>
      <button class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 cursor-pointer" onclick="cancelUpload(${fileId})">
      <i class="ri-close-line text-gray-500"></i>
      </button>
      `;
          uploadProgressList.appendChild(progressItem);
        }

        // 真实上传函数
        async function realUpload(file, fileId) {
          const progressBar = document.getElementById(`progress-bar-${fileId}`);
          const progressPercentage = document.getElementById(`progress-percentage-${fileId}`);
          const uploadSpeed = document.getElementById(`upload-speed-${fileId}`);
          const uploadTime = document.getElementById(`upload-time-${fileId}`);

          try {
            const formData = new FormData();
            formData.append('file', file);

            const startTime = Date.now();

            // 模拟上传进度
            let progress = 0;
            const progressInterval = setInterval(() => {
              progress += Math.random() * 10 + 5;
              if (progress > 90) progress = 90; // 保持在90%直到真实上传完成

              progressBar.style.width = `${progress}%`;
              progressPercentage.textContent = `${Math.round(progress)}%`;

              const elapsed = (Date.now() - startTime) / 1000;
              const speed = (file.size * progress / 100) / elapsed / 1024; // KB/s
              uploadSpeed.textContent = `${Math.round(speed)} KB/s`;

              const remaining = (100 - progress) / (progress / elapsed);
              uploadTime.textContent = `剩余时间: ${Math.round(remaining)} 秒`;
            }, 200);

            // 发送真实请求
            const response = await fetch('/upload', {
              method: 'POST',
              body: formData
            });

            clearInterval(progressInterval);

            if (response.ok) {
              const result = await response.json();

              // 完成上传进度
              progressBar.style.width = '100%';
              progressPercentage.textContent = '100%';
              uploadSpeed.textContent = '上传完成';
              uploadTime.textContent = '已完成';

              // 显示成功状态
              setTimeout(() => {
                const progressItem = document.getElementById(`progress-item-${fileId}`);
                if (progressItem) {
                  progressItem.innerHTML = `
                    <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-green-50 mr-4">
                      <i class="ri-check-line text-green-500"></i>
                    </div>
                    <div class="flex-grow">
                      <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-800">上传并审核成功</span>
                      </div>
                      <p class="text-sm text-gray-500">文件已成功上传并完成AI审核，即将显示结果...</p>
                    </div>
                  `;

                  // 存储审核结果
                  window.currentAuditResults = window.currentAuditResults || [];
                  window.currentAuditResults.push({
                    fileId: fileId,
                    filename: file.name,
                    result: result
                  });

                  setTimeout(() => {
                    progressItem.remove();
                    if (uploadProgressList.children.length === 0) {
                      uploadProgressContainer.classList.add("hidden");
                      reviewProcessContainer.classList.remove("hidden");
                      startRealReviewProcess();
                    }
                  }, 1500);
                }
              }, 500);

            } else {
              throw new Error(`上传失败: ${response.status}`);
            }

          } catch (error) {
            clearInterval(progressInterval);
            console.error('上传错误:', error);

            const progressItem = document.getElementById(`progress-item-${fileId}`);
            if (progressItem) {
              progressItem.innerHTML = `
                <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-red-50 mr-4">
                  <i class="ri-close-line text-red-500"></i>
                </div>
                <div class="flex-grow">
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-800">上传失败</span>
                  </div>
                  <p class="text-sm text-gray-500">错误: ${error.message}</p>
                </div>
              `;
            }
          }
        }

        function simulateUpload(fileId) {
          let progress = 0;
          const progressBar = document.getElementById(`progress-bar-${fileId}`);
          const progressPercentage = document.getElementById(
            `progress-percentage-${fileId}`,
          );
          const uploadSpeed = document.getElementById(`upload-speed-${fileId}`);
          const uploadTime = document.getElementById(`upload-time-${fileId}`);
          const interval = setInterval(() => {
            const increment = Math.random() * 5 + 1;
            progress += increment;
            if (progress >= 100) {
              progress = 100;
              clearInterval(interval);
              setTimeout(() => {
                const progressItem = document.getElementById(
                  `progress-item-${fileId}`,
                );
                if (progressItem) {
                  progressItem.innerHTML = `
      <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-green-50 mr-4">
      <i class="ri-check-line text-green-500"></i>
      </div>
      <div class="flex-grow">
      <div class="flex justify-between mb-1">
      <span class="text-sm font-medium text-gray-800">上传成功</span>
      </div>
      <p class="text-sm text-gray-500">文件已成功上传，即将进入审核流程...</p>
      </div>
      `;
                  setTimeout(() => {
                    progressItem.remove();
                    if (uploadProgressList.children.length === 0) {
                      uploadProgressContainer.classList.add("hidden");
                      reviewProcessContainer.classList.remove("hidden");
                      startReviewProcess();
                    }
                  }, 1500);
                }
              }, 500);
            }
            progressBar.style.width = `${progress}%`;
            progressPercentage.textContent = `${Math.round(progress)}%`;
            const speed = Math.random() * 500 + 100;
            uploadSpeed.textContent = `${Math.round(speed)} KB/s`;
            const remainingTime = Math.round((100 - progress) / increment);
            uploadTime.textContent = `剩余时间: ${remainingTime} 秒`;
          }, 300);
          window.uploadIntervals = window.uploadIntervals || {};
          window.uploadIntervals[fileId] = interval;
        }
        // 添加到全局作用域，以便HTML中的onclick调用
        window.cancelUpload = function (fileId) {
          if (window.uploadIntervals && window.uploadIntervals[fileId]) {
            clearInterval(window.uploadIntervals[fileId]);
            delete window.uploadIntervals[fileId];
          }
          const progressItem = document.getElementById(`progress-item-${fileId}`);
          if (progressItem) {
            progressItem.remove();
          }
          if (uploadProgressList.children.length === 0) {
            uploadProgressContainer.classList.add("hidden");
          }
        };

        // 将工具函数移到全局作用域
        window.formatFileSize = function(bytes) {
          if (bytes === 0) return "0 Bytes";
          const k = 1024;
          const sizes = ["Bytes", "KB", "MB", "GB"];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
        };

        window.getFileType = function(mimeType) {
          if (mimeType.startsWith("image/")) return "image";
          if (mimeType.startsWith("video/")) return "video";
          if (mimeType.startsWith("application/pdf")) return "pdf";
          if (mimeType.includes("word") || mimeType.includes("document"))
            return "doc";
          if (mimeType.includes("excel") || mimeType.includes("sheet")) return "xls";
          if (mimeType.includes("powerpoint") || mimeType.includes("presentation"))
            return "ppt";
          return "file";
        };

        window.getFileIcon = function(fileType) {
          switch (fileType) {
            case "image":
              return "ri-image-line";
            case "video":
              return "ri-video-line";
            case "pdf":
              return "ri-file-pdf-line";
            case "doc":
              return "ri-file-word-line";
            case "xls":
              return "ri-file-excel-line";
            case "ppt":
              return "ri-file-ppt-line";
            default:
              return "ri-file-line";
          }
        };
      });
    </script>
    <script id="review-process-handler">
      document.addEventListener("DOMContentLoaded", function () {
        // 真实审核流程
        window.startRealReviewProcess = function () {
          const step1Circle = document.getElementById("step-1-circle");
          const step2Circle = document.getElementById("step-2-circle");
          const step3Circle = document.getElementById("step-3-circle");
          const step4Circle = document.getElementById("step-4-circle");
          const step1Text = document.getElementById("step-1-text");
          const step2Text = document.getElementById("step-2-text");
          const step3Text = document.getElementById("step-3-text");
          const step4Text = document.getElementById("step-4-text");
          const step1Time = document.getElementById("step-1-time");
          const step2Time = document.getElementById("step-2-time");
          const step3Time = document.getElementById("step-3-time");
          const step4Time = document.getElementById("step-4-time");
          const connector1 = document.getElementById("connector-1");
          const connector2 = document.getElementById("connector-2");
          const connector3 = document.getElementById("connector-3");
          const statusMessage = document.getElementById("status-message");

          // 步骤1完成
          step1Circle.innerHTML = '<i class="ri-check-line"></i>';
          step1Circle.classList.add("completed");

          // 步骤2激活
          setTimeout(() => {
            step2Circle.classList.add("active");
            step2Text.classList.remove("text-gray-500");
            step2Text.classList.add("text-primary");
            connector1.classList.add("active");
            const now = new Date();
            step2Time.textContent = formatDateTime(now);
            statusMessage.textContent = "AI系统正在分析文件内容，包括OCR识别、人脸检测、语音识别等...";

            // 步骤2完成
            setTimeout(() => {
              step2Circle.innerHTML = '<i class="ri-check-line"></i>';
              step2Circle.classList.add("completed");

              // 步骤3激活
              step3Circle.classList.add("active");
              step3Text.classList.remove("text-gray-500");
              step3Text.classList.add("text-primary");
              connector2.classList.add("active");
              const now = new Date();
              step3Time.textContent = formatDateTime(now);
              statusMessage.textContent = "AI分析完成，正在进行风险评估和合规检查...";

              // 步骤3完成
              setTimeout(() => {
                step3Circle.innerHTML = '<i class="ri-check-line"></i>';
                step3Circle.classList.add("completed");

                // 步骤4激活
                step4Circle.classList.add("active");
                step4Text.classList.remove("text-gray-500");
                step4Text.classList.add("text-primary");
                connector3.classList.add("active");
                const now = new Date();
                step4Time.textContent = formatDateTime(now);
                statusMessage.textContent = "审核已完成，请查看详细的AI分析结果";

                // 显示真实审核结果和JSON数据
                setTimeout(() => {
                  document.getElementById("json-data-container").classList.remove("hidden");
                  displayJsonData();
                  document.getElementById("review-result-container").classList.remove("hidden");
                  generateRealReviewResults();
                }, 1000);
              }, 2000);
            }, 2000);
          }, 1000);
        };

        // 显示JSON数据
        window.displayJsonData = function () {
          const jsonDisplay = document.getElementById("json-display");
          const copyBtn = document.getElementById("copy-json-btn");

          if (window.currentAuditResults && window.currentAuditResults.length > 0) {
            // 合并所有审核结果
            const allResults = window.currentAuditResults.map(item => ({
              filename: item.filename,
              trace_id: item.result.trace_id,
              upload_time: item.result.audit_result.audit_time,
              file_info: item.result.audit_result.file_info,
              analysis_result: {
                ocr_result: item.result.audit_result.ocr_result,
                face_detection: item.result.audit_result.face_detection,
                asr_result: item.result.audit_result.asr_result,
                qr_code_result: item.result.audit_result.qr_code_result,
                risk_evaluation: item.result.audit_result.risk_evaluation,
                compliance_check: item.result.audit_result.compliance_check,
                analysis_method: item.result.audit_result.analysis_method
              }
            }));

            const jsonData = {
              total_files: allResults.length,
              analysis_timestamp: new Date().toISOString(),
              results: allResults
            };

            jsonDisplay.textContent = JSON.stringify(jsonData, null, 2);

            // 复制功能
            copyBtn.onclick = function() {
              navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2)).then(() => {
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="ri-check-line mr-1"></i>已复制';
                copyBtn.classList.add('bg-green-50', 'text-green-600');
                copyBtn.classList.remove('bg-blue-50', 'text-blue-600');

                setTimeout(() => {
                  copyBtn.innerHTML = originalText;
                  copyBtn.classList.remove('bg-green-50', 'text-green-600');
                  copyBtn.classList.add('bg-blue-50', 'text-blue-600');
                }, 2000);
              }).catch(() => {
                alert('复制失败，请手动选择文本复制');
              });
            };
          } else {
            jsonDisplay.textContent = '暂无数据';
          }
        };

        window.startReviewProcess = function () {
          const step1Circle = document.getElementById("step-1-circle");
          const step2Circle = document.getElementById("step-2-circle");
          const step3Circle = document.getElementById("step-3-circle");
          const step4Circle = document.getElementById("step-4-circle");
          const step1Text = document.getElementById("step-1-text");
          const step2Text = document.getElementById("step-2-text");
          const step3Text = document.getElementById("step-3-text");
          const step4Text = document.getElementById("step-4-text");
          const step1Time = document.getElementById("step-1-time");
          const step2Time = document.getElementById("step-2-time");
          const step3Time = document.getElementById("step-3-time");
          const step4Time = document.getElementById("step-4-time");
          const connector1 = document.getElementById("connector-1");
          const connector2 = document.getElementById("connector-2");
          const connector3 = document.getElementById("connector-3");
          const statusMessage = document.getElementById("status-message");
          // 模拟系统校验过程
          setTimeout(() => {
            // 步骤1完成
            step1Circle.innerHTML = '<i class="ri-check-line"></i>';
            step1Circle.classList.add("completed");
            // 步骤2激活
            step2Circle.classList.add("active");
            step2Text.classList.remove("text-gray-500");
            step2Text.classList.add("text-primary");
            connector1.classList.add("active");
            const now = new Date();
            step2Time.textContent = formatDateTime(now);
            statusMessage.textContent = "系统正在校验文件格式和内容，请稍候...";
            // 模拟系统校验完成
            setTimeout(() => {
              // 步骤2完成
              step2Circle.innerHTML = '<i class="ri-check-line"></i>';
              step2Circle.classList.add("completed");
              // 步骤3激活
              step3Circle.classList.add("active");
              step3Text.classList.remove("text-gray-500");
              step3Text.classList.add("text-primary");
              connector2.classList.add("active");
              const now = new Date();
              step3Time.textContent = formatDateTime(now);
              statusMessage.textContent = "系统校验通过，等待人工审核中...";
              // 模拟人工审核
              setTimeout(() => {
                // 步骤3完成
                step3Circle.innerHTML = '<i class="ri-check-line"></i>';
                step3Circle.classList.add("completed");
                // 步骤4激活
                step4Circle.classList.add("active");
                step4Text.classList.remove("text-gray-500");
                step4Text.classList.add("text-primary");
                connector3.classList.add("active");
                const now = new Date();
                step4Time.textContent = formatDateTime(now);
                statusMessage.textContent = "审核已完成，请查看审核结果";
                // 显示审核结果
                setTimeout(() => {
                  document
                    .getElementById("review-result-container")
                    .classList.remove("hidden");
                  generateReviewResults();
                }, 1000);
              }, 5000); // 人工审核时间
            }, 3000); // 系统校验时间
          }, 2000); // 初始延迟
        };
        function formatDateTime(date) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          return `${year}-${month}-${day} ${hours}:${minutes}`;
        }
        // 生成真实审核结果
        window.generateRealReviewResults = function () {
          const reviewResultList = document.getElementById("review-result-list");
          reviewResultList.innerHTML = "";

          if (window.currentAuditResults && window.currentAuditResults.length > 0) {
            window.currentAuditResults.forEach((auditData, index) => {
              const result = auditData.result;
              const audit = result.audit_result;

              // 构造结果数据
              const resultItem = {
                id: auditData.fileId,
                fileName: auditData.filename,
                fileType: getFileTypeFromName(auditData.filename),
                fileSize: formatFileSize(result.size),
                status: audit.compliance_check ? "approved" : "rejected",
                reviewer: "AI系统",
                reviewTime: new Date(audit.audit_time).toLocaleString('zh-CN'),
                comments: generateAIComments(audit),
                suggestions: generateAISuggestions(audit),
                auditDetails: audit
              };

              const resultElement = createRealResultItem(resultItem);
              reviewResultList.appendChild(resultElement);
            });
          } else {
            // 如果没有真实结果，显示提示
            reviewResultList.innerHTML = `
              <div class="bg-white rounded-2xl p-6 shadow-sm text-center">
                <div class="w-16 h-16 mx-auto flex items-center justify-center rounded-full bg-gray-100 mb-4">
                  <i class="ri-file-list-3-line ri-2x text-gray-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">暂无审核结果</h3>
                <p class="text-gray-500">请先上传文件进行审核</p>
              </div>
            `;
          }
        };

        function generateAIComments(audit) {
          let comments = [];

          if (audit.ocr_result && audit.ocr_result.length > 0) {
            comments.push(`检测到${audit.ocr_result.length}个文字区域`);
          }

          if (audit.face_detection && audit.face_detection.length > 0) {
            comments.push(`检测到${audit.face_detection.length}个人脸`);
          }

          if (audit.asr_result && audit.asr_result.length > 0) {
            comments.push(`语音识别完成，检测到${audit.asr_result.length}段语音`);
          }

          if (audit.qr_code_result && audit.qr_code_result.length > 0) {
            comments.push(`检测到${audit.qr_code_result.length}个二维码`);
          }

          comments.push(`风险等级: ${audit.risk_evaluation}`);
          comments.push(`合规检查: ${audit.compliance_check ? '通过' : '未通过'}`);

          return comments.join('；');
        }

        function generateAISuggestions(audit) {
          let suggestions = [];

          if (audit.risk_evaluation === 'high') {
            suggestions.push('建议人工复审高风险内容');
          }

          if (audit.ocr_result && audit.ocr_result.some(text => text.includes('贷款') || text.includes('投资'))) {
            suggestions.push('检测到金融相关词汇，请确保符合监管要求');
          }

          if (!audit.compliance_check) {
            suggestions.push('内容不符合合规要求，建议修改后重新提交');
          }

          return suggestions;
        }

        window.getFileTypeFromName = function(filename) {
          const ext = filename.split('.').pop().toLowerCase();
          if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)) return 'image';
          if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(ext)) return 'video';
          if (ext === 'pdf') return 'pdf';
          if (['doc', 'docx'].includes(ext)) return 'doc';
          if (['xls', 'xlsx'].includes(ext)) return 'xls';
          if (['ppt', 'pptx'].includes(ext)) return 'ppt';
          return 'file';
        };

        window.generateReviewResults = function () {
          const reviewResultList = document.getElementById("review-result-list");
          reviewResultList.innerHTML = "";
          // 模拟3个审核结果
          const results = [
            {
              id: 1,
              fileName: "营销宣传视频.mp4",
              fileType: "video",
              fileSize: "35.4 MB",
              status: "approved",
              reviewer: "李审核",
              reviewTime: "2025-07-04 15:05",
              comments:
                "视频内容符合银行宣传规范，画面清晰，音频质量良好，可以用于线上营销活动。",
              suggestions: [],
            },
            {
              id: 2,
              fileName: "贷款产品介绍.pdf",
              fileType: "pdf",
              fileSize: "2.8 MB",
              status: "rejected",
              reviewer: "王审核",
              reviewTime: "2025-07-04 15:10",
              comments: "文档中部分条款描述不够清晰，可能导致客户误解。",
              suggestions: [
                "第3页中关于利率的描述需要更加明确",
                "第5页中的贷款期限说明需要补充提前还款的相关条件",
                "封面设计需要符合最新的品牌规范",
              ],
            },
            {
              id: 3,
              fileName: "客户权益说明.jpg",
              fileType: "image",
              fileSize: "1.2 MB",
              status: "approved",
              reviewer: "赵审核",
              reviewTime: "2025-07-04 15:15",
              comments: "图片内容清晰，表述准确，符合监管要求。",
              suggestions: [],
            },
          ];
          results.forEach((result) => {
            const resultItem = createResultItem(result);
            reviewResultList.appendChild(resultItem);
          });
        };
        function createRealResultItem(result) {
          const resultItem = document.createElement("div");
          resultItem.className = "bg-white rounded-2xl p-6 shadow-sm";
          const statusColor = result.status === "approved" ? "text-green-500 bg-green-50" : "text-red-500 bg-red-50";
          const statusIcon = result.status === "approved" ? "ri-checkbox-circle-line" : "ri-close-circle-line";
          const statusText = result.status === "approved" ? "AI审核通过" : "AI审核不通过";
          const fileIcon = getFileIconByType(result.fileType);

          resultItem.innerHTML = `
            <div class="flex justify-between items-start mb-4">
              <div class="flex items-center">
                <div class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
                  <i class="${fileIcon} ri-lg text-primary"></i>
                </div>
                <div>
                  <h4 class="text-lg font-medium text-gray-800">${result.fileName}</h4>
                  <p class="text-sm text-gray-500">${result.fileSize} • ${result.reviewTime}</p>
                </div>
              </div>
              <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
                <i class="${statusIcon} mr-1"></i>
                <span class="text-sm font-medium">${statusText}</span>
              </div>
            </div>
            <div class="pl-16">
              <div class="mb-4">
                <h5 class="text-sm font-medium text-gray-700 mb-2">AI分析结果</h5>
                <p class="text-sm text-gray-600">${result.comments}</p>
              </div>
              ${result.suggestions.length > 0 ? `
                <div class="mb-4">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">建议</h5>
                  <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
                    ${result.suggestions.map(suggestion => `<li>${suggestion}</li>`).join("")}
                  </ul>
                </div>
              ` : ""}
              <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">审核方式: ${result.reviewer}</div>
                <div class="flex space-x-2">
                  ${result.status === "rejected" ? `
                    <button class="px-4 py-1.5 border border-primary text-primary rounded-button text-sm font-medium hover:bg-primary hover:text-white transition-all !rounded-button" onclick="showReuploadModal(${result.id})">重新上传</button>
                  ` : ""}
                  <button class="px-4 py-1.5 gradient-bg text-white rounded-button text-sm font-medium shadow-sm hover:shadow-md transition-all !rounded-button" onclick="showRealDetailModal(${result.id})">查看详情</button>
                </div>
              </div>
            </div>
          `;
          return resultItem;
        }

        function createResultItem(result) {
          const resultItem = document.createElement("div");
          resultItem.className = "bg-white rounded-2xl p-6 shadow-sm";
          const statusColor =
            result.status === "approved"
              ? "text-green-500 bg-green-50"
              : "text-red-500 bg-red-50";
          const statusIcon =
            result.status === "approved"
              ? "ri-checkbox-circle-line"
              : "ri-close-circle-line";
          const statusText = result.status === "approved" ? "审核通过" : "审核不通过";
          const fileIcon = getFileIconByType(result.fileType);
          resultItem.innerHTML = `
      <div class="flex justify-between items-start mb-4">
      <div class="flex items-center">
      <div class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
      <i class="${fileIcon} ri-lg text-primary"></i>
      </div>
      <div>
      <h4 class="text-lg font-medium text-gray-800">${result.fileName}</h4>
      <p class="text-sm text-gray-500">${result.fileSize} • ${result.reviewTime}</p>
      </div>
      </div>
      <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
      <i class="${statusIcon} mr-1"></i>
      <span class="text-sm font-medium">${statusText}</span>
      </div>
      </div>
      <div class="pl-16">
      <div class="mb-4">
      <h5 class="text-sm font-medium text-gray-700 mb-2">审核意见</h5>
      <p class="text-sm text-gray-600">${result.comments}</p>
      </div>
      ${
        result.suggestions.length > 0
          ? `
      <div class="mb-4">
      <h5 class="text-sm font-medium text-gray-700 mb-2">修改建议</h5>
      <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
      ${result.suggestions.map((suggestion) => `<li>${suggestion}</li>`).join("")}
      </ul>
      </div>
      `
          : ""
      }
      <div class="flex justify-between items-center">
      <div class="text-sm text-gray-500">审核人员: ${result.reviewer}</div>
      <div class="flex space-x-2">
      ${
        result.status === "rejected"
          ? `
      <button class="px-4 py-1.5 border border-primary text-primary rounded-button text-sm font-medium hover:bg-primary hover:text-white transition-all !rounded-button" onclick="showReuploadModal(${result.id})">重新上传</button>
      `
          : ""
      }
      <button class="px-4 py-1.5 gradient-bg text-white rounded-button text-sm font-medium shadow-sm hover:shadow-md transition-all !rounded-button" onclick="showDetailModal(${result.id})">查看详情</button>
      </div>
      </div>
      </div>
      `;
          return resultItem;
        }
        function getFileIconByType(fileType) {
          switch (fileType) {
            case "image":
              return "ri-image-line";
            case "video":
              return "ri-video-line";
            case "pdf":
              return "ri-file-pdf-line";
            case "doc":
              return "ri-file-word-line";
            case "xls":
              return "ri-file-excel-line";
            case "ppt":
              return "ri-file-ppt-line";
            default:
              return "ri-file-line";
          }
        }
        // 显示真实详情弹窗
        window.showRealDetailModal = function (resultId) {
          const modal = document.getElementById("review-detail-modal");
          const modalContent = document.getElementById("modal-content");

          // 查找对应的审核结果
          const auditData = window.currentAuditResults.find(item => item.fileId == resultId);
          if (!auditData) {
            alert('未找到审核结果');
            return;
          }

          const result = auditData.result;
          const audit = result.audit_result;
          const fileIcon = getFileIconByType(getFileTypeFromName(auditData.filename));
          const statusColor = audit.compliance_check ? "text-green-500 bg-green-50" : "text-red-500 bg-red-50";
          const statusIcon = audit.compliance_check ? "ri-checkbox-circle-line" : "ri-close-circle-line";
          const statusText = audit.compliance_check ? "AI审核通过" : "AI审核不通过";

          modalContent.innerHTML = `
            <div class="flex items-start">
              <div class="w-14 h-14 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
                <i class="${fileIcon} ri-2x text-primary"></i>
              </div>
              <div class="flex-grow">
                <div class="flex justify-between items-center mb-2">
                  <h4 class="text-lg font-medium text-gray-800">${auditData.filename}</h4>
                  <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
                    <i class="${statusIcon} mr-1"></i>
                    <span class="text-sm font-medium">${statusText}</span>
                  </div>
                </div>
                <p class="text-sm text-gray-500">${formatFileSize(result.size)}</p>
              </div>
            </div>

            <div class="border-t border-gray-200 pt-4 mt-4">
              <h5 class="text-md font-medium text-gray-800 mb-3">AI分析详情</h5>

              ${audit.ocr_result && audit.ocr_result.length > 0 ? `
                <div class="mb-4">
                  <h6 class="text-sm font-medium text-gray-700 mb-2">OCR文字识别结果</h6>
                  <div class="bg-gray-50 p-3 rounded-lg">
                    <ul class="text-sm text-gray-600 space-y-1">
                      ${audit.ocr_result.map(text => `<li>• ${text}</li>`).join("")}
                    </ul>
                  </div>
                </div>
              ` : ""}

              ${audit.face_detection && audit.face_detection.length > 0 ? `
                <div class="mb-4">
                  <h6 class="text-sm font-medium text-gray-700 mb-2">人脸检测结果</h6>
                  <div class="bg-gray-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600">检测到 ${audit.face_detection.length} 个人脸</p>
                  </div>
                </div>
              ` : ""}

              ${audit.asr_result && audit.asr_result.length > 0 ? `
                <div class="mb-4">
                  <h6 class="text-sm font-medium text-gray-700 mb-2">语音识别结果</h6>
                  <div class="bg-gray-50 p-3 rounded-lg">
                    <ul class="text-sm text-gray-600 space-y-1">
                      ${audit.asr_result.map(text => `<li>• ${text}</li>`).join("")}
                    </ul>
                  </div>
                </div>
              ` : ""}

              ${audit.qr_code_result && audit.qr_code_result.length > 0 ? `
                <div class="mb-4">
                  <h6 class="text-sm font-medium text-gray-700 mb-2">二维码检测结果</h6>
                  <div class="bg-gray-50 p-3 rounded-lg">
                    <p class="text-sm text-gray-600">检测到 ${audit.qr_code_result.length} 个二维码</p>
                  </div>
                </div>
              ` : ""}

              <div class="mb-4">
                <h6 class="text-sm font-medium text-gray-700 mb-2">风险评估</h6>
                <div class="bg-gray-50 p-3 rounded-lg">
                  <div class="flex justify-between mb-2">
                    <span class="text-sm text-gray-600">风险等级</span>
                    <span class="text-sm font-medium ${audit.risk_evaluation === 'high' ? 'text-red-500' : audit.risk_evaluation === 'medium' ? 'text-yellow-500' : 'text-green-500'}">${audit.risk_evaluation}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">合规检查</span>
                    <span class="text-sm font-medium ${audit.compliance_check ? 'text-green-500' : 'text-red-500'}">${audit.compliance_check ? '通过' : '未通过'}</span>
                  </div>
                </div>
              </div>

              <div class="mb-4">
                <h6 class="text-sm font-medium text-gray-700 mb-2">审核信息</h6>
                <div class="bg-gray-50 p-3 rounded-lg">
                  <div class="flex justify-between mb-2">
                    <span class="text-sm text-gray-600">审核时间</span>
                    <span class="text-sm text-gray-800">${new Date(audit.audit_time).toLocaleString('zh-CN')}</span>
                  </div>
                  <div class="flex justify-between mb-2">
                    <span class="text-sm text-gray-600">追踪ID</span>
                    <span class="text-sm text-gray-800">${result.trace_id}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">文件MD5</span>
                    <span class="text-sm text-gray-800 font-mono">${result.md5}</span>
                  </div>
                </div>
              </div>
            </div>
          `;

          modal.classList.remove("hidden");

          // 根据状态调整按钮
          const confirmButton = document.getElementById("modal-confirm");
          if (audit.compliance_check) {
            confirmButton.textContent = "下载审核报告";
          } else {
            confirmButton.textContent = "重新上传";
          }
        };

        // 添加到全局作用域，以便HTML中的onclick调用
        window.showDetailModal = function (resultId) {
          const modal = document.getElementById("review-detail-modal");
          const modalContent = document.getElementById("modal-content");
          // 模拟获取详细信息
          const detailInfo = {
            id: resultId,
            fileName:
              resultId === 1
                ? "营销宣传视频.mp4"
                : resultId === 2
                  ? "贷款产品介绍.pdf"
                  : "客户权益说明.jpg",
            fileType: resultId === 1 ? "video" : resultId === 2 ? "pdf" : "image",
            fileSize:
              resultId === 1 ? "35.4 MB" : resultId === 2 ? "2.8 MB" : "1.2 MB",
            uploadTime: "2025-07-04 14:30",
            checkTime: "2025-07-04 14:35",
            reviewStartTime: "2025-07-04 14:40",
            reviewEndTime:
              resultId === 1
                ? "2025-07-04 15:05"
                : resultId === 2
                  ? "2025-07-04 15:10"
                  : "2025-07-04 15:15",
            status: resultId === 2 ? "rejected" : "approved",
            reviewer:
              resultId === 1 ? "李审核" : resultId === 2 ? "王审核" : "赵审核",
            department: "内容审核部",
            comments:
              resultId === 1
                ? "视频内容符合银行宣传规范，画面清晰，音频质量良好，可以用于线上营销活动。"
                : resultId === 2
                  ? "文档中部分条款描述不够清晰，可能导致客户误解。"
                  : "图片内容清晰，表述准确，符合监管要求。",
            checkItems: [
              {
                name: "内容合规性",
                result: resultId === 2 ? "不通过" : "通过",
                comments:
                  resultId === 2 ? "部分内容描述不够清晰" : "内容符合监管要求",
              },
              { name: "品牌一致性", result: "通过", comments: "符合品牌规范" },
              {
                name: "信息准确性",
                result: resultId === 2 ? "不通过" : "通过",
                comments: resultId === 2 ? "第3页利率描述不准确" : "信息准确无误",
              },
              { name: "格式规范", result: "通过", comments: "格式符合要求" },
            ],
          };
          const fileIcon = getFileIconByType(detailInfo.fileType);
          const statusColor =
            detailInfo.status === "approved"
              ? "text-green-500 bg-green-50"
              : "text-red-500 bg-red-50";
          const statusIcon =
            detailInfo.status === "approved"
              ? "ri-checkbox-circle-line"
              : "ri-close-circle-line";
          const statusText =
            detailInfo.status === "approved" ? "审核通过" : "审核不通过";
          modalContent.innerHTML = `
      <div class="flex items-start">
      <div class="w-14 h-14 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
      <i class="${fileIcon} ri-2x text-primary"></i>
      </div>
      <div class="flex-grow">
      <div class="flex justify-between items-center mb-2">
      <h4 class="text-lg font-medium text-gray-800">${detailInfo.fileName}</h4>
      <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
      <i class="${statusIcon} mr-1"></i>
      <span class="text-sm font-medium">${statusText}</span>
      </div>
      </div>
      <p class="text-sm text-gray-500">${detailInfo.fileSize}</p>
      </div>
      </div>
      <div class="border-t border-gray-200 pt-4 mt-4">
      <h5 class="text-md font-medium text-gray-800 mb-3">审核时间线</h5>
      <div class="space-y-3">
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-upload-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">文件上传</p>
      <p class="text-xs text-gray-500">${detailInfo.uploadTime}</p>
      </div>
      </div>
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-shield-check-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">系统校验</p>
      <p class="text-xs text-gray-500">${detailInfo.checkTime}</p>
      </div>
      </div>
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-user-search-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">人工审核开始</p>
      <p class="text-xs text-gray-500">${detailInfo.reviewStartTime}</p>
      </div>
      </div>
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-check-double-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">审核完成</p>
      <p class="text-xs text-gray-500">${detailInfo.reviewEndTime}</p>
      </div>
      </div>
      </div>
      </div>
      <div class="border-t border-gray-200 pt-4 mt-4">
      <h5 class="text-md font-medium text-gray-800 mb-3">审核详情</h5>
      <div class="mb-4">
      <div class="flex justify-between mb-2">
      <p class="text-sm text-gray-600">审核人员</p>
      <p class="text-sm font-medium text-gray-800">${detailInfo.reviewer}</p>
      </div>
      <div class="flex justify-between mb-2">
      <p class="text-sm text-gray-600">所属部门</p>
      <p class="text-sm font-medium text-gray-800">${detailInfo.department}</p>
      </div>
      </div>
      <div class="mb-4">
      <p class="text-sm font-medium text-gray-700 mb-2">审核意见</p>
      <p class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">${detailInfo.comments}</p>
      </div>
      </div>
      <div class="border-t border-gray-200 pt-4 mt-4">
      <h5 class="text-md font-medium text-gray-800 mb-3">审核项目</h5>
      <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
      <thead>
      <tr>
      <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核项</th>
      <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结果</th>
      <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
      </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
      ${detailInfo.checkItems
        .map(
          (item) => `
      <tr>
      <td class="px-4 py-3 text-sm text-gray-700">${item.name}</td>
      <td class="px-4 py-3 text-sm ${item.result === "通过" ? "text-green-500" : "text-red-500"}">${item.result}</td>
      <td class="px-4 py-3 text-sm text-gray-600">${item.comments}</td>
      </tr>
      `,
        )
        .join("")}
      </tbody>
      </table>
      </div>
      </div>
      `;
          modal.classList.remove("hidden");
          // 根据状态调整按钮
          const confirmButton = document.getElementById("modal-confirm");
          if (detailInfo.status === "approved") {
            confirmButton.textContent = "下载审核报告";
          } else {
            confirmButton.textContent = "重新上传";
          }
        };
        window.showReuploadModal = function (resultId) {
          const modal = document.getElementById("review-detail-modal");
          const modalContent = document.getElementById("modal-content");
          const confirmButton = document.getElementById("modal-confirm");
          modalContent.innerHTML = `
      <div class="text-center mb-6">
      <div class="w-16 h-16 mx-auto flex items-center justify-center rounded-full bg-blue-50 mb-4">
      <i class="ri-upload-cloud-2-line ri-2x text-primary"></i>
      </div>
      <h4 class="text-lg font-medium text-gray-800 mb-2">重新上传文件</h4>
      <p class="text-sm text-gray-600">请上传修改后的文件进行重新审核</p>
      </div>
      <div class="upload-area rounded-xl p-6 flex flex-col items-center justify-center text-center cursor-pointer border-2 border-dashed border-gray-300 hover:border-primary">
      <i class="ri-upload-line ri-2x text-gray-400 mb-3"></i>
      <p class="text-sm font-medium text-gray-700 mb-1">点击或拖拽文件到此处</p>
      <p class="text-xs text-gray-500">支持 JPG、PNG、PDF、DOCX、MP4 等格式</p>
      </div>
      <div class="mt-4">
      <div class="flex items-start">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-information-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm text-gray-600">请根据审核意见修改文件内容后重新提交，重新提交的文件将进入新的审核流程。</p>
      </div>
      </div>
      </div>
      `;
          confirmButton.textContent = "提交";
          modal.classList.remove("hidden");
        };
        // 关闭弹窗
        document.getElementById("close-modal").addEventListener("click", function () {
          document.getElementById("review-detail-modal").classList.add("hidden");
        });
        document
          .getElementById("modal-cancel")
          .addEventListener("click", function () {
            document.getElementById("review-detail-modal").classList.add("hidden");
          });
        document
          .getElementById("modal-confirm")
          .addEventListener("click", function () {
            // 根据按钮文本执行不同操作
            const buttonText = this.textContent;
            if (buttonText === "下载审核报告") {
              // 模拟下载报告
              const downloadLink = document.createElement("a");
              downloadLink.href = "#";
              downloadLink.download = "审核报告.pdf";
              downloadLink.click();
            } else if (buttonText === "重新上传" || buttonText === "提交") {
              // 模拟重新上传
              document.getElementById("review-detail-modal").classList.add("hidden");
              // 可以在这里添加重新上传的逻辑
            }
            document.getElementById("review-detail-modal").classList.add("hidden");
          });
      });
    </script>
  </body>
</html>


