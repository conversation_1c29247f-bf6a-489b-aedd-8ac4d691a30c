<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频审核系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 40px; 
            text-align: center; 
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover { border-color: #007bff; }
        .result { margin: 20px 0; padding: 10px; background: #f8f9fa; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>视频审核系统演示</h1>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>点击或拖拽文件到此处上传</p>
        <input type="file" id="fileInput" style="display: none" multiple>
    </div>
    
    <div id="results"></div>
    
    <script>
        const fileInput = document.getElementById('fileInput');
        const results = document.getElementById('results');
        
        fileInput.addEventListener('change', handleFiles);
        
        function handleFiles(event) {
            const files = event.target.files;
            results.innerHTML = '<h3>上传的文件:</h3>';
            
            Array.from(files).forEach(file => {
                const div = document.createElement('div');
                div.className = 'result';
                div.innerHTML = `
                    <strong>文件名:</strong> ${file.name}<br>
                    <strong>大小:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                    <strong>类型:</strong> ${file.type}<br>
                    <strong>状态:</strong> 等待审核...
                `;
                results.appendChild(div);
                
                // 模拟审核过程
                setTimeout(() => {
                    div.innerHTML += '<br><strong>审核结果:</strong> <span style="color: green;">通过</span>';
                }, 2000);
            });
        }
        
        // 拖拽功能
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            fileInput.files = e.dataTransfer.files;
            handleFiles({ target: { files: e.dataTransfer.files } });
        });
    </script>
</body>
</html>