#!/usr/bin/env python3
"""
测试OCR功能，调试数据格式问题
"""
import cv2
from rapidocr_onnxruntime import RapidOCR
import json

def test_ocr_format():
    """测试OCR返回格式"""
    print("🔍 测试OCR返回格式")
    
    try:
        # 初始化OCR
        ocr_engine = RapidOCR()
        print("✅ OCR引擎初始化成功")
        
        # 测试图片
        image_path = "test_files/银行宣传图.png"
        image = cv2.imread(image_path)
        
        if image is None:
            print("❌ 图片读取失败")
            return
        
        print(f"📊 图片尺寸: {image.shape}")
        
        # 进行OCR
        print("📝 进行OCR识别...")
        ocr_results = ocr_engine(image)
        
        print(f"📋 OCR结果类型: {type(ocr_results)}")
        print(f"📋 OCR结果长度: {len(ocr_results) if ocr_results else 0}")
        
        if ocr_results:
            print("📋 OCR结果详情:")
            for i, item in enumerate(ocr_results[:3]):  # 只显示前3个
                print(f"  项目 {i+1}:")
                print(f"    类型: {type(item)}")
                print(f"    长度: {len(item) if hasattr(item, '__len__') else 'N/A'}")
                print(f"    内容: {item}")
                
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    print(f"    文字: {item[1]}")
                    if len(item) > 2:
                        print(f"    置信度: {item[2]} (类型: {type(item[2])})")
                print()
        else:
            print("⚠️  OCR未返回结果")
            
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_video_frame():
    """测试视频帧提取"""
    print("\n🎬 测试视频帧提取")
    
    try:
        from moviepy.editor import VideoFileClip
        
        # 创建一个简单的测试视频帧
        import numpy as np
        
        # 创建一个包含文字的测试图像
        test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 添加一些文字（使用OpenCV）
        cv2.putText(test_image, "Test OCR Text", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(test_image, "众安贷", (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(test_image, "理财产品", (50, 300), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 保存测试图像
        cv2.imwrite("test_frame.png", test_image)
        print("✅ 测试帧已保存: test_frame.png")
        
        # 测试OCR
        ocr_engine = RapidOCR()
        ocr_results = ocr_engine(test_image)
        
        print(f"📋 测试帧OCR结果: {ocr_results}")
        
        if ocr_results:
            for item in ocr_results:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    text = str(item[1])
                    confidence = item[2] if len(item) > 2 else 0.8
                    print(f"  识别文字: '{text}' (置信度: {confidence})")
        
    except Exception as e:
        print(f"❌ 视频帧测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_upload_fixed_image():
    """测试上传修复后的图片"""
    print("\n📤 测试上传修复后的图片")
    
    try:
        import requests
        
        # 上传测试帧
        with open("test_frame.png", "rb") as f:
            files = {"file": ("test_frame.png", f, "image/png")}
            response = requests.post("http://localhost:8080/upload", files=files)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 上传成功")
                print(f"📝 OCR结果: {result['audit_result']['ocr_result']}")
                print(f"📊 风险等级: {result['audit_result']['risk_evaluation']}")
            else:
                print(f"❌ 上传失败: {response.status_code}")
                print(f"错误: {response.text}")
                
    except Exception as e:
        print(f"❌ 上传测试失败: {e}")

if __name__ == "__main__":
    print("🧪 OCR调试测试")
    print("=" * 50)
    
    test_ocr_format()
    test_video_frame()
    test_upload_fixed_image()
    
    print("\n" + "=" * 50)
    print("🎉 OCR调试测试完成")
