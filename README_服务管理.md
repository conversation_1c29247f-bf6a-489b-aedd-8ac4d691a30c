# 视频分析服务管理脚本

## 📋 脚本说明

本项目提供了三个便捷的服务管理脚本，帮助您轻松管理ASR服务和视频分析服务。

### 🚀 启动服务脚本 - `start_services.sh`

**功能**: 一键启动ASR服务和视频分析服务

**使用方法**:
```bash
cd /Users/<USER>/Documents/work/augment-projects/material-review
./start_services.sh
```

**启动的服务**:
- 🎤 ASR服务 (端口8080): http://localhost:8080
- 🎬 视频分析服务 (端口8081): http://localhost:8081

**功能特性**:
- ✅ 自动检查Python环境
- ✅ 自动检查项目目录
- ✅ 自动检查端口占用并清理
- ✅ 后台启动服务并记录PID
- ✅ 等待服务启动完成
- ✅ 显示服务状态和访问地址

### 🛑 停止服务脚本 - `stop_services.sh`

**功能**: 停止所有相关服务

**使用方法**:
```bash
./stop_services.sh                # 停止所有服务
./stop_services.sh --clean-logs   # 停止服务并清理日志
./stop_services.sh --help         # 显示帮助
```

**功能特性**:
- ✅ 通过PID文件优雅停止服务
- ✅ 通过端口强制停止服务
- ✅ 清理相关Python进程
- ✅ 可选清理日志文件
- ✅ 显示停止后的状态

### 📊 状态检查脚本 - `check_services.sh`

**功能**: 检查服务运行状态和健康情况

**使用方法**:
```bash
./check_services.sh
```

**检查内容**:
- 🔌 端口占用情况
- 🏥 服务健康状态 (HTTP响应)
- 📋 日志文件状态
- 💾 PID文件检查
- 📈 性能监控 (内存、CPU使用)
- 💽 磁盘空间使用

## 🌐 服务访问地址

启动成功后，您可以访问以下地址：

| 服务 | 地址 | 说明 |
|------|------|------|
| Demo页面 | http://localhost:8081/demo | 视频上传和分析界面 |
| API文档 | http://localhost:8081/docs | FastAPI自动生成的API文档 |
| 健康检查 | http://localhost:8081/health | 视频分析服务健康状态 |
| ASR健康检查 | http://localhost:8080/health | ASR服务健康状态 |

## 📋 日志文件

日志文件保存在 `logs/` 目录下：

- **ASR服务日志**: `logs/asr_service.log`
- **视频分析服务日志**: `logs/video_service.log`

**查看实时日志**:
```bash
# 查看ASR服务日志
tail -f logs/asr_service.log

# 查看视频分析服务日志
tail -f logs/video_service.log
```

## 🔧 常用操作

### 完整的服务管理流程

```bash
# 1. 启动所有服务
./start_services.sh

# 2. 检查服务状态
./check_services.sh

# 3. 使用服务 (访问 http://localhost:8081/demo)

# 4. 停止所有服务
./stop_services.sh
```

### 故障排除

**如果服务启动失败**:
1. 检查日志文件: `tail -f logs/asr_service.log`
2. 检查端口占用: `lsof -i :8080` 或 `lsof -i :8081`
3. 手动停止服务: `./stop_services.sh`
4. 重新启动: `./start_services.sh`

**如果端口被占用**:
```bash
# 查看占用端口的进程
lsof -i :8080
lsof -i :8081

# 强制停止
./stop_services.sh
```

## 🎯 服务功能

### ASR服务 (8080端口)
- ✅ 完整FunASR模型 (包含标点符号模型)
- ✅ 语音识别和转文字
- ✅ 热词支持
- ✅ 时间戳信息

### 视频分析服务 (8081端口)
- ✅ 视频帧提取和OCR识别
- ✅ 人脸检测
- ✅ ASR语音识别
- ✅ 二维码检测
- ✅ 综合风险评估

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行 `./check_services.sh` 检查服务状态
3. 确保Python环境和依赖包正确安装

---

**作者**: Augment Agent  
**更新时间**: 2025-07-29
