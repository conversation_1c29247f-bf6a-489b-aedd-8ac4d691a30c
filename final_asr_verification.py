#!/usr/bin/env python3
"""
最终ASR功能验证
"""
import requests
import json

def verify_asr_service():
    """验证ASR服务状态"""
    print("🎤 ASR服务状态验证")
    print("=" * 50)
    
    # 检查ASR服务
    try:
        response = requests.get("http://localhost:8080/health")
        if response.status_code == 200:
            print("✅ ASR服务 (8080端口): 运行正常")
        else:
            print(f"❌ ASR服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ ASR服务连接失败: {e}")
        return False
    
    # 检查视频分析服务
    try:
        response = requests.get("http://localhost:8081/health")
        if response.status_code == 200:
            result = response.json()
            asr_available = result.get('capabilities', {}).get('asr_service', False)
            if asr_available:
                print("✅ 视频分析服务 (8081端口): ASR已连接")
                return True
            else:
                print("❌ 视频分析服务: ASR未连接")
                return False
        else:
            print(f"❌ 视频分析服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 视频分析服务连接失败: {e}")
        return False

def test_asr_with_real_video():
    """使用真实视频测试ASR功能"""
    print("\n🎬 真实视频ASR测试")
    print("=" * 50)
    
    try:
        # 创建一个小的测试视频内容
        test_content = b"fake video content for asr testing"
        files = {"file": ("众安贷测试视频.mp4", test_content, "video/mp4")}
        
        print("📤 上传测试视频...")
        response = requests.post("http://localhost:8081/upload", files=files)
        
        if response.status_code == 200:
            result = response.json()
            audit_result = result['audit_result']
            asr_result = audit_result.get('asr_result', [])
            
            print("✅ 视频分析完成")
            print(f"🎤 ASR结果数量: {len(asr_result)}")
            
            # 检查ASR结果质量
            if len(asr_result) >= 2:  # 至少有时长信息和其他内容
                non_meta_results = [r for r in asr_result if not r.startswith("视频时长") and not r.startswith("ASR")]
                
                if non_meta_results:
                    print("🎉 ASR功能正常！获得了语音识别结果")
                    print("📝 ASR结果预览:")
                    for i, text in enumerate(asr_result[:3], 1):
                        print(f"  {i}. {text}")
                    return True
                else:
                    print("⚠️  ASR返回基础信息，但可能没有识别到语音内容")
            else:
                print("❌ ASR结果不足，可能服务有问题")
                print(f"📝 实际ASR结果: {asr_result}")
            
            return False
        else:
            print(f"❌ 视频分析失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def show_service_status():
    """显示服务状态总结"""
    print("\n📊 服务状态总结")
    print("=" * 50)
    
    services = [
        ("ASR服务", "http://localhost:8080/health"),
        ("视频分析服务", "http://localhost:8081/health")
    ]
    
    for name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 运行正常")
            else:
                print(f"❌ {name}: 状态异常 ({response.status_code})")
        except Exception as e:
            print(f"❌ {name}: 连接失败")

def main():
    print("🔍 最终ASR功能验证")
    print("=" * 60)
    
    # 1. 验证服务状态
    asr_ready = verify_asr_service()
    
    if not asr_ready:
        print("\n❌ ASR服务未就绪，请检查服务状态")
        show_service_status()
        return
    
    # 2. 测试ASR功能
    asr_working = test_asr_with_real_video()
    
    # 3. 显示结果
    print("\n" + "=" * 60)
    if asr_ready and asr_working:
        print("🎉 ASR功能验证成功！")
        print("\n✅ 现在可以获得真实的ASR语音识别结果")
        print("✅ 包含文本优化和错误修正功能")
        print("\n🌐 立即测试: http://localhost:8081/demo")
        print("📤 上传您的MP4视频文件查看完整ASR结果")
    else:
        print("⚠️  ASR功能验证未完全成功")
        print("请检查服务状态或重新启动服务")
    
    show_service_status()
    print("=" * 60)

if __name__ == "__main__":
    main()
