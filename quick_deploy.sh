#!/bin/bash

echo "⚡ 快速部署远程代理服务"
echo "================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 生成API密钥
API_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))" 2>/dev/null || openssl rand -hex 32)

echo -e "${YELLOW}选择部署方式:${NC}"
echo "1. 直接Python部署 (推荐)"
echo "2. Docker部署"
echo "3. 后台进程部署"

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo -e "\n${YELLOW}🐍 Python直接部署...${NC}"
        
        # 安装依赖
        pip3 install fastapi uvicorn requests pydantic
        
        # 修改API密钥
        sed -i "s/API_KEY = \"remote-agent-2024-secure-key\"/API_KEY = \"$API_KEY\"/" remote_agent.py
        
        # 启动服务
        echo -e "${GREEN}启动服务...${NC}"
        nohup python3 remote_agent.py > /tmp/remote_agent.log 2>&1 &
        PID=$!
        echo $PID > /tmp/remote_agent.pid
        
        sleep 3
        
        if kill -0 $PID 2>/dev/null; then
            echo -e "${GREEN}✓ 服务启动成功 (PID: $PID)${NC}"
        else
            echo -e "${RED}✗ 服务启动失败${NC}"
            exit 1
        fi
        ;;
        
    2)
        echo -e "\n${YELLOW}🐳 Docker部署...${NC}"
        
        # 检查Docker
        if ! command -v docker &> /dev/null; then
            echo -e "${RED}✗ Docker未安装${NC}"
            exit 1
        fi
        
        # 修改API密钥
        sed -i "s/API_KEY = \"remote-agent-2024-secure-key\"/API_KEY = \"$API_KEY\"/" remote_agent.py
        
        # 构建并启动
        docker build -f Dockerfile.remote-agent -t remote-agent .
        docker run -d --name remote-agent --network host \
            -v /alidata1/admin/material-review:/workspace \
            -v /tmp:/tmp \
            remote-agent
        
        sleep 3
        
        if docker ps | grep remote-agent > /dev/null; then
            echo -e "${GREEN}✓ Docker容器启动成功${NC}"
        else
            echo -e "${RED}✗ Docker容器启动失败${NC}"
            docker logs remote-agent
            exit 1
        fi
        ;;
        
    3)
        echo -e "\n${YELLOW}🔧 后台进程部署...${NC}"
        
        # 安装依赖
        pip3 install fastapi uvicorn requests pydantic
        
        # 修改API密钥
        sed -i "s/API_KEY = \"remote-agent-2024-secure-key\"/API_KEY = \"$API_KEY\"/" remote_agent.py
        
        # 创建启动脚本
        cat > start_remote_agent.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
python3 remote_agent.py
EOF
        chmod +x start_remote_agent.sh
        
        # 使用screen启动
        if command -v screen &> /dev/null; then
            screen -dmS remote-agent ./start_remote_agent.sh
            echo -e "${GREEN}✓ 服务在screen会话中启动${NC}"
            echo -e "查看会话: screen -r remote-agent"
        else
            nohup ./start_remote_agent.sh > /tmp/remote_agent.log 2>&1 &
            echo $! > /tmp/remote_agent.pid
            echo -e "${GREEN}✓ 服务在后台启动${NC}"
        fi
        ;;
        
    *)
        echo -e "${RED}无效选择${NC}"
        exit 1
        ;;
esac

# 等待服务启动
echo -e "\n${YELLOW}等待服务启动...${NC}"
sleep 5

# 测试服务
echo -e "${YELLOW}测试服务...${NC}"
if curl -s http://localhost:9999/health > /dev/null; then
    echo -e "${GREEN}✓ 服务响应正常${NC}"
else
    echo -e "${RED}✗ 服务无响应，检查日志...${NC}"
    tail -20 /tmp/remote_agent.log
    exit 1
fi

# 获取IP信息
LOCAL_IP=$(hostname -I | awk '{print $1}')
PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || echo "无法获取")

# 显示结果
echo -e "\n${BLUE}================================${NC}"
echo -e "${GREEN}🎉 部署成功！${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "本地地址: http://$LOCAL_IP:9999"
echo -e "公网地址: http://$PUBLIC_IP:9999"
echo -e "API密钥: $API_KEY"
echo -e "健康检查: curl http://localhost:9999/health"
echo -e "${BLUE}================================${NC}"

# 保存配置
cat > remote_config.json << EOF
{
    "server_url": "http://$PUBLIC_IP:9999",
    "api_key": "$API_KEY",
    "local_url": "http://$LOCAL_IP:9999",
    "deployed_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

echo -e "${YELLOW}配置已保存到: remote_config.json${NC}"

# 创建测试脚本
cat > test_remote_agent.py << EOF
#!/usr/bin/env python3
import requests
import json

# 配置
SERVER_URL = "http://localhost:9999"
API_KEY = "$API_KEY"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# 测试健康检查
print("🔍 测试健康检查...")
response = requests.get(f"{SERVER_URL}/health")
print(f"状态: {response.status_code}")
print(f"响应: {response.json()}")

# 测试命令执行
print("\n🔍 测试命令执行...")
response = requests.post(
    f"{SERVER_URL}/execute",
    headers=headers,
    json={"command": "pwd"}
)
print(f"状态: {response.status_code}")
print(f"响应: {response.json()}")

print("\n✅ 测试完成！")
EOF

chmod +x test_remote_agent.py

echo -e "\n${YELLOW}运行测试: python3 test_remote_agent.py${NC}"
echo -e "${GREEN}现在AI助手可以通过API直接操作您的服务器了！${NC}"
