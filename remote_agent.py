#!/usr/bin/env python3
"""
远程代理服务 - 让AI助手能够直接操作服务器
安全特性：
1. API密钥认证
2. 命令白名单
3. 路径限制
4. 操作日志
"""

import os
import sys
import subprocess
import json
import time
import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, Depends, Security
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 配置
API_KEY = "remote-agent-2024-secure-key"  # 请修改为复杂密钥
ALLOWED_PATHS = ["/alidata1/admin/material-review"]  # 允许操作的路径
LOG_FILE = "/tmp/remote_agent.log"

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="远程代理服务", description="AI助手远程操作接口")
security = HTTPBearer()

# 添加CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class CommandRequest(BaseModel):
    command: str
    cwd: Optional[str] = None
    timeout: int = 30

class FileRequest(BaseModel):
    path: str
    content: Optional[str] = None
    mode: str = "read"  # read, write, append

class ServiceRequest(BaseModel):
    action: str  # start, stop, restart, status
    service: str  # asr, video, all

# 安全验证
def verify_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    if credentials.credentials != API_KEY:
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return credentials.credentials

def is_path_allowed(path: str) -> bool:
    """检查路径是否在允许范围内"""
    abs_path = os.path.abspath(path)
    return any(abs_path.startswith(allowed) for allowed in ALLOWED_PATHS)

def log_operation(operation: str, details: Dict[str, Any], user: str = "ai_agent"):
    """记录操作日志"""
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "user": user,
        "operation": operation,
        "details": details
    }
    logger.info(f"OPERATION: {json.dumps(log_entry)}")

@app.get("/")
async def root():
    return {
        "service": "远程代理服务",
        "status": "运行中",
        "version": "1.0.0",
        "time": datetime.now().isoformat()
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/execute")
async def execute_command(request: CommandRequest, token: str = Depends(verify_token)):
    """执行系统命令"""
    try:
        # 安全检查
        dangerous_commands = ["rm -rf", "dd if=", "mkfs", "fdisk", "shutdown", "reboot"]
        if any(cmd in request.command.lower() for cmd in dangerous_commands):
            raise HTTPException(status_code=403, detail="危险命令被禁止")
        
        # 设置工作目录
        cwd = request.cwd or "/alidata1/admin/material-review"
        if not is_path_allowed(cwd):
            raise HTTPException(status_code=403, detail="路径不在允许范围内")
        
        log_operation("execute_command", {
            "command": request.command,
            "cwd": cwd,
            "timeout": request.timeout
        })
        
        # 执行命令
        result = subprocess.run(
            request.command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=request.timeout
        )
        
        return {
            "success": True,
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "command": request.command,
            "cwd": cwd
        }
        
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "error": "命令执行超时",
            "timeout": request.timeout
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/file")
async def file_operation(request: FileRequest, token: str = Depends(verify_token)):
    """文件操作"""
    try:
        if not is_path_allowed(request.path):
            raise HTTPException(status_code=403, detail="路径不在允许范围内")
        
        log_operation("file_operation", {
            "path": request.path,
            "mode": request.mode
        })
        
        if request.mode == "read":
            if not os.path.exists(request.path):
                return {"success": False, "error": "文件不存在"}
            
            with open(request.path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                "success": True,
                "content": content,
                "size": len(content),
                "path": request.path
            }
        
        elif request.mode == "write":
            os.makedirs(os.path.dirname(request.path), exist_ok=True)
            with open(request.path, 'w', encoding='utf-8') as f:
                f.write(request.content or "")
            
            return {
                "success": True,
                "message": "文件写入成功",
                "path": request.path
            }
        
        elif request.mode == "append":
            with open(request.path, 'a', encoding='utf-8') as f:
                f.write(request.content or "")
            
            return {
                "success": True,
                "message": "文件追加成功",
                "path": request.path
            }
        
        else:
            return {"success": False, "error": "不支持的操作模式"}
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/list/{path:path}")
async def list_directory(path: str, token: str = Depends(verify_token)):
    """列出目录内容"""
    try:
        full_path = f"/{path}" if not path.startswith('/') else path
        if not is_path_allowed(full_path):
            raise HTTPException(status_code=403, detail="路径不在允许范围内")
        
        if not os.path.exists(full_path):
            return {"success": False, "error": "路径不存在"}
        
        if not os.path.isdir(full_path):
            return {"success": False, "error": "不是目录"}
        
        items = []
        for item in os.listdir(full_path):
            item_path = os.path.join(full_path, item)
            stat = os.stat(item_path)
            items.append({
                "name": item,
                "type": "directory" if os.path.isdir(item_path) else "file",
                "size": stat.st_size,
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
            })
        
        return {
            "success": True,
            "path": full_path,
            "items": items
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/service")
async def service_control(request: ServiceRequest, token: str = Depends(verify_token)):
    """服务控制"""
    try:
        log_operation("service_control", {
            "action": request.action,
            "service": request.service
        })
        
        cwd = "/alidata1/admin/material-review"
        
        if request.action == "status":
            # 检查服务状态
            ps_result = subprocess.run(
                "ps aux | grep -E 'python.*808[01]' | grep -v grep",
                shell=True, capture_output=True, text=True
            )
            
            port_result = subprocess.run(
                "netstat -tlnp | grep -E ':808[01]'",
                shell=True, capture_output=True, text=True
            )
            
            return {
                "success": True,
                "processes": ps_result.stdout,
                "ports": port_result.stdout
            }
        
        elif request.action == "start":
            script = "./start_services_conda.sh"
            result = subprocess.run(script, shell=True, cwd=cwd, capture_output=True, text=True)
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
        
        elif request.action == "stop":
            script = "./stop_services.sh"
            result = subprocess.run(script, shell=True, cwd=cwd, capture_output=True, text=True)
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
        
        elif request.action == "restart":
            # 先停止
            subprocess.run("./stop_services.sh", shell=True, cwd=cwd)
            time.sleep(2)
            # 再启动
            result = subprocess.run("./start_services_conda.sh", shell=True, cwd=cwd, capture_output=True, text=True)
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
        
        else:
            return {"success": False, "error": "不支持的操作"}
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/logs/{service}")
async def get_logs(service: str, lines: int = 50, token: str = Depends(verify_token)):
    """获取服务日志"""
    try:
        log_files = {
            "video": "/alidata1/admin/material-review/logs/video_conda.log",
            "asr": "/alidata1/admin/material-review/logs/asr_conda.log",
            "agent": LOG_FILE
        }
        
        if service not in log_files:
            return {"success": False, "error": "未知的服务"}
        
        log_file = log_files[service]
        if not os.path.exists(log_file):
            return {"success": False, "error": "日志文件不存在"}
        
        result = subprocess.run(
            f"tail -{lines} {log_file}",
            shell=True, capture_output=True, text=True
        )
        
        return {
            "success": True,
            "service": service,
            "lines": lines,
            "content": result.stdout
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    print("🚀 启动远程代理服务...")
    print(f"📝 日志文件: {LOG_FILE}")
    print(f"🔑 API密钥: {API_KEY}")
    print("⚠️  请确保修改API_KEY为复杂密钥！")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=9999,
        log_level="info"
    )
