#!/bin/bash

echo "🚀 部署远程代理服务"
echo "================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查Python环境
echo -e "${YELLOW}1. 检查Python环境...${NC}"
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    echo -e "${GREEN}✓ 找到Python3${NC}"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    echo -e "${GREEN}✓ 找到Python${NC}"
else
    echo -e "${RED}✗ 未找到Python，请先安装Python${NC}"
    exit 1
fi

$PYTHON_CMD --version

# 安装依赖
echo -e "\n${YELLOW}2. 安装依赖...${NC}"
$PYTHON_CMD -m pip install fastapi uvicorn requests pydantic

# 生成安全的API密钥
echo -e "\n${YELLOW}3. 生成API密钥...${NC}"
API_KEY=$(openssl rand -hex 32 2>/dev/null || python3 -c "import secrets; print(secrets.token_hex(32))")
echo -e "${GREEN}生成的API密钥: $API_KEY${NC}"

# 修改remote_agent.py中的API密钥
echo -e "\n${YELLOW}4. 配置API密钥...${NC}"
if [ -f "remote_agent.py" ]; then
    sed -i "s/API_KEY = \"remote-agent-2024-secure-key\"/API_KEY = \"$API_KEY\"/" remote_agent.py
    echo -e "${GREEN}✓ API密钥已配置${NC}"
else
    echo -e "${RED}✗ remote_agent.py文件不存在${NC}"
    exit 1
fi

# 创建systemd服务文件
echo -e "\n${YELLOW}5. 创建系统服务...${NC}"
CURRENT_DIR=$(pwd)
SERVICE_FILE="/etc/systemd/system/remote-agent.service"

sudo tee $SERVICE_FILE > /dev/null << EOF
[Unit]
Description=Remote Agent Service for AI Assistant
After=network.target

[Service]
Type=simple
User=admin
WorkingDirectory=$CURRENT_DIR
Environment=PATH=/usr/bin:/usr/local/bin
ExecStart=$PYTHON_CMD $CURRENT_DIR/remote_agent.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

echo -e "${GREEN}✓ 系统服务文件已创建${NC}"

# 启动服务
echo -e "\n${YELLOW}6. 启动服务...${NC}"
sudo systemctl daemon-reload
sudo systemctl enable remote-agent
sudo systemctl start remote-agent

# 等待服务启动
sleep 3

# 检查服务状态
echo -e "\n${YELLOW}7. 检查服务状态...${NC}"
if sudo systemctl is-active --quiet remote-agent; then
    echo -e "${GREEN}✓ 远程代理服务运行正常${NC}"
else
    echo -e "${RED}✗ 远程代理服务启动失败${NC}"
    echo "查看日志:"
    sudo journalctl -u remote-agent --no-pager -n 20
    exit 1
fi

# 检查端口
echo -e "\n${YELLOW}8. 检查端口...${NC}"
if netstat -tlnp | grep ":9999" > /dev/null; then
    echo -e "${GREEN}✓ 端口9999已监听${NC}"
else
    echo -e "${RED}✗ 端口9999未监听${NC}"
fi

# 测试服务
echo -e "\n${YELLOW}9. 测试服务...${NC}"
sleep 2
if curl -s http://localhost:9999/health > /dev/null; then
    echo -e "${GREEN}✓ 服务响应正常${NC}"
else
    echo -e "${RED}✗ 服务无响应${NC}"
fi

# 获取服务器IP
echo -e "\n${YELLOW}10. 获取服务器信息...${NC}"
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "无法获取公网IP")
LOCAL_IP=$(hostname -I | awk '{print $1}')

echo -e "${BLUE}================================${NC}"
echo -e "${GREEN}🎉 远程代理服务部署完成！${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "服务地址: http://$LOCAL_IP:9999"
echo -e "公网地址: http://$SERVER_IP:9999 (如果开放了端口)"
echo -e "API密钥: $API_KEY"
echo -e "日志文件: /tmp/remote_agent.log"
echo -e "${BLUE}================================${NC}"

# 保存配置信息
cat > remote_agent_config.txt << EOF
远程代理服务配置信息
====================
部署时间: $(date)
服务地址: http://$LOCAL_IP:9999
公网地址: http://$SERVER_IP:9999
API密钥: $API_KEY
工作目录: $CURRENT_DIR
日志文件: /tmp/remote_agent.log

管理命令:
启动服务: sudo systemctl start remote-agent
停止服务: sudo systemctl stop remote-agent
重启服务: sudo systemctl restart remote-agent
查看状态: sudo systemctl status remote-agent
查看日志: sudo journalctl -u remote-agent -f
EOF

echo -e "${YELLOW}配置信息已保存到: remote_agent_config.txt${NC}"

# 防火墙提示
echo -e "\n${YELLOW}⚠️  重要提示:${NC}"
echo -e "1. 如需外网访问，请开放端口9999"
echo -e "2. 阿里云安全组需要添加9999端口规则"
echo -e "3. 请妥善保管API密钥"
echo -e "4. 建议定期更换API密钥"

echo -e "\n${GREEN}部署完成！现在AI助手可以通过API直接操作服务器了。${NC}"
