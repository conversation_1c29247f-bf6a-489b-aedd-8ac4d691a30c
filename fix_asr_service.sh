#!/bin/bash

echo "🎤 ASR服务专项修复脚本"
echo "================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 设置工作目录
WORK_DIR="/alidata1/admin/material-review"
cd "$WORK_DIR" || exit 1

# 1. 检查ASR项目目录
echo -e "\n${YELLOW}1. 检查ASR项目目录...${NC}"
if [ -d "za-video-asr-master" ]; then
    echo "✓ 找到ASR项目: za-video-asr-master"
    cd za-video-asr-master
    ls -la
else
    echo "✗ ASR项目目录不存在"
    exit 1
fi

# 2. 检查ASR配置
echo -e "\n${YELLOW}2. 检查ASR配置...${NC}"
if [ -f "config.py" ]; then
    echo "✓ 找到config.py"
    cat config.py | head -20
fi

if [ -f "run.sh" ]; then
    echo "✓ 找到run.sh"
    cat run.sh
fi

# 3. 安装ASR依赖
echo -e "\n${YELLOW}3. 安装ASR依赖...${NC}"
if [ -f "requirements.txt" ]; then
    echo "安装requirements.txt中的依赖..."
    pip install -r requirements.txt
fi

# 安装常用ASR依赖
pip install funasr modelscope torch torchaudio librosa soundfile

# 4. 检查ASR模型
echo -e "\n${YELLOW}4. 检查ASR模型...${NC}"
if [ -d "funasr_algorithm" ]; then
    echo "✓ 找到funasr_algorithm目录"
    find funasr_algorithm -name "*.py" | head -5
fi

# 5. 创建简化的ASR服务
echo -e "\n${YELLOW}5. 创建简化ASR服务...${NC}"
cat > simple_asr_server.py << 'EOF'
#!/usr/bin/env python3
"""
简化的ASR服务
"""
import os
import sys
import json
import time
import logging
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="ASR语音识别服务", description="音频转文字服务")

# 添加CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
asr_model = None

def init_asr_model():
    """初始化ASR模型"""
    global asr_model
    try:
        # 尝试导入FunASR
        try:
            from funasr import AutoModel
            asr_model = AutoModel(model="paraformer-zh", device="cpu")
            logger.info("✓ FunASR模型加载成功")
            return True
        except ImportError:
            logger.warning("FunASR未安装，使用模拟模式")
        except Exception as e:
            logger.warning(f"FunASR加载失败: {e}")
        
        # 备用方案：模拟ASR
        asr_model = "mock"
        logger.info("✓ 使用模拟ASR模式")
        return True
        
    except Exception as e:
        logger.error(f"ASR初始化失败: {e}")
        return False

@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    logger.info("🚀 启动ASR服务...")
    init_asr_model()

@app.get("/")
async def root():
    return {
        "service": "ASR语音识别服务",
        "status": "运行中",
        "model": "FunASR" if asr_model != "mock" else "模拟模式",
        "version": "1.0.0"
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "model_loaded": asr_model is not None,
        "timestamp": time.time()
    }

@app.post("/asr")
async def speech_recognition(file: UploadFile = File(...)):
    """语音识别接口"""
    try:
        if not file.filename.lower().endswith(('.wav', '.mp3', '.m4a', '.flac')):
            raise HTTPException(status_code=400, detail="不支持的音频格式")
        
        # 保存上传的文件
        temp_path = f"/tmp/{file.filename}"
        with open(temp_path, "wb") as f:
            content = await file.read()
            f.write(content)
        
        # 进行语音识别
        if asr_model == "mock":
            # 模拟识别结果
            result_text = f"这是对音频文件 {file.filename} 的模拟识别结果。实际部署时会使用真实的ASR模型。"
        else:
            # 使用真实ASR模型
            try:
                result = asr_model.generate(input=temp_path)
                result_text = result[0]["text"] if result and len(result) > 0 else "识别失败"
            except Exception as e:
                logger.error(f"ASR识别错误: {e}")
                result_text = f"识别失败: {str(e)}"
        
        # 清理临时文件
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        return {
            "success": True,
            "filename": file.filename,
            "text": result_text,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"ASR处理错误: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.post("/asr/batch")
async def batch_speech_recognition(files: list[UploadFile] = File(...)):
    """批量语音识别"""
    results = []
    for file in files:
        try:
            result = await speech_recognition(file)
            results.append(result)
        except Exception as e:
            results.append({
                "success": False,
                "filename": file.filename,
                "error": str(e)
            })
    
    return {
        "success": True,
        "results": results,
        "total": len(files),
        "timestamp": time.time()
    }

if __name__ == "__main__":
    print("🎤 启动ASR服务...")
    print("服务地址: http://localhost:8080")
    print("API文档: http://localhost:8080/docs")
    print("健康检查: http://localhost:8080/health")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
EOF

# 6. 创建ASR启动脚本
echo -e "\n${YELLOW}6. 创建ASR启动脚本...${NC}"
cat > start_asr_simple.sh << 'EOF'
#!/bin/bash

echo "🎤 启动简化ASR服务..."

# 激活conda环境
if command -v conda &> /dev/null; then
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate py39 2>/dev/null || echo "无法激活py39环境"
fi

# 设置环境变量
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 启动服务
nohup python simple_asr_server.py > ../logs/asr_simple.log 2>&1 &
ASR_PID=$!

echo "ASR服务已启动，PID: $ASR_PID"
echo $ASR_PID > ../logs/asr.pid

# 等待启动
sleep 3

# 检查服务
if kill -0 $ASR_PID 2>/dev/null; then
    echo "✓ ASR服务启动成功"
    echo "服务地址: http://localhost:8080"
    echo "健康检查: curl http://localhost:8080/health"
else
    echo "✗ ASR服务启动失败"
    cat ../logs/asr_simple.log
fi
EOF

chmod +x start_asr_simple.sh

# 7. 启动ASR服务
echo -e "\n${YELLOW}7. 启动ASR服务...${NC}"
./start_asr_simple.sh

# 8. 测试ASR服务
echo -e "\n${YELLOW}8. 测试ASR服务...${NC}"
sleep 5

if curl -s http://localhost:8080/health > /dev/null; then
    echo -e "${GREEN}✓ ASR服务响应正常${NC}"
    curl -s http://localhost:8080/health | python -m json.tool
else
    echo -e "${RED}✗ ASR服务无响应${NC}"
    echo "查看日志:"
    tail -20 ../logs/asr_simple.log
fi

# 9. 返回主目录
cd "$WORK_DIR"

echo -e "\n${BLUE}================================${NC}"
echo -e "${GREEN}🎤 ASR服务修复完成！${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "ASR服务: http://localhost:8080"
echo -e "API文档: http://localhost:8080/docs"
echo -e "健康检查: http://localhost:8080/health"
echo -e "${BLUE}================================${NC}"
