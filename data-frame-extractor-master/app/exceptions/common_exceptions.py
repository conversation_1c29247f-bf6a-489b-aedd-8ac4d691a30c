from app.exceptions.error_code import MAP_STATUS_CODE_TO_MSG


class CommonException(Exception):
    """
    err_code 错误代码
    err_msg 错误信息
    """

    def __init__(self, err_code) -> None:
        super().__init__()
        self.err_code = err_code
        self.err_msg = MAP_STATUS_CODE_TO_MSG.get(err_code)

    def get_err_code(self):
        return self.err_code

    def get_err_msg(self):
        return self.err_msg
