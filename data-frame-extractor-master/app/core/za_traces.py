from typing import Callable

from config import get_config
from fastapi import FastAPI
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

configs = get_config()


def setup_opentelemetry(app: FastAPI) -> None:  # pragma: no cover
    """
    本地开发不需要接入调用链
    Enables opentelemetry instrumentation.

    :param app: current application.
    """

    if configs["deploy_env"] not in ["test", "pre", "prd"]:
        return

    resource = Resource(
        attributes={
            SERVICE_NAME: configs["app_name"],
            "service.id": configs["app_id"],
            "env": configs["deploy_env"],
            "company.id": configs["company_id"],
        }
    )
    provider = TracerProvider(resource=resource)
    processor = BatchSpanProcessor(OTLPSpanExporter(endpoint=configs["otlp_exporter"]))
    provider.add_span_processor(processor)
    trace.set_tracer_provider(provider)

    excluded_endpoints = ["/metrics"]

    for path_name in [
        "health",
        "openapi",
        "swagger_ui_html",
        "swagger_ui_redirect",
        "redoc_html",
    ]:
        try:
            excluded_endpoints.append(app.url_path_for(path_name))
        except Exception as e:
            print(str(e))

    FastAPIInstrumentor.instrument_app(
        app,
        excluded_urls=",".join(excluded_endpoints),
    )


def stop_opentelemetry(app: FastAPI) -> None:  # pragma: no cover
    """
    Disables opentelemetry instrumentation.

    :param app: current application.
    """
    if configs["deploy_env"] not in ["test", "pre", "prd"]:
        return

    FastAPIInstrumentor.uninstrument_app(app)



def stop_app_handler(app: FastAPI) -> Callable:
    def stop_app() -> None:
        stop_opentelemetry(app)

    return stop_app
