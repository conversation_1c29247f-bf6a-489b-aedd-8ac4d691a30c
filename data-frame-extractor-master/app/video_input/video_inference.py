from pydantic import BaseModel, AnyHttpUrl
from typing import List, Optional
from app.core.utils import get_now_time_ms


class VideoData(BaseModel):
    id: str
    url: AnyHttpUrl
    frame_list: List[int]
    oss_dir: str


class VideoFrameExtractionInput(BaseModel):
    data: VideoData
    reqStartTime: int  # long
    respStartTime: int = get_now_time_ms()
    respEndTime: Optional[int] = None
    srvLogTraceId: str

