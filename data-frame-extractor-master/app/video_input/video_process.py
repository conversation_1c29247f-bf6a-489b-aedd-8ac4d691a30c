import httpx
import aiofiles
import requests

from app.exceptions.common_exceptions import CommonException


async def download_file_url(file_url, download_file_path):
    try:
        async with httpx.AsyncClient(verify=False) as client:
            async with client.stream(method="GET", url=file_url) as response:
                async with aiofiles.open(download_file_path, 'wb') as f:
                    async for chunk in response.aiter_bytes():
                        await f.write(chunk)
    except httpx.TransportError as e:
        raise CommonException(10000) from e
    except Exception as e:
        raise CommonException(10000) from e


def download_file_requests(file_url, download_file_path):
    res = requests.get(file_url, verify=False)
    with open(download_file_path, "wb") as f:
        f.write(res.content)

