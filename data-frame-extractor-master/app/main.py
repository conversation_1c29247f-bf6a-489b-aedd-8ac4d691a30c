import copy
import os
import tempfile
import time
from urllib.parse import unquote

from algorithm.video_frame_extraction import VideoFrameExtraction
from config import get_config
from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from app.core.za_traces import setup_opentelemetry, stop_app_handler
from app.exceptions.common_exceptions import CommonException
from app.exceptions.error_code import MAP_STATUS_CODE_TO_MSG
from app.video_input.video_inference import VideoFrameExtractionInput
from app.video_input.video_process import download_file_url, download_file_requests
from app.zalog import get_app_logger, get_biz_logger

deploy_env = os.getenv("DEPLOY_ENV")
configs = get_config()
biz_logger = get_biz_logger(configs)
app_logger = get_app_logger(configs)

frame_process_tool = None


def load_model():
    global frame_process_tool
    print("start loading algorithm...", flush=True)
    frame_process_tool = VideoFrameExtraction(oss_info=configs["oss_info"])
    print("algorithm is ready", flush=True)


def get_application() -> FastAPI:
    if deploy_env in ["pre", "prd"]:
        application = FastAPI(docs_url=None, redoc_url=None)
    else:
        application = FastAPI()
    setup_opentelemetry(application)
    application.add_event_handler("startup", load_model)
    application.add_event_handler("shutdown", stop_app_handler(application))
    return application


app = get_application()


@app.exception_handler(RequestValidationError)
def validation_exception_handler(request: Request, exc):
    app_logger.error(f"input validation exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        content={
            "success": False,
            "code": 10003,
            "message": MAP_STATUS_CODE_TO_MSG[10003],
            "detail": exc.errors(),
        }
    )


@app.exception_handler(CommonException)
def common_exception_handler(request: Request, exc):
    app_logger.error("predefined exception: {}".format(exc.get_err_msg()), exc_info=True)
    return JSONResponse(content={"success": False, "code": exc.get_err_code(), "message": exc.get_err_msg()})


@app.middleware("http")
def catch_exceptions_middleware(request: Request, call_next):
    # SEE: https://stackoverflow.com/a/62407111/8991693
    try:
        return call_next(request)
    except Exception as e:
        app_logger.error(f"Unknow exception: {str(e)}", exc_info=True)
        return JSONResponse(
            content={
                "success": False,
                "code": 50000,
                "message": MAP_STATUS_CODE_TO_MSG[50000],
                "detail": str(e),
            }
        )


@app.get("/health")
def health():
    return "ok"


@app.post("/frame_extraction")
def extract_video_frame(request: VideoFrameExtractionInput):
    t1 = time.perf_counter()
    # TODO: 支持的文件类型（后缀）？
    biz_logger.info(request.dict())  # 请求内容log
    with tempfile.TemporaryDirectory() as tmpdir:
        encoded_url = unquote(request.data.url)
        encoded_url = encoded_url[: encoded_url.find(".mp4") + len(".mp4")]
        tmp_name = os.path.basename(encoded_url)
        download_video_file_path = os.path.join(tmpdir, tmp_name)
        # download_file_url(request.data.url, download_video_file_path)
        download_file_requests(request.data.url, download_video_file_path)

        t2 = time.perf_counter()

        biz_logger.info({"stage": "download", "costTime": t2 - t1})

        input_dict = {
            "srvLogTraceId": request.srvLogTraceId,
            "data": {
                "id": request.data.id,
                "url": request.data.url,
                "frame_list": request.data.frame_list,
                "oss_dir": request.data.oss_dir,
                "file_path": download_video_file_path}
        }
        algo_result = frame_process_tool.process(input_dict)  # type: ignore


        # 返回内容log
        result_log_dict = copy.deepcopy(algo_result)
        biz_logger.info(result_log_dict)  # 返回内容log

        biz_logger.info({"stage": "all", "costTime": time.perf_counter() - t1})

        algo_result["success"] = True
        return algo_result

biz_logger.info({"service_name": configs["app_name"], "status": "start"})


if __name__ == "__main__":
    # debugging mode
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="debug")
