FROM base-registry.zhonganinfo.com/vision/data-frame-extrator:v0

RUN mkdir -p /root/app

WORKDIR /root/app/

ENV SERVICE_PORT=8080

# pip源 -i http://pypi.zhonganinfo.com/root/public --trusted-host pypi.zhonganinfo.com
# pip源 -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
# RUN python3 -m pip3 install --upgrade pip==20.3.1 && python3 -m pip install -r ./requirements --default-timeout=2000

COPY . /root/app/


CMD ["/bin/bash", "run.sh"]
