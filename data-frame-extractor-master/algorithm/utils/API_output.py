import numpy as np
import copy
from algorithm.utils.try_except import error_handler
from algorithm.utils import try_except


output_template = {
    "id": None,
    "frame_list": []
}

output_frame_template = {"frame_stamp": None,
                         "oss_flag": False,
                         "oss_path": None}

class ApiOutputTool:
    def __init__(self):
        pass

    @error_handler("ApiOutputTool", item_return=None)
    def process(self, case_struct_info):
        result_dict = copy.deepcopy(output_template)
        frame_list = case_struct_info.data.frame_list
        result_dict["id"] = case_struct_info.data.info.id

        for frame_obj in frame_list:
            new_frame_dict = copy.deepcopy(output_frame_template)
            new_frame_dict["frame_stamp"] = frame_obj.frame_stamp
            new_frame_dict["oss_flag"] = frame_obj.oss_flag
            new_frame_dict["oss_path"] = frame_obj.oss_url      # 20231122 要求改成能直接访问的url
            result_dict["frame_list"].append(new_frame_dict)

        # 错误码处理
        error_code = 0
        error_message = None
        error_detail = None
        if len(try_except.OCR_CASE_ERROR_CODE) > 0:
            code_list = try_except.OCR_CASE_ERROR_CODE
            error_code = try_except.MAP_ALGORITHM_CODE[code_list[0][0]][code_list[0][1]]
            error_detail = try_except.OCR_CASE_ERROR_DETAIL[0]
            error_message = try_except.OCR_CASE_ERROR_MSG[0]
            if error_code == 30100:
                error_code = 30000      # 该错误过于严重，导致输出无效，直接返回整体失败

        # 对服务层输出
        output = {"errcode": error_code,
                  "message": error_message,
                  "detail": error_detail,
                  "result": result_dict}

        return output
