from .data_type import FrameData, BaseCaseStruct
from algorithm.utils import try_except
import os

from config import get_config
from app.zalog import get_app_logger, get_biz_logger

configs = get_config()
app_logger = get_app_logger(configs)


class BaseCaseStructInfo(BaseCaseStruct):
    # 案件级结构体
    def __init__(self):
        super(BaseCaseStructInfo, self).__init__()
        pass

    # 提取完特征后更新数据流
    def update_img_frame_result(self, frame_result_list):
        frame_id_list = self.data.frame_idx_list
        oss_dir = self.data.info.oss_dir
        video_id = self.data.info.id

        for frame_id, image in zip(frame_id_list, frame_result_list):
            new_frame_res = FrameData()
            new_frame_res.frame_stamp = frame_id
            new_frame_res.image = image

            # 在这里把准备存的oss地址也编辑好
            base_name = "{}_{}.jpg".format(video_id, frame_id)
            save_path = os.path.join(oss_dir, base_name)
            new_frame_res.oss_path = save_path

            self.data.frame_list.append(new_frame_res)
        return True
