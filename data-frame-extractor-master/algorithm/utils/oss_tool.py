import re
import cv2
import numpy as np
import oss2
from algorithm.utils.try_except import error_handler


class OssTool:
    def __init__(self, oss_info):
        self.endpoint = oss_info["endpoint"]
        self.bucketName = oss_info["bucketName"]
        self.accessKeyId = oss_info["accessKeyId"]
        self.accessKeySecret = oss_info["accessKeySecret"]
        if oss_info["mode"] in ["prd", "pre", "test"]:
            # 需要解码
            from za_ec import za_dec

            self.accessKeySecret = za_dec(self.accessKeySecret.encode("utf-8")).decode("utf-8")

            self.remove_internal_flag = True     # 20231205 生成时效性链接会自带internal，去掉后外网可访问。
        else:
            self.remove_internal_flag = True

        self.auth = oss2.Auth(self.accessKeyId, self.accessKeySecret)
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucketName)

    def transfer_to_internal_signed_url(self, signed_url):
        regex_pattern = r"(https?:\/\/)(.*)"
        m = re.match(regex_pattern, signed_url)
        head = m.group(1)
        body_list = m.group(2).split("/", 1)
        domain_list = body_list[0].split(".")
        # if not domain_list[1].endswith("-internal"):
        #     domain_list[1] = domain_list[1] + "-internal"     # 20231204 需开放对外oss路径
        internal_domain = ".".join(domain_list)
        internal_url = head + "/".join([internal_domain, body_list[1]])
        return internal_url

    def get_signed_url(self, oss_path, expire_time=60 * 60 * 24 * 3650):
        return self.bucket.sign_url("GET", oss_path, expire_time)

    def get_internal_signed_url(self, oss_path, expire_time=60 * 60 * 24 * 360 * 10):
        # return self.transfer_to_internal_signed_url(self.get_signed_url(oss_path, expire_time))
        return self.get_signed_url(oss_path, expire_time)

    def save_to_oss(self, local_path, oss_path):
        with open(local_path, "rb") as f:
            self.bucket.put_object(oss_path, f)
        return oss_path

    @error_handler("OssTool", num_return=2)
    def process(self, image_np, oss_path):
        if image_np is None:
            return False, None  # 前置抽帧报错的跳过。

        # step1 图片转为jpg的byte形式
        image_code = cv2.imencode('.jpg', image_np)[1]
        jpg_bytes = np.array(image_code).tobytes()

        # step2 上传oss
        self.bucket.put_object(oss_path, jpg_bytes)

        # step3 获取oss下载链接
        internal_signed_url = self.get_internal_signed_url(oss_path)

        if self.remove_internal_flag:
            internal_signed_url = internal_signed_url.replace("-internal", "")  # 20231205 生成时效性链接会自带internal，去掉后外网可访问。

        raw_url = "http://{}.{}/{}".format(self.bucketName, self.endpoint.replace("http://", ""), oss_path)

        return True, internal_signed_url


if __name__ == "__main__":
    # 测试上传图片

    oss_info = {"endpoint": "http://oss-cn-hzjbp-b-internal.aliyuncs.com",
                "bucketName": "xdecision-dev",
                "accessKeyId": "LTAIcgcYhAfGfGqJ",
                "accessKeySecret": "cUXb4mcwFxZbzufj5HowfQvXlMrZB9",
                "mode": "dev"
                }

    oss_util = OssTool(oss_info)
    oss_path = "voiceprint/test_sj/" + "test.jpg"

    import cv2
    import numpy as np

    image = cv2.imread("/Users/<USER>/Downloads/17459660800293250_small.jpg")
    image_code = cv2.imencode('.jpg', image)[1]
    jpg_bytes = np.array(image_code).tobytes()
    oss_util.bucket.put_object(oss_path, jpg_bytes)
