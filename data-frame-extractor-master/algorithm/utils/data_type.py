

# 每一帧的数据结构
class FrameData(object):
    def __init__(self):
        # self.time_stamp = None
        self.frame_stamp = None
        self.image = None
        self.oss_path = None
        self.oss_flag = False


# 案件数据结构
class CaseInfo(object):
    def __init__(self):
        self.id = None
        self.url = None
        self.oss_dir = None


# 案件数据结构
class CaseData(object):
    def __init__(self):
        self.info = CaseInfo()
        self.data = None    # 存放视频或图片对象
        self.frame_idx_list = []
        self.frame_list = []


# 案件结构
class BaseCaseStruct(object):
    def __init__(self):
        self.srvLogTraceId = None
        self.data = CaseData()

