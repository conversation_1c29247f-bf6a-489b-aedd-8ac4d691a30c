from algorithm.utils.try_except import error_handler


class FrameTool:
    @error_handler("FrameTool", item_return=None)
    def get_frames(self, video_obj, frame_list):
        frame_count = 0
        frame_list.sort()
        output_list = [None for x in frame_list]    # 每一帧对应位置是None
        while True:
            # 逐帧读取
            _, frame = video_obj.read()
            if frame is None:
                break
            # 按照设置的频率保存图片
            if frame_count in frame_list:
                idx = frame_list.index(frame_count)
                output_list[idx] = frame
            frame_count += 1  # 读取视频帧数＋1

        # assert len(frame_list) == len(output_list), "抽帧数量不匹配"
        return output_list

    def process(self, video_obj, frame_list):
        output_list = self.get_frames(video_obj, frame_list)
        if output_list is None:     # 有报错，返回对应数量None
            output_list = [None for x in frame_list]
        return output_list
