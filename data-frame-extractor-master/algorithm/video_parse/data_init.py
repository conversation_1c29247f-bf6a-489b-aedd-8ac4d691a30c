from algorithm.utils.data_func import *
from algorithm.utils.try_except import error_handler
import cv2



class DataInitModule:
    # 将输入字典结构对应录入结构体
    def __init__(self):
        pass

    @error_handler("DataInitModule")
    def record_case_data(self, case_struct_info, input):
        srvLogTraceId = input["srvLogTraceId"]
        case_struct_info.srvLogTraceId = srvLogTraceId

        # Step1 录入案件信息
        case_info = input["data"]
        for key in ["id", "url", "oss_dir"]:
            setattr(case_struct_info.data.info, key, case_info[key])
        case_struct_info.data.frame_idx_list = input["data"]["frame_list"]

        # Step2 读取视频对象或图片对象
        file_path = input["data"].get("file_path")  # 本地调用有这个
        data_obj = input["data"].get("data_obj")   # 服务调用有这个


        if data_obj is None:        # 本地测试调用
            video_obj = cv2.VideoCapture(file_path)
            case_struct_info.data.data = video_obj
        else:       # 服务网络调用
            case_struct_info.data.data = data_obj

        return case_struct_info


    def go_service_entrance(self, input):
        case_struct_info = BaseCaseStructInfo()

        # Step1 录入报案信息
        new_case = self.record_case_data(case_struct_info, input)
        if new_case is not None:
            case_struct_info = new_case

        return case_struct_info
