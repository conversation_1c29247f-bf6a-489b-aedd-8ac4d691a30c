from .main_stream import MainStreamControl


class ModelInit(object):
    def __init__(self, model_config_path):
        pass

class VideoFrameExtraction(object):
    def __init__(self, model_config_path=None, oss_info=None):
        # 初始化模型
        self.model_manager = ModelInit(model_config_path)

        # 流程初始化
        self.MainStreamController = MainStreamControl(self.model_manager, oss_info)

    def process(self, input_data):
        res = self.MainStreamController.process(input_data)
        return res

