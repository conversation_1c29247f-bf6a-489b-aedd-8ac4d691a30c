from config import get_config
from .utils.API_output import ApiOutputTool
from .utils.oss_tool import OssTool
from .utils import try_except
from .video_parse.data_init import DataInitModule
from .video_parse.frame_parse import FrameTool

import time
configs = get_config()
from app.zalog import get_app_logger, get_biz_logger
biz_logger = get_biz_logger(configs)

class MainStreamControl(object):
    # 主要流程初始化
    def __init__(self, model_manager, oss_info):
        # 数据流初始化
        self.data_init_parse = DataInitModule()

        self.frame_tool = FrameTool()

        # oss上传工具
        self.oss_tool = OssTool(oss_info)

        # 组成输出形式的工具
        self.API_output_tool = ApiOutputTool()

        # logger初始化
        configs = get_config()
        self.app_logger = get_app_logger(configs)

    def process_init(self, input_dict):
        try_except.OCR_CASE_ERROR_CODE = []  # 错误码初始化
        try_except.OCR_CASE_ERROR_MSG = []
        try_except.OCR_CASE_ERROR_DETAIL = []
        try_except.OCR_CASE_CONTEXT_ID = input_dict["srvLogTraceId"]  # 记录本次请求的uuid
        self.app_logger.info("Start processing the request {}".format(input_dict["srvLogTraceId"]))  # log记录请求
        case_struct_info = self.data_init_parse.go_service_entrance(input_dict)
        return case_struct_info

    def get_frame_image(self, case_struct_info):
        video_obj = case_struct_info.data.data
        frame_list = case_struct_info.data.frame_idx_list
        image_list = self.frame_tool.process(video_obj, frame_list)
        case_struct_info.update_img_frame_result(image_list)
        return case_struct_info

    def upload_to_oss(self, case_struct_info):
        # 将文件上传到oss的流程。
        frame_res_list = case_struct_info.data.frame_list

        for frame_case in frame_res_list:
            image_np = frame_case.image
            oss_path = frame_case.oss_path
            oss_flag, oss_url = self.oss_tool.process(image_np, oss_path)
            if not oss_flag:
                frame_case.oss_path = None
            frame_case.oss_flag = oss_flag
            frame_case.oss_url = oss_url

        return case_struct_info

    def process(self, input_dict):
        # Step1 案件请求参数初始化，新建案子级别数据传输结构体
        case_struct_info = self.process_init(input_dict)

        t1 = time.perf_counter()

        # Step2 抽帧
        case_struct_info = self.get_frame_image(case_struct_info)

        t2 = time.perf_counter()
        biz_logger.info({"stage": "get_frame_image", "costTime": t2 - t1})

        # Step3 上传oss
        case_struct_info = self.upload_to_oss(case_struct_info)

        t3 = time.perf_counter()
        biz_logger.info({"stage": "upload_to_oss", "costTime": t3 - t2})

        # Step1 返回结果
        out_dict = self.API_output_tool.process(case_struct_info)

        return out_dict
