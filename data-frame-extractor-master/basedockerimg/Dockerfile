FROM python:3.9

# Setup Pypi
ARG PyPI_CN_HOST=pypi.tuna.tsinghua.edu.cn
ARG PyPI_CN_REPO=https://${PyPI_CN_HOST}/simple

RUN echo "[global]" > /etc/pip.conf \
    && echo "index-url = ${PyPI_CN_REPO}" >> /etc/pip.conf \
    && echo "[install]" >> /etc/pip.conf \
    && echo "trusted-host = ${PyPI_CN_HOST}" >> /etc/pip.conf \
    && python -m pip install -U pip \
    && python -m pip install wheel

RUN apt-get update && \ 
    apt-get install -y vim && \
    rm -rf /var/lib/apt/lists/*

ENV TZ=Asia/Shanghai

COPY requirements.txt /tmp/requirements.txt
RUN python3 -m pip install -r /tmp/requirements.txt && rm /tmp/requirements.txt
RUN python3 -m pip install za-ec -i http://pypi.zhonganinfo.com/root/public/+simple --trusted-host pypi.zhonganinfo.com