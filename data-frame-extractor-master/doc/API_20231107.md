# za-fraud-userimg-recog
# todo
## 视频抽帧服务 API

测试服务地址: 

### 请求接口参数

| 参数                                | 必选    | 类型           | 默认值 | 说明                                          |
|-----------------------------------|-------|--------------|-----|---------------------------------------------|
| srvLogTraceId                     | true  | string       | /   | 请求uuid。可以是视频id+时间戳                          |
| reqStartTime                      | true  | int          | /   | 请求发起时间戳（单位毫秒）                               |
| reserved_info                     | true  | dict         | /   | 给业务中台留的预留字段，会原封不动返回，需要为一个打平的字典，建议字符长度不要过长。  |
| data                              | true  | dict[object] | /   | 请求内容                                        |
| data.id                | true  | string       | /   | 视频唯一id号                                    |
| data.url               | true  | string       | /   | 视频的url                                      |
| data.frame_list        | true  | list[object] | /   | 所需要抽取的帧数                    |
| data.oss_dir           | true  | string       | /   | 指定上传oss的位置                    |

请求接口示例：

``` json
{   
    "srvLogTraceId": "请求UUID",
    "reqStartTime": 1675003131,
    "reserved_info": {"给业务中台留的预留字段": "内容"},
    "data": {
            "id": "video_id",
            "url": "http:/abc/123.mp4",
            "frame_list": [10,35,60],
            "oss_dir": "frames/20230101/a12345/",    
        }
}
```

## 返回结果参数

| 返回字段             | 字段类型 | 说明                                                         |
| -------------------- | -------- | ------------------------------------------------------------ |
| success              | bool     | 返回请求状态,True:成功, False:失败                           |
| code                 | int      | 状态错误码           |
| message              | string   | 错误信息                                                     |
| result               | dict[object]   | 相似度结果               |
| result.id            | string   |  视频唯一id号            |
| result.frame_list    | dict[object]   | 对每一帧的抽取结果   |
| result.frame_list.frame_stamp    | int   | 该帧的帧序号   |
| result.frame_list.oss_flag    | bool   | 该帧的oss上传结果是否成功   |
| result.frame_list.oss_path    | string   | 该帧的存储路径   |

返回结果示例：

``` json
{
    "success": true,
    "code": 0,
    "message": "",
    "result": {
        "id": "video_id",
        "frame_list":[{"frame_stamp": 10,
                    "oss_flag": true,
                    "oss_path": "frames/20230101/a12345/frame_10.jpg"},
                    {"frame_stamp": 35,
                    "oss_flag": true,
                    "oss_path": "frames/20230101/a12345/frame_35.jpg"},
                    {"frame_stamp": 60,
                    "oss_flag": false,
                    "oss_path": None},
        ]
        }
}
```
