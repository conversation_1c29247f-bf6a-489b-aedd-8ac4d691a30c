import os

import yaml

CONFIG = None
# cube中公司id
# 保险的company_id是10022
company_id = 10022


def get_config():
    base = os.path.abspath(os.path.dirname(__file__))
    global CONFIG
    if CONFIG:
        return CONFIG

    # 从config.yml中读取服务配置文件
    with open(
        os.path.join(base, "config.yml"),
        "r",
        encoding="utf-8",
    ) as yf:
        service_config = yaml.load(yf.read(), Loader=yaml.Loader)

    CONFIG = {
        # app_name和申请的git项目保持一致
        "app_name": "data-frame-extrator",
        # 调用链中需要,公司内部项目需要
        "company_id": company_id,
        # 日志目录
        "default_log_dir": os.path.join(base, "logs"),
        "deploy_env": os.getenv("DEPLOY_ENV"),
        "app_id": os.getenv("APP_ID"),
    }
    CONFIG.update(service_config)
    return CONFIG
