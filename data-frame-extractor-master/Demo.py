from algorithm.video_frame_extraction import VideoFrameExtraction
from config import get_config
import time, requests, json
time_now = time.strftime('%Y-%m-%d_%H-%M-%S', time.localtime(time.time()))

def 算法层调用():
    configs = get_config()

    tool = VideoFrameExtraction(oss_info=configs["oss_info"])

    input = {
        "srvLogTraceId": "请求UUID",
        "reqStartTime": 1675003131,
        "reserved_info": {"给业务中台留的预留字段": "内容"},
        "data": {
                "id": "video_id",
                "url": "http:/abc/123.mp4",
                "frame_list": [10,35,60],
                "oss_dir": "frames/20230101/test_sj/",
                "file_path": "/Users/<USER>/workspace/数据/短视频202301/05b26250-b64b-4962-b874-264e6d4446ad.mp4"
            }
    }

    input = {"srvLogTraceId": "68bbd134-468c-4d13-8942-b19a9a1aefbe",
             "reqStartTime": 1700115139203,
             "reserved_info": "24236ss竖屏-20220225090416.mp4",
             "data": {"id": "2423",
                      "url": "http://oss-cn-hzfinance.aliyuncs.com/test-open/89f50e12-3a65-4965-a362-9a493c460f61.mp4",
                      "frame_list": [286, 240, 168],

                      "file_path": "/Users/<USER>/Downloads/89f50e12-3a65-4965-a362-9a493c460f61.mp4",
                      "oss_dir": "frames/20230101/test_sj/"}}


    res = tool.process(input)

    print(res)

def 服务层调用():
    # url = "http://0.0.0.0:8080/frame_extraction"      # mac
    url = "http://data-frame-extrator.test.za-tech.net/frame_extraction"      # mac

    data = {
        "srvLogTraceId": "测试视频_{}".format(time_now),
        "reqStartTime": time.time(),
        "reserved_info": {"给业务中台留的预留字段": "内容"},
        "data": {
            "id": "测试视频_{}".format(time_now),
            # "url": "http://xdecision-dev.oss-cn-hzjbp-b-internal.aliyuncs.com/frames/20230101/test_sj/4d2da2ba-e4f7-4d14-a0bc-c7579c480bd7.mp4",
            "url": "https://test-open.oss-cn-hzfinance.aliyuncs.com/x-magnet/16-9002-avi-20220225090039.avi",
            "frame_list": [10, 35, 60],
            "oss_dir": "frames/20230101/test_sj/"}
    }

    res = requests.post(url=url, data=json.dumps(data))

    result = json.loads(res.text)
    print(json.loads(res.text))

    a = {"srvLogTraceId": "813091fd-398a-422d-9a8b-17095af43360",
         "reqStartTime": 1701162765214,
         "reserved_info": "29198张国立测试.mp4",
         "data": {"id": "29198",
                  "url": "https://test-open.oss-cn-hzfinance.aliyuncs.com/x-magnet/张国立测试1116-20231116045459.mp4",
                  "frame_list": [
                                 1525, 1600, 1666, 168, 240, 286, 1, None],
                  "oss_dir": "audit/hit/frames/1121"}}

    print(1)

if __name__ == '__main__':
    # 算法层调用()
    服务层调用()
