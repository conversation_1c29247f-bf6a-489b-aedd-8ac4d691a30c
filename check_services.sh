#!/bin/bash

# 视频分析服务状态检查脚本
# 作者: Augment Agent
# 用途: 检查ASR服务和视频分析服务状态

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_DIR="/Users/<USER>/Documents/work/augment-projects/material-review"
LOG_DIR="$PROJECT_DIR/logs"

echo -e "${BLUE}📊 视频分析服务状态检查${NC}"
echo -e "${BLUE}================================${NC}"

# 检查服务健康状态
check_service_health() {
    local url=$1
    local service_name=$2
    
    echo -e "${YELLOW}检查 $service_name...${NC}"
    
    # 检查HTTP响应
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null)
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ $service_name: 健康 (HTTP 200)${NC}"
        
        # 获取详细信息
        if [[ "$url" == *"/health"* ]]; then
            local health_info=$(curl -s "$url" 2>/dev/null)
            if [ ! -z "$health_info" ]; then
                echo -e "${BLUE}   响应: $health_info${NC}"
            fi
        fi
        return 0
    else
        echo -e "${RED}❌ $service_name: 不健康 (HTTP $response)${NC}"
        return 1
    fi
}

# 检查端口占用
check_port_usage() {
    local port=$1
    local service_name=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        local pid=$(lsof -ti:$port)
        local process_info=$(ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null)
        echo -e "${GREEN}✅ 端口 $port ($service_name): 已占用${NC}"
        echo -e "${BLUE}   进程信息: $process_info${NC}"
        return 0
    else
        echo -e "${RED}❌ 端口 $port ($service_name): 未占用${NC}"
        return 1
    fi
}

# 检查日志文件
check_logs() {
    local log_file=$1
    local service_name=$2
    
    echo -e "${YELLOW}检查 $service_name 日志...${NC}"
    
    if [ -f "$log_file" ]; then
        local log_size=$(du -h "$log_file" | cut -f1)
        local last_modified=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$log_file" 2>/dev/null)
        echo -e "${GREEN}✅ 日志文件存在: $log_file${NC}"
        echo -e "${BLUE}   大小: $log_size, 最后修改: $last_modified${NC}"
        
        # 显示最后几行日志
        echo -e "${BLUE}   最后5行日志:${NC}"
        tail -5 "$log_file" | sed 's/^/     /'
    else
        echo -e "${RED}❌ 日志文件不存在: $log_file${NC}"
    fi
}

# 检查PID文件
check_pid_files() {
    echo -e "\n${YELLOW}检查PID文件...${NC}"
    
    # ASR服务PID
    local asr_pid_file="$LOG_DIR/asr.pid"
    if [ -f "$asr_pid_file" ]; then
        local asr_pid=$(cat "$asr_pid_file")
        if ps -p $asr_pid > /dev/null 2>&1; then
            echo -e "${GREEN}✅ ASR服务PID: $asr_pid (运行中)${NC}"
        else
            echo -e "${RED}❌ ASR服务PID: $asr_pid (进程不存在)${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  ASR服务PID文件不存在${NC}"
    fi
    
    # 视频分析服务PID
    local video_pid_file="$LOG_DIR/video.pid"
    if [ -f "$video_pid_file" ]; then
        local video_pid=$(cat "$video_pid_file")
        if ps -p $video_pid > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 视频分析服务PID: $video_pid (运行中)${NC}"
        else
            echo -e "${RED}❌ 视频分析服务PID: $video_pid (进程不存在)${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  视频分析服务PID文件不存在${NC}"
    fi
}

# 性能检查
check_performance() {
    echo -e "\n${YELLOW}性能检查...${NC}"
    
    # 内存使用
    local python_memory=$(ps aux | grep -E "(app.server|real_video_server)" | grep -v grep | awk '{sum+=$6} END {print sum/1024}')
    if [ ! -z "$python_memory" ]; then
        echo -e "${BLUE}Python服务内存使用: ${python_memory} MB${NC}"
    fi
    
    # CPU使用
    local python_cpu=$(ps aux | grep -E "(app.server|real_video_server)" | grep -v grep | awk '{sum+=$3} END {print sum}')
    if [ ! -z "$python_cpu" ]; then
        echo -e "${BLUE}Python服务CPU使用: ${python_cpu}%${NC}"
    fi
    
    # 磁盘空间
    local disk_usage=$(df -h "$PROJECT_DIR" | tail -1 | awk '{print $5}')
    echo -e "${BLUE}项目目录磁盘使用: $disk_usage${NC}"
}

# 主函数
main() {
    echo -e "${YELLOW}开始检查服务状态...${NC}\n"
    
    # 检查端口占用
    echo -e "${BLUE}🔌 端口检查${NC}"
    echo -e "${BLUE}================================${NC}"
    check_port_usage 8080 "ASR服务"
    check_port_usage 8081 "视频分析服务"
    
    echo ""
    
    # 检查服务健康状态
    echo -e "${BLUE}🏥 健康检查${NC}"
    echo -e "${BLUE}================================${NC}"
    check_service_health "http://localhost:8080/health" "ASR服务"
    check_service_health "http://localhost:8081/health" "视频分析服务"
    
    # 检查PID文件
    check_pid_files
    
    echo ""
    
    # 检查日志文件
    echo -e "${BLUE}📋 日志检查${NC}"
    echo -e "${BLUE}================================${NC}"
    check_logs "$LOG_DIR/asr_service.log" "ASR服务"
    echo ""
    check_logs "$LOG_DIR/video_service.log" "视频分析服务"
    
    # 性能检查
    check_performance
    
    echo -e "\n${BLUE}🌐 访问地址${NC}"
    echo -e "${BLUE}================================${NC}"
    echo -e "Demo页面: ${GREEN}http://localhost:8081/demo${NC}"
    echo -e "API文档:  ${GREEN}http://localhost:8081/docs${NC}"
    echo -e "健康检查: ${GREEN}http://localhost:8081/health${NC}"
    echo -e "ASR服务:  ${GREEN}http://localhost:8080/health${NC}"
    
    echo -e "\n${BLUE}🔧 管理命令${NC}"
    echo -e "${BLUE}================================${NC}"
    echo -e "启动服务: ${GREEN}./start_services.sh${NC}"
    echo -e "停止服务: ${GREEN}./stop_services.sh${NC}"
    echo -e "查看日志: ${GREEN}tail -f $LOG_DIR/asr_service.log${NC}"
    echo -e "查看日志: ${GREEN}tail -f $LOG_DIR/video_service.log${NC}"
}

# 运行主函数
main
