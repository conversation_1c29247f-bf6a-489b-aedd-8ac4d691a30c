#!/usr/bin/env python3
"""
本地隧道代理 - 通过您的本地机器作为中转
"""

import requests
import json
import time
from flask import Flask, request, jsonify
from threading import Thread

app = Flask(__name__)

# 远程服务器配置
REMOTE_SERVER = "http://localhost:9999"  # 服务器本地地址
API_KEY = "5c08c2ed4caf27079ab6bf9b208aaf9d355f63ce51e1481caced856642758a40"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

@app.route('/health')
def health():
    return {"status": "tunnel_healthy", "timestamp": time.time()}

@app.route('/execute', methods=['POST'])
def execute_command():
    """转发命令执行请求"""
    try:
        data = request.get_json()
        response = requests.post(
            f"{REMOTE_SERVER}/execute",
            headers=headers,
            json=data,
            timeout=30
        )
        return response.json()
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.route('/file', methods=['POST'])
def file_operation():
    """转发文件操作请求"""
    try:
        data = request.get_json()
        response = requests.post(
            f"{REMOTE_SERVER}/file",
            headers=headers,
            json=data
        )
        return response.json()
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.route('/list/<path:path>')
def list_directory(path):
    """转发目录列表请求"""
    try:
        response = requests.get(
            f"{REMOTE_SERVER}/list/{path}",
            headers=headers
        )
        return response.json()
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.route('/service', methods=['POST'])
def service_control():
    """转发服务控制请求"""
    try:
        data = request.get_json()
        response = requests.post(
            f"{REMOTE_SERVER}/service",
            headers=headers,
            json=data
        )
        return response.json()
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.route('/logs/<service>')
def get_logs(service):
    """转发日志获取请求"""
    try:
        lines = request.args.get('lines', 50)
        response = requests.get(
            f"{REMOTE_SERVER}/logs/{service}",
            headers=headers,
            params={"lines": lines}
        )
        return response.json()
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("🌉 启动本地隧道代理...")
    print("请确保您已经通过SSH隧道连接到服务器")
    print("SSH命令示例: ssh -L 9999:localhost:9999 admin@*************")
    print("隧道地址: http://localhost:8888")
    
    app.run(host='0.0.0.0', port=8888, debug=False)
