import os

# import yaml

CONFIG = None
app_name = "za-video-asr"

# cube中公司id
company_id = 10022

oltp_exporter_map= {
    "dev": "http://172.28.34.35:4317",
    "test": "http://otel-collector-test.za-tech.net:80",
    "pre": "http://otel-collector-pre.za-tech.net:80",
    "prd": "http://otel-collector-pub.za-tech.net:80"
}


def get_config():
    base = os.path.abspath(os.path.dirname(__file__))
    global CONFIG
    if CONFIG:
        return CONFIG

    # 从config.yml中读取服务配置文件
    # with open(os.path.join(base, "config.yml"), "r", encoding="utf-8") as yf:
    #     service_config = yaml.load(yf.read(), Loader=yaml.Loader)

    CONFIG = {
        "app_name": app_name,
        "app_id": os.getenv("APP_ID"),
        "company_id": company_id,
        "default_log_dir": os.path.join(base, "logs"),
        "otlp_exporter": oltp_exporter_map.get(os.getenv("DEPLOY_ENV", "dev"))
    }
    # CONFIG.update(service_config)
    return CONFIG
