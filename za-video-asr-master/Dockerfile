FROM base-registry.zhonganinfo.com/vision/ubuntu:20.04-cuda11.8-base-torch2.1.0cu118
# Setup Pypi
ARG PyPI_CN_HOST=pypi.tuna.tsinghua.edu.cn
ARG PyPI_CN_REPO=https://${PyPI_CN_HOST}/simple

# 安装常用工具和依赖项
RUN apt-get update && apt-get install -y build-essential
RUN apt-get update && apt-get install -y build-essential python3-dev

RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# 设中文
RUN apt-get update && apt-get install -y locales
RUN locale-gen zh_CN.UTF-8
ENV LANG=zh_CN.UTF-8
ENV LANGUAGE=zh_CN:zh

# 安装pip包
RUN python3 -m pip install funasr==1.0.25
RUN python3 -m pip install modelscope==1.13.3

RUN python3 -m pip install fastapi==0.104.0
RUN python3 -m pip install pydantic==1.10.13
RUN python3 -m pip install uvicorn==0.23.2
RUN python3 -m pip install PyYAML==6.0
RUN python3 -m pip install httpx==0.25.0

RUN mkdir -p /root/app/
COPY . /root/app/
WORKDIR /root/app/


RUN mkdir -p /root/.cache/modelscope/hub
RUN wget "https://dsl-os.oss-cn-hzfinance.aliyuncs.com/video_understanding/iic.zip" -O /root/.cache/modelscope/hub/iic.zip && unzip /root/.cache/modelscope/hub/iic.zip -d /root/.cache/modelscope/hub
RUN rm -rf /root/.cache/modelscope/hub/iic.zip


CMD ["bash", "run.sh"]
