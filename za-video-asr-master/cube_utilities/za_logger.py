import copy
import json
import logging.config
import os
import re
import socket
import string
import time
from datetime import datetime
from typing import Any, Callable, Dict
import uuid

from cube_utilities.logging import (
    LOGGING_CONFIG,
    app_logger_name,
    biz_logger_name,
)

app_logger = None
biz_logger = None


def check_app_name(app_name):
    if not re.match("^[a-zA-Z-]+$", app_name):
        return False
    return True


class ZALoggingConfig:
    def __init__(self) -> None:
        self.log_config_initiliazed = False
        self.log_configured = False
        self.log_config = LOGGING_CONFIG
        self.log_dir_template = "/alidata1/admin/{}/logs"
        self.info_log_filename_template = string.Template(
            "${hostname}_app_${app_name}_${rand}_lt_info.log"
        )
        self.error_log_filename_template = string.Template(
            "${hostname}_app_${app_name}_${rand}_lt_error.log"
        )
        self.biz_log_filename_template = string.Template(
            "${hostname}-ss_micro_app_${app_name}_${rand}_lt_biz.log"
        )

    def _init_log_config(self, app_name, default_log_dir):
        if self.log_config_initiliazed:
            return
        print("start config service log")
        assert len(app_name) > 0, "app_name cannot be empty"
        assert check_app_name(app_name), "app_name cannot contain special characters"
        hostname = socket.gethostname()
        # 开发环境使用本地目录，线上环境使用六翼约定的目录

        if os.getenv("DEPLOY_ENV") not in [
            "test",
            "pre",
            "prd",
        ]:
            log_dir_path = default_log_dir
        else:
            log_dir_path = self.log_dir_template.format(app_name)
        if not os.path.exists(log_dir_path):
            os.makedirs(log_dir_path)

        change_conf = {
            "app_name": app_name,
            "hostname": hostname,
            "rand": str(uuid.uuid4())[:6]
        }
        info_log_path = os.path.join(
            log_dir_path,
            self.info_log_filename_template.substitute(change_conf),
        )
        error_log_path = os.path.join(
            log_dir_path,
            self.error_log_filename_template.substitute(change_conf),
        )
        biz_log_path = os.path.join(
            log_dir_path,
            self.biz_log_filename_template.substitute(change_conf),
        )
        print(info_log_path)
        print(error_log_path)
        print(biz_log_path)
        self.log_config["handlers"]["app_info_log_file"]["filename"] = info_log_path
        self.log_config["handlers"]["app_error_log_file"]["filename"] = error_log_path
        self.log_config["handlers"]["biz_info_log_file"]["filename"] = biz_log_path
        self.log_config_initiliazed = True

    def get_log_config_after_init(self, app_name, default_log_dir):
        if self.log_config_initiliazed:
            return self.log_config
        self._init_log_config(app_name, default_log_dir)
        return self.log_config

    def configure_logger(self, app_name, default_log_dir):
        if self.log_configured:
            return
        self._init_log_config(app_name, default_log_dir)
        logging.config.dictConfig(self.log_config)
        self.log_configured = True


def check_input(func: Callable[["ZABizLogger", str], None]):
    def wrapper(self, content: Dict[str, Any]) -> None:
        assert isinstance(content, dict), "biz log content must be dict"
        assert "ip" not in content
        assert "appName" not in content
        assert "hostName" not in content
        assert "agentType" not in content
        assert "source" not in content
        new_content = copy.deepcopy(content)
        new_content["time"] = ZABizLogger._get_now_time_str()
        msg = json.dumps(new_content, ensure_ascii=False)
        func(self, msg)

    return wrapper


class ZABizLogger(logging.Logger):
    def __init__(self):
        self.logger = logging.getLogger(biz_logger_name)

    @check_input
    def debug(self, msg):
        self.logger.debug(msg)

    @check_input
    def info(self, msg):
        self.logger.info(msg)

    @check_input
    def warn(self, msg):
        self.logger.warn(msg)

    @check_input
    def error(self, msg):
        self.logger.error(msg)

    @check_input
    def critical(self, msg):
        self.logger.critical(msg)

    @staticmethod
    def _get_now_time_str():
        s = datetime.now().isoformat()[:-3]
        symbol = "+"
        offset = -time.timezone / 3600
        if offset < 0:
            symbol = "-"
        offset = abs(offset)
        offset_str = "{:02n}:00".format(offset)
        return "{}{}{}".format(s, symbol, offset_str)


za_logging_config = ZALoggingConfig()


# app log 用来记录 info, warining, error日志
def get_app_logger(project_conf):
    global app_logger
    if app_logger:
        return app_logger
    za_logging_config.configure_logger(
        app_name=project_conf.get("app_name"),
        default_log_dir=project_conf.get("default_log_dir"),
    )
    app_logger = logging.getLogger(app_logger_name)
    return app_logger


# biz log只用来记录info级别的业务日志
def get_biz_logger(project_conf):
    global biz_logger
    if biz_logger:
        return biz_logger
    za_logging_config.configure_logger(
        app_name=project_conf.get("app_name"),
        default_log_dir=project_conf.get("default_log_dir"),
    )
    biz_logger = ZABizLogger()
    return biz_logger
