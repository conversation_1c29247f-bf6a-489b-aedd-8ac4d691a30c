import logging
import logging.config
from typing import Any, Dict

app_logger_name = "APP_LOGGER"
biz_logger_name = "BIZ_LOGGER"

default_file_name = "./log.txt"
default_file_handler_class = "logging.handlers.TimedRotatingFileHandler"



class CustomInfoFilter(logging.Filter):
    def filter(self, record: logging.LogRecord):
        # print(record.levelno)
        return record.levelno < logging.WARN


class CustomErrorFilter(logging.Filter):
    def filter(self, record: logging.LogRecord):
        # print(record.levelno)
        return record.levelno > logging.INFO


LOGGING_CONFIG: Dict[str, Any] = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "app_log_format": {
            # 时间格式 yyyy-MM-dd HH:mm:ss,SSS
            "format": "%(asctime)s [%(thread)d] %(levelname)s [%(module)s] [%(filename)s:%(lineno)s] [trace=,span=] - %(message)s",
        },
        "biz_log_format": {
            # 实际输出内容格式为json 包含time字段 格式为 "2017-03-19T12:41:29.590+08:00"
            "format": "%(message)s",
        },
    },
    "filters": {
        "info_filter": {
            "()": CustomInfoFilter,
        },
        "error_filter": {
            "()": CustomErrorFilter,
        }
    },
    "handlers": {
        "app_log_console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "app_log_format",
        },
        "biz_log_console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "biz_log_format",
        },
        "app_info_log_file": {
            "class": default_file_handler_class,
            # 需要修改
            "filename": default_file_name,
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "level": "DEBUG",
            "filters": ["info_filter"],
            "formatter": "app_log_format",
        },
        "app_error_log_file": {
            "class": default_file_handler_class,
            # 需要修改
            "filename": default_file_name,
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "level": "WARNING",
            "filters": ["error_filter"],
            "formatter": "app_log_format",
        },
        "biz_info_log_file": {
            "class": default_file_handler_class,
            # 需要修改
            "filename": default_file_name,
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "level": "INFO",
            "filters": ["info_filter"],
            "formatter": "biz_log_format",
        },
    },
    "loggers": {
        app_logger_name: {
            "handlers": [
                "app_log_console",
                "app_info_log_file",
                "app_error_log_file",
            ],
            "level": "DEBUG",
            "propagate": False,
        },
        biz_logger_name: {
            "handlers": [
                "biz_log_console",
                "biz_info_log_file",
            ],
            "level": "INFO",
            "propagate": False,
        },
        "uvicorn": {
            "handlers": [
                "app_log_console",
                "app_info_log_file",
                "app_error_log_file",
            ],
            "level": "INFO",
            "propagate": False,
        },
        "uvicorn.access": {
            "handlers": [
                "app_log_console",
                "app_info_log_file",
                "app_error_log_file",
            ],
            "level": "INFO",
            "propagate": False,
        },
        "uvicorn.access": {"handlers": []},
        "uvicorn.error": {
            "level": "INFO",
        },
    },
}
