# 日志目录
logs/

.vscode/
.venv
za-fraud-userimg-recog.code-workspace

### Python template
.idea/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

app/recognition/downloads/
downloads/
human2/*
result_lama/*
tests_210/*
*.pkl
*.bin
test_images/*
test_latitudes/*
models_0525.zip
models.zip
oss_v2.py
lama_test/*
tests/app/recognition/results
results*
*.jpg
*.png
cut_gps.py
nacos-data/
models.zip.back
*.wav
