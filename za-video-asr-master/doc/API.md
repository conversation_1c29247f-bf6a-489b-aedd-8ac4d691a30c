# Vitmatte-segmentation API
用于宠物AIGC项目的精细分割需要，运行于阿里云GPU环境。
当前版本默认只请求一张图片，不涉及batch推理。

 版本号 | 最后更改时间 | 撰写人 | 说明 
:----: | :----------: | :----: | :--- 
  v1.0  |   20231205   |  谭国珠  |     

### 请求接口参数

| 参数       | 必选  | 类型    | 默认值 | 说明                |
|:---------:|:-----:|:------:|:-----:|:-------------------|
| id        | true  | string | /     | 图片唯一id号         |
| base64    | true  | string | /     | 图片文件的base64编码  |
| base64    | true  | string | /     | 图片文件的base64编码  |

请求接口示例：
##### url: "http://aliyun_ip:8081/inference"
##### data:
``` json
{   
    "id": "image_id",
    "base64_ori_img": "base64_str", 
    "base64_trimap": "base64_str"
}
```
##### 请求方式: res = requests.post(url=url, data=json.dumps(data))

## 返回结果参数

| 返回字段             | 字段类型 | 说明                                                         |
| -------------------- | -------- | ------------------------------------------------------------ |
| success              | bool     | 返回请求状态,True:成功, False:失败                           |
| errcode                 | int      | 状态错误码           |
| message              | string   | 错误信息                                                     |
| detail              | string   | 详细错误信息                                                     |
| result               | dict[object]   | 返回结果               |
| result.id            | string   |  图片唯一id号            |
| result.data          | dict[object]    |  vitmatte分割模型的结果           |
| result.data.alpha_mask          | string   |  分割图像结果的base64编码          |

返回结果示例：

``` json
{
    "success": true,
    "errcode": 0,
    "message": "",
    "detail": "",
    "result": {
        "id": "image_id",
        "data":{"alpha_mask": "iVBORw0KGgoAAAANSUhEUgAAAyAAAAMgCAAAAAD..."
               }
```