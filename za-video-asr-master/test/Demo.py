from PIL import Image
import requests
import numpy as np
import json
import base64

def encode_image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read())
        return encoded_string.decode('utf-8')


def server_test():
    url = "http://10.139.183.68:2021/inference_funasr"  # mac

    wavbase64 = encode_image_to_base64("/Users/<USER>/Desktop/project/视频打标/code/0417/za-video-asr/funasr_algorithm/test/ee2bfb6b-dc8a-4fb5-ad11-b93ad296e558.wav")
    data = {
        "audio_id": "image_id",
        "audio_url": "https://dsl-os.oss-cn-hzfinance.aliyuncs.com/video_understanding/ee2bfb6b-dc8a-4fb5-ad11-b93ad296e558.wav",
        "audio_base64": ""

    }

    res = requests.post(url=url, data=json.dumps(data))
    result = json.loads(res.text)
    print(result)


if __name__ == '__main__':
    server_test()
