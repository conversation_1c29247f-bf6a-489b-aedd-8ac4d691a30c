from typing import Optional

MAP_STATUS_CODE_TO_MSG = {
    400: "Request validation failed",
    500: "Unknown",
    10000: "Prepare image failed",
}


class CommonException(Exception):
    """
    err_code 错误代码
    err_msg 错误信息
    """

    def __init__(
        self,
        err_code: int,
        err_msg: Optional[str],
    ) -> None:
        super().__init__()
        self.err_code = err_code
        self.err_msg = err_msg if err_msg else MAP_STATUS_CODE_TO_MSG.get(err_code)

    def get_err_code(self):
        return self.err_code

    def get_err_msg(self):
        return self.err_msg
