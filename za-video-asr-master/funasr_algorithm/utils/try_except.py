import functools
import traceback

from config import get_config
from cube_utilities.za_logger import get_app_logger

# 全局变量记录错误码
OCR_CASE_ERROR_CODE = []
OCR_CASE_CONTEXT_ID = None
OCR_CASE_ERROR_MSG = []
OCR_CASE_ERROR_DETAIL = []

# 映射错误码
MAP_ALGORITHM_CODE = {
    "YoloSegmentation": {"process_init": 30101, "ImageDepthPredict": 30102},
    "ApiMapping": {"trans_depth_res": 30201},
}

# 创建logger
configs = get_config()
app_logger = get_app_logger(configs)


# 返回1个输出的异常处理模块
def error_handler(mudule_name, num_return=1, item_return=None):
    def error_handler(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # return func(*args, **kwargs)
            try:
                return func(*args, **kwargs)
            except Exception:
                error_msg = (
                    "Error occured in : {} mudule {} function. request id: {}".format(
                        mudule_name, func.__name__, OCR_CASE_CONTEXT_ID
                    )
                )
                app_logger.info(error_msg, exc_info=True)
                err_detail = traceback.format_exc()  # 具体报错
                OCR_CASE_ERROR_CODE.append(
                    (mudule_name, func.__name__, OCR_CASE_CONTEXT_ID)
                )
                OCR_CASE_ERROR_MSG.append(error_msg)
                OCR_CASE_ERROR_DETAIL.append(err_detail)
                return [item_return] * num_return if num_return > 1 else item_return

        return wrapper

    return error_handler
