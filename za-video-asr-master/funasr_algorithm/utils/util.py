import cv2
import base64
import numpy as np


def base64encode(image_np):
    # cv2 np格式图片转base64
    image_code = cv2.imencode('.png', image_np)[1]
    base64_data = base64.b64encode(image_code)
    return base64_data


def base64decode(base64_data, flags=cv2.IMREAD_COLOR):
    # base64格式图片转cv2 np
    imgdata = base64.b64decode(base64_data)
    image = np.fromstring(imgdata, np.uint8)
    img_np = cv2.imdecode(image, flags)
    return img_np


def encode_image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read())
        return encoded_string.decode('utf-8')
