import copy
from funasr_algorithm.utils.util import base64encode, base64decode
from funasr_algorithm.utils import try_except
from funasr_algorithm.utils.try_except import error_handler
import base64
import cv2
import numpy as np

class ApiMapping:
    def __init__(self):
        self.depth_result_template = {"depth_image": None}
    
    @error_handler("trans_depth_res")
    def trans_depth_res(self, result):

        image_data = cv2.imencode('.png', result)[1].tobytes() 
        image = base64.b64encode(image_data).decode('utf-8')
        return image

    def process(self, result, input_dict):
        # step1 将yolo输出格式转为可被json.loads的类型
        image_id = input_dict.get("id")
        if result is None:
            self.depth_result_template["depth_image"] = None         # 避免前置报错
        else:
            self.depth_result_template["depth_image"] = self.trans_depth_res(result)

        # step2 错误码处理
        error_code = 0
        error_message = None
        error_detail = None
        if len(try_except.OCR_CASE_ERROR_CODE) > 0:
            code_list = try_except.OCR_CASE_ERROR_CODE
            error_code = try_except.MAP_ALGORITHM_CODE[code_list[0][0]][code_list[0][1]]
            error_detail = try_except.OCR_CASE_ERROR_DETAIL[0]
            error_message = try_except.OCR_CASE_ERROR_MSG[0]
            if error_code == 30100:
                error_code = 30000      # 该错误过于严重，导致输出无效，直接返回整体失败

        # 对服务层输出
        output = {"errcode": error_code,
                  "message": error_message,
                  "detail": error_detail,
                  "result": {"id": image_id,
                             "data": self.depth_result_template}}
        return output