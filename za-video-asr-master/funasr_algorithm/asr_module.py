import base64
from funasr import AutoModel


class ASRPredict(object):
    def __init__(self):
        # 先尝试完整模型，如果失败则使用基础模型
        try:
            # 使用完整的FunASR模型配置，包含标点符号和说话人识别
            self.funasr_model = AutoModel(
                model="paraformer-zh",  # 语音识别模型
                vad_model="fsmn-vad",   # 语音活动检测
                punc_model="ct-punc",   # 标点符号模型
                disable_update=True     # 禁用自动更新检查
            )
            print("✅ 完整FunASR模型加载成功（包含标点符号模型）")
            self.has_punctuation_model = True
        except Exception as e:
            print(f"⚠️  完整模型加载失败，使用基础模型: {e}")
            # 回退到基础模型
            self.funasr_model = AutoModel(
                model="paraformer-zh",
                disable_update=True
            )
            print("✅ 基础FunASR模型加载成功")
            self.has_punctuation_model = False

    def predict(self, input_dict, hot_words="众安,尊享e生,百万医疗"):

        input_url = input_dict.get("audio_url", None)
        input_base64 = input_dict.get("audio_base64", None)

        if input_url:
            input_data = input_url
        elif input_base64:
            decoded_data = base64.b64decode(input_base64)
            with open('output.wav', 'wb') as wav_file:
                wav_file.write(decoded_data)
            input_data = 'output.wav'
        else:
            return None

        # 根据模型能力调整参数
        generate_params = {
            "input": input_data,
            "batch_size_s": 300,
            "hotword": hot_words,
            "disable_pbar": True
        }

        # 如果有完整模型，启用高级功能
        if self.has_punctuation_model:
            generate_params.update({
                "sentence_timestamp": True,  # 启用句子时间戳
                "word_timestamp": True,      # 启用词级时间戳
            })

        res = self.funasr_model.generate(**generate_params)

        return res

if __name__ == "__main__":
    net = ASRPredict()
    res = net.predict(input="https://dsl-os.oss-cn-hzfinance.aliyuncs.com/video_understanding/ee2bfb6b-dc8a-4fb5-ad11-b93ad296e558.wav")
