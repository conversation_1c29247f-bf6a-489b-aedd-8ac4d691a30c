from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.schemas.input import (
    InputAsr,
)
from config import get_config
from cube_utilities.za_logger import get_app_logger, get_biz_logger, za_logging_config
from exception.common_exception import MAP_STATUS_CODE_TO_MSG, CommonException

configs = get_config()


za_logging_config.configure_logger(
    app_name=configs["app_name"],
    default_log_dir=configs["default_log_dir"],
)

app_logger = get_app_logger(configs)
biz_logger = get_biz_logger(configs)

model_loaded = False

origins = [
    "http://localhost",
    "http://localhost:8080",
]

app = FastAPI(docs_url=None, redoc_url=None)
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
def load_model():
    # 算法模块后加载
    from funasr_algorithm.asr_module import ASRPredict

    global model_loaded
    global asr_net

    print("start loading depth_estimate model...", flush=True)
    asr_net = ASRPredict()
    print("load depth_estimate model success!")

    model_loaded = True
    print("all models are ready", flush=True)


# TODO: 统一返回结构, inference接口


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc):
    app_logger.error(
        f"input validation exception: {str(exc)}",
        exc_info=True,
    )
    return JSONResponse(
        content={
            "success": False,
            "code": 400,
            "message": MAP_STATUS_CODE_TO_MSG[400],
            "detail": exc.errors(),
        }
    )


@app.exception_handler(CommonException)
async def common_exception_handler(request: Request, exc):
    app_logger.error(
        "common exception: {}".format(exc.get_err_msg()),
        exc_info=True,
    )
    return JSONResponse(
        content={
            "success": False,
            "code": exc.get_err_code(),
            "message": exc.get_err_msg(),
        }
    )


@app.middleware("http")
async def catch_exceptions_middleware(request: Request, call_next):
    # SEE: https://stackoverflow.com/a/62407111/8991693
    try:
        return await call_next(request)
    except Exception as e:
        app_logger.error(
            f"Unknow exception: {str(e)}",
            exc_info=True,
        )
        return JSONResponse(
            content={
                "success": False,
                "code": 500,
                "message": MAP_STATUS_CODE_TO_MSG[500],
            }
        )


@app.get("/health")
async def health():
    if model_loaded:
        return "ok"
    else:
        return JSONResponse(
            content={
                "success": False,
                "message": "not ready",
            },
            status_code=503,
        )


@app.post("/inference_funasr")
async def asr_fun(input:InputAsr):
    input_dict = input.dict()
    biz_logger.info({"audio_id": input.audio_id, "process": "start"})        # 不显示base64字段，log太长
    # input_dict["image_data"] = await transform_url_to_numpy(input.url)
    algo_result = asr_net.predict(input_dict)  # type: ignore
    biz_logger.info({"audio_id": input.audio_id, "process": "end"})

    return algo_result


if __name__ == "__main__":
    # debugging mode
    import uvicorn

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="debug",
        log_config=za_logging_config.get_log_config_after_init(
            app_name=configs["app_name"],
            default_log_dir=configs["default_log_dir"],
        ),
    )
