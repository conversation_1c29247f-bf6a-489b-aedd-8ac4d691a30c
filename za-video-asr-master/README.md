# 项目初始化及发布工作

文档分成3个部分，主要介绍脚手架代码生成后建议的环境初始化工作，提交commit前校验的步骤，以及发布到公司发布系统中的准备步骤


## 环境初始化

新项目建议的python版本是python 3.9(2023年), 具体参考python官方的发布周期: https://devguide.python.org/versions/

### 操作步骤

1. 本地开发建议每一个项目在**项目根目录**下面创建一个独立的虚拟环境，环境名为.venv

```shell
$ python3 -m venv ./.venv
```

如果你用了其他名字作为虚拟环境的名字，记得把你的虚拟环境的名字放入.gitignore，因为虚拟环境不需要上传到git中

2. 接着安装服务需要的依赖

```shell
# 激活环境
$ source ./.venv/bin/activate

# 安装服务依赖
$ python3 -m pip install -r requirements.txt

# 以下非必要步骤
# 如果你还初始化了代码风格校验等其他的代码
$ python3 -m pip install -r requirements_dev.txt
$ pre-commit install
```

接下来，你可以开发你的模型代码，或者把模型代码放入algorithm目录，同时修改app/server.py中对应的加载模型的代码

### 关于添加依赖的说明

1. 如果你明确知道要安装的依赖的版本，那么把明确的依赖和版本填入requirements.txt，然后重新执行安装服务依赖
2. 如果你不是很清楚是否有额外的依赖
   1. 执行bash run.sh, 查看是否有依赖报错
   2. 如果有依赖报错，先在环境中执行python3 -m pip install {package}
   3. 然后查看安装完成后输出的日志，把安装好的具体版本写入requirements.txt

**注意**：一个python package还会依赖其他的package，你只需要把你确定要安装的package及版本写入requirements.txt即可，其他这个package依赖的依赖不需要写入requirements.txt, 交给pip自己去解析

### cpu版本pytorch依赖的说明

因为目前线上没有GPU，所以如果有pytorch依赖的话，需要安装pytorch的cpu版本，实践下来，需要放在Dockerfile里安装，不要把pytorch依赖写入requirements.txt

比如要安装cpu版本的torch 1.13.1和torchaudio 0.13.1，在Dockerfile里加入下面一行

```dockerfile
# ====新增=====
RUN python3 -m pip install torch==1.13.1+cpu torchaudio==0.13.1+cpu --index-url https://download.pytorch.org/whl/cpu
# ====新增=====

COPY requirements.txt /tmp/requirements.txt
RUN python3 -m pip install -r /tmp/requirements.txt && rm /tmp/requirements.txt
```



## 提交commit前校验

提交commit前最重要的就是检查你的.gitignore文件是否有漏掉要排除的文件，比较常见的一些需要放在.gitignore中的文件：

- python运行的缓存文件夹`__pycache__`
- 可能的python虚拟环境目录.venv
- vscode相关的配置文件: `.vscode, *.code-workspace`
- pycharm相关的: .idea
- 项目中特定的一些文件，比如本项目中，logs，downloads文件夹只保存本地文件即可
- mac隐藏文件.DS_Store
- 本地测试数据集



## 部署说明

本项目单个分支为一个独立的服务，独立部署在生产GPU机器上

服务基础镜像为: base-registry.zhonganinfo.com/vision/pets-aigc-image-parse:v0


启动命令

```shell

mkdir -p /alidata1/admin/pets-aigc-image-parse/logs

#内存的设定值需要看实际宿主机内存的大小
#挂时区为了和系统时间保持一致
docker run -d -m 31g --gpus all -v /alidata1/admin/pets-aigc-image-parse/logs:/root/app/logs -v /etc/localtime:/etc/localtime:ro -v /etc/timezone:/etc/timezone:ro -v /usr/share/zoneinfo/Asia:/usr/share/zoneinfo/Asia:ro -p 17310:8080 base-registry.zhonganinfo.com/vision/pets-aigc-image-parse:v0
```