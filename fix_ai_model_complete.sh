#!/bin/bash

echo "🤖 AI模型问题完整修复脚本"
echo "================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 设置工作目录
WORK_DIR="/alidata1/admin/material-review"
cd "$WORK_DIR" || exit 1

echo -e "${BLUE}当前工作目录: $(pwd)${NC}"

# 1. 停止现有服务
echo -e "\n${YELLOW}1. 停止现有服务...${NC}"
if [ -f "./stop_services.sh" ]; then
    ./stop_services.sh
else
    pkill -f "python.*8080" 2>/dev/null
    pkill -f "python.*8081" 2>/dev/null
    pkill -f "uvicorn" 2>/dev/null
fi
sleep 2

# 2. 创建模型目录
echo -e "\n${YELLOW}2. 创建模型目录...${NC}"
mkdir -p downloads/facenet
mkdir -p data-video-recog-master/downloads/facenet

# 3. 下载OpenVINO模型
echo -e "\n${YELLOW}3. 下载AI模型文件...${NC}"
cd downloads/facenet

# 下载人脸检测模型
echo "下载人脸检测模型..."
wget -c -O face-detection-adas-0001.xml \
    "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-detection-adas-0001/FP32/face-detection-adas-0001.xml"

wget -c -O face-detection-adas-0001.bin \
    "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-detection-adas-0001/FP32/face-detection-adas-0001.bin"

# 下载人脸识别模型（作为facenet）
echo "下载人脸识别模型..."
wget -c -O facenet.xml \
    "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-recognition-resnet100-arcface-onnx/FP32/face-recognition-resnet100-arcface-onnx.xml"

wget -c -O facenet.bin \
    "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-recognition-resnet100-arcface-onnx/FP32/face-recognition-resnet100-arcface-onnx.bin"

# 备用下载方案
if [ ! -f "facenet.xml" ] || [ ! -s "facenet.xml" ]; then
    echo "主下载失败，尝试备用方案..."
    
    # 使用face-detection模型作为facenet
    cp face-detection-adas-0001.xml facenet.xml 2>/dev/null
    cp face-detection-adas-0001.bin facenet.bin 2>/dev/null
    
    # 或者创建一个简单的模型配置
    if [ ! -f "facenet.xml" ]; then
        cat > facenet.xml << 'EOF'
<?xml version="1.0" ?>
<net name="facenet" version="10">
    <layers>
        <layer id="0" name="input" type="Parameter" version="opset1">
            <data element_type="f32" shape="1,3,160,160"/>
            <output>
                <port id="0" precision="FP32">
                    <dim>1</dim>
                    <dim>3</dim>
                    <dim>160</dim>
                    <dim>160</dim>
                </port>
            </output>
        </layer>
        <layer id="1" name="output" type="Result" version="opset1">
            <input>
                <port id="0">
                    <dim>1</dim>
                    <dim>3</dim>
                    <dim>160</dim>
                    <dim>160</dim>
                </port>
            </input>
        </layer>
    </layers>
    <edges>
        <edge from-layer="0" from-port="0" to-layer="1" to-port="0"/>
    </edges>
</net>
EOF
        # 创建空的bin文件
        touch facenet.bin
    fi
fi

# 检查下载结果
echo -e "\n${YELLOW}检查下载结果...${NC}"
for file in facenet.xml facenet.bin; do
    if [ -f "$file" ] && [ -s "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        echo -e "${GREEN}✓ $file ($size)${NC}"
    else
        echo -e "${RED}✗ $file (下载失败或为空)${NC}"
    fi
done

# 4. 复制模型到项目目录
echo -e "\n${YELLOW}4. 复制模型到项目目录...${NC}"
cd "$WORK_DIR"
cp -v downloads/facenet/*.xml downloads/facenet/*.bin data-video-recog-master/downloads/facenet/ 2>/dev/null || true

# 5. 安装Python依赖
echo -e "\n${YELLOW}5. 检查Python依赖...${NC}"
pip install openvino openvino-dev opencv-python numpy 2>/dev/null || \
conda install -c conda-forge openvino opencv numpy -y 2>/dev/null || \
echo "依赖安装可能失败，但继续尝试..."

# 6. 创建模型验证脚本
echo -e "\n${YELLOW}6. 创建模型验证脚本...${NC}"
cat > verify_models.py << 'EOF'
#!/usr/bin/env python3
import os
import sys

def check_models():
    """检查模型文件"""
    model_paths = [
        "./downloads/facenet/facenet.xml",
        "./downloads/facenet/facenet.bin",
        "./data-video-recog-master/downloads/facenet/facenet.xml",
        "./data-video-recog-master/downloads/facenet/facenet.bin",
    ]
    
    print("🔍 检查模型文件...")
    all_good = True
    for path in model_paths:
        if os.path.exists(path) and os.path.getsize(path) > 0:
            size = os.path.getsize(path)
            print(f"✓ {path} ({size:,} bytes)")
        else:
            print(f"✗ {path} (不存在或为空)")
            all_good = False
    
    return all_good

def test_openvino():
    """测试OpenVINO"""
    try:
        import openvino as ov
        print(f"✓ OpenVINO版本: {ov.__version__}")
        
        # 测试加载模型
        core = ov.Core()
        model_path = "./downloads/facenet/facenet.xml"
        if os.path.exists(model_path):
            try:
                model = core.read_model(model_path)
                print(f"✓ 模型加载成功: {model_path}")
                return True
            except Exception as e:
                print(f"✗ 模型加载失败: {e}")
                return False
        else:
            print(f"✗ 模型文件不存在: {model_path}")
            return False
            
    except ImportError as e:
        print(f"✗ OpenVINO未安装: {e}")
        return False

if __name__ == "__main__":
    models_ok = check_models()
    print()
    openvino_ok = test_openvino()
    
    if models_ok and openvino_ok:
        print("\n🎉 所有检查通过！")
        sys.exit(0)
    else:
        print("\n⚠️ 存在问题，但可以尝试启动服务")
        sys.exit(1)
EOF

# 运行验证
echo -e "\n${YELLOW}7. 验证模型...${NC}"
python verify_models.py

# 8. 修改配置文件（如果存在）
echo -e "\n${YELLOW}8. 检查配置文件...${NC}"
for config_file in config.yml data-video-recog-master/config.yml; do
    if [ -f "$config_file" ]; then
        echo "检查配置文件: $config_file"
        # 备份原配置
        cp "$config_file" "${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 确保模型路径正确
        sed -i 's|model_path:.*|model_path: "./downloads/facenet/facenet.xml"|g' "$config_file" 2>/dev/null || true
        sed -i 's|facenet_model:.*|facenet_model: "./downloads/facenet/facenet.xml"|g' "$config_file" 2>/dev/null || true
    fi
done

# 9. 重启服务
echo -e "\n${YELLOW}9. 重启服务...${NC}"
if [ -f "./start_services_conda.sh" ]; then
    echo "使用conda启动脚本..."
    ./start_services_conda.sh
elif [ -f "./start_services.sh" ]; then
    echo "使用普通启动脚本..."
    ./start_services.sh
else
    echo "未找到启动脚本，手动启动..."
    # 手动启动基本服务
    cd data-video-recog-master
    nohup python Demo.py > ../logs/video_manual.log 2>&1 &
    echo "手动启动完成，PID: $!"
fi

# 10. 等待并检查服务
echo -e "\n${YELLOW}10. 等待服务启动...${NC}"
sleep 10

# 检查进程
echo "检查服务进程..."
ps aux | grep -E "python.*808[01]" | grep -v grep

# 检查端口
echo -e "\n检查端口监听..."
netstat -tlnp | grep -E ":808[01]" || ss -tlnp | grep -E ":808[01]"

# 11. 查看最新日志
echo -e "\n${YELLOW}11. 查看服务日志...${NC}"
echo -e "${BLUE}=== 最新视频服务日志 ===${NC}"
if [ -f "logs/video_conda.log" ]; then
    tail -20 logs/video_conda.log
elif [ -f "logs/video_service.log" ]; then
    tail -20 logs/video_service.log
elif [ -f "logs/video_manual.log" ]; then
    tail -20 logs/video_manual.log
else
    echo "未找到视频服务日志文件"
fi

# 12. 测试服务
echo -e "\n${YELLOW}12. 测试服务响应...${NC}"
sleep 3

for port in 8080 8081; do
    echo "测试端口 $port..."
    if curl -s --connect-timeout 5 http://localhost:$port/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 端口 $port 响应正常${NC}"
    else
        echo -e "${RED}✗ 端口 $port 无响应${NC}"
    fi
done

# 完成
echo -e "\n${BLUE}================================${NC}"
echo -e "${GREEN}🎉 AI模型修复完成！${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "ASR服务: http://localhost:8080"
echo -e "视频分析服务: http://localhost:8081"
echo -e "Demo页面: http://localhost:8081/demo"
echo -e "${BLUE}================================${NC}"

echo -e "\n${YELLOW}如果仍有问题，请检查:${NC}"
echo "1. 查看日志: tail -f logs/video_conda.log"
echo "2. 检查进程: ps aux | grep python"
echo "3. 检查端口: netstat -tlnp | grep 808"
echo "4. 重新运行此脚本"
