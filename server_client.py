#!/usr/bin/env python3
"""
连接到远程服务器的客户端
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class RemoteServerClient:
    def __init__(self, server_url: str, api_key: str):
        self.server_url = server_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def execute_command(self, command: str, cwd: Optional[str] = None, timeout: int = 30) -> Dict[str, Any]:
        """执行远程命令"""
        try:
            response = requests.post(
                f"{self.server_url}/execute",
                headers=self.headers,
                json={
                    "command": command,
                    "cwd": cwd,
                    "timeout": timeout
                },
                timeout=timeout + 5
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def read_file(self, path: str) -> Dict[str, Any]:
        """读取远程文件"""
        try:
            response = requests.post(
                f"{self.server_url}/file",
                headers=self.headers,
                json={
                    "path": path,
                    "mode": "read"
                }
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def write_file(self, path: str, content: str) -> Dict[str, Any]:
        """写入远程文件"""
        try:
            response = requests.post(
                f"{self.server_url}/file",
                headers=self.headers,
                json={
                    "path": path,
                    "content": content,
                    "mode": "write"
                }
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def list_directory(self, path: str) -> Dict[str, Any]:
        """列出远程目录"""
        try:
            clean_path = path.lstrip('/')
            response = requests.get(
                f"{self.server_url}/list/{clean_path}",
                headers=self.headers
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def service_control(self, action: str, service: str = "all") -> Dict[str, Any]:
        """控制远程服务"""
        try:
            response = requests.post(
                f"{self.server_url}/service",
                headers=self.headers,
                json={
                    "action": action,
                    "service": service
                }
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_logs(self, service: str, lines: int = 50) -> Dict[str, Any]:
        """获取远程日志"""
        try:
            response = requests.get(
                f"{self.server_url}/logs/{service}",
                headers=self.headers,
                params={"lines": lines}
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}

# 连接到您的服务器
SERVER_URL = "http://8.149.232.131:9999"
API_KEY = "5c08c2ed4caf27079ab6bf9b208aaf9d355f63ce51e1481caced856642758a40"

client = RemoteServerClient(SERVER_URL, API_KEY)

print("🔍 开始诊断服务器问题...")

# 1. 检查当前服务状态
print("\n1. 检查服务状态...")
status = client.service_control("status")
if status.get("success"):
    print("✓ 服务状态检查成功")
    print("进程信息:", status.get("processes", ""))
    print("端口信息:", status.get("ports", ""))
else:
    print("✗ 服务状态检查失败:", status.get("error"))

# 2. 检查模型文件
print("\n2. 检查模型文件...")
model_paths = [
    "/alidata1/admin/material-review/downloads/facenet/facenet.xml",
    "/alidata1/admin/material-review/data-video-recog-master/downloads/facenet/facenet.xml"
]

for path in model_paths:
    result = client.execute_command(f"ls -la {path}")
    if result.get("success") and result.get("returncode") == 0:
        print(f"✓ 找到模型文件: {path}")
        print(result.get("stdout", ""))
    else:
        print(f"✗ 模型文件不存在: {path}")

# 3. 检查downloads目录结构
print("\n3. 检查downloads目录...")
result = client.execute_command("find /alidata1/admin/material-review -name downloads -type d")
if result.get("success"):
    print("Downloads目录:", result.get("stdout", ""))

# 4. 查看最新的视频服务日志
print("\n4. 查看视频服务日志...")
logs = client.get_logs("video", 30)
if logs.get("success"):
    print("最新日志:")
    print(logs.get("content", ""))
else:
    print("✗ 无法获取日志:", logs.get("error"))

print("\n🔧 诊断完成，准备修复...")
