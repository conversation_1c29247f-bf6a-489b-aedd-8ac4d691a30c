#!/bin/bash

echo "🔍 AI模型问题诊断和修复脚本"
echo "================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取当前目录
CURRENT_DIR=$(pwd)
echo -e "${BLUE}当前工作目录: $CURRENT_DIR${NC}"

# 1. 检查模型文件路径
echo -e "\n${YELLOW}1. 检查模型文件状态...${NC}"

MODEL_PATHS=(
    "./downloads/facenet/facenet.xml"
    "./data-video-recog-master/downloads/facenet/facenet.xml"
    "/alidata1/admin/material-review/downloads/facenet/facenet.xml"
    "/alidata1/admin/material-review/data-video-recog-master/downloads/facenet/facenet.xml"
)

for path in "${MODEL_PATHS[@]}"; do
    if [ -f "$path" ]; then
        echo -e "${GREEN}✓ 找到模型文件: $path${NC}"
        ls -la "$path"
        file "$path"
    else
        echo -e "${RED}✗ 模型文件不存在: $path${NC}"
    fi
done

# 2. 检查downloads目录结构
echo -e "\n${YELLOW}2. 检查downloads目录结构...${NC}"
DOWNLOAD_DIRS=(
    "./downloads"
    "./data-video-recog-master/downloads"
    "/alidata1/admin/material-review/downloads"
    "/alidata1/admin/material-review/data-video-recog-master/downloads"
)

for dir in "${DOWNLOAD_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo -e "${GREEN}✓ 找到downloads目录: $dir${NC}"
        find "$dir" -name "*.xml" -o -name "*.bin" -o -name "*.onnx" | head -10
    else
        echo -e "${RED}✗ downloads目录不存在: $dir${NC}"
    fi
done

# 3. 检查Python环境和依赖
echo -e "\n${YELLOW}3. 检查Python环境...${NC}"
python --version
pip list | grep -E "(openvino|opencv|torch|tensorflow)"

# 4. 检查配置文件
echo -e "\n${YELLOW}4. 检查配置文件...${NC}"
CONFIG_FILES=(
    "./config.yml"
    "./data-video-recog-master/config.yml"
    "./config.py"
    "./data-video-recog-master/config.py"
)

for config in "${CONFIG_FILES[@]}"; do
    if [ -f "$config" ]; then
        echo -e "${GREEN}✓ 找到配置文件: $config${NC}"
        echo "--- 配置内容 ---"
        cat "$config" | head -20
        echo "--- 配置结束 ---"
    fi
done

# 5. 检查服务进程
echo -e "\n${YELLOW}5. 检查服务进程...${NC}"
ps aux | grep -E "(python|uvicorn)" | grep -v grep

# 6. 检查端口占用
echo -e "\n${YELLOW}6. 检查端口占用...${NC}"
netstat -tlnp | grep -E ":808[01]"

# 7. 提供修复建议
echo -e "\n${BLUE}7. 修复建议:${NC}"
echo "================================"

# 检查是否需要下载模型
if ! find . -name "facenet.xml" 2>/dev/null | grep -q .; then
    echo -e "${YELLOW}建议1: 下载缺失的模型文件${NC}"
    cat << 'EOF'
# 创建模型下载脚本
mkdir -p downloads/facenet
cd downloads/facenet

# 下载FaceNet模型 (示例URL，需要根据实际情况调整)
wget https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-recognition-resnet100-arcface-onnx/FP32/face-recognition-resnet100-arcface-onnx.xml -O facenet.xml
wget https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-recognition-resnet100-arcface-onnx/FP32/face-recognition-resnet100-arcface-onnx.bin -O facenet.bin

EOF
fi

echo -e "${YELLOW}建议2: 检查OpenVINO安装${NC}"
echo "pip install openvino openvino-dev"

echo -e "${YELLOW}建议3: 修改配置文件中的模型路径${NC}"
echo "确保配置文件中的模型路径指向正确的位置"

echo -e "\n${GREEN}诊断完成！请根据上述信息进行修复。${NC}"
