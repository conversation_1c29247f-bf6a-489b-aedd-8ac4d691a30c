#!/bin/bash

# 视频分析服务一键启动脚本
# 作者: Augment Agent
# 用途: 启动ASR服务和视频分析服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_DIR="/Users/<USER>/Documents/work/augment-projects/material-review"
ASR_DIR="$PROJECT_DIR/za-video-asr-master"
VIDEO_DIR="$PROJECT_DIR/data-video-recog-master"

# 日志文件
LOG_DIR="$PROJECT_DIR/logs"
ASR_LOG="$LOG_DIR/asr_service.log"
VIDEO_LOG="$LOG_DIR/video_service.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

echo -e "${BLUE}🚀 视频分析服务启动脚本${NC}"
echo -e "${BLUE}================================${NC}"

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 未找到，请先安装Python3${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Python3 环境检查通过${NC}"
}

# 检查项目目录
check_directories() {
    if [ ! -d "$ASR_DIR" ]; then
        echo -e "${RED}❌ ASR项目目录不存在: $ASR_DIR${NC}"
        exit 1
    fi
    
    if [ ! -d "$VIDEO_DIR" ]; then
        echo -e "${RED}❌ 视频分析项目目录不存在: $VIDEO_DIR${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 项目目录检查通过${NC}"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo -e "${YELLOW}⚠️  端口 $port 已被占用，正在尝试停止现有服务...${NC}"
        # 杀死占用端口的进程
        lsof -ti:$port | xargs kill -9 2>/dev/null
        sleep 2
        
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
            echo -e "${RED}❌ 无法释放端口 $port，请手动检查${NC}"
            exit 1
        fi
    fi
    echo -e "${GREEN}✅ 端口 $port ($service_name) 可用${NC}"
}

# 启动ASR服务
start_asr_service() {
    echo -e "${YELLOW}🎤 启动ASR服务 (端口8080)...${NC}"
    
    cd "$ASR_DIR"
    nohup python3 -m app.server > "$ASR_LOG" 2>&1 &
    ASR_PID=$!
    
    echo "$ASR_PID" > "$LOG_DIR/asr.pid"
    echo -e "${BLUE}   ASR服务PID: $ASR_PID${NC}"
    
    # 等待服务启动
    echo -e "${YELLOW}   等待ASR服务启动...${NC}"
    for i in {1..30}; do
        if curl -s http://localhost:8080/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ ASR服务启动成功 (http://localhost:8080)${NC}"
            return 0
        fi
        sleep 2
        echo -n "."
    done
    
    echo -e "${RED}❌ ASR服务启动失败，请检查日志: $ASR_LOG${NC}"
    return 1
}

# 启动视频分析服务
start_video_service() {
    echo -e "${YELLOW}🎬 启动视频分析服务 (端口8081)...${NC}"
    
    cd "$VIDEO_DIR"
    nohup python3 server/real_video_server.py > "$VIDEO_LOG" 2>&1 &
    VIDEO_PID=$!
    
    echo "$VIDEO_PID" > "$LOG_DIR/video.pid"
    echo -e "${BLUE}   视频分析服务PID: $VIDEO_PID${NC}"
    
    # 等待服务启动
    echo -e "${YELLOW}   等待视频分析服务启动...${NC}"
    for i in {1..15}; do
        if curl -s http://localhost:8081/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 视频分析服务启动成功 (http://localhost:8081)${NC}"
            return 0
        fi
        sleep 2
        echo -n "."
    done
    
    echo -e "${RED}❌ 视频分析服务启动失败，请检查日志: $VIDEO_LOG${NC}"
    return 1
}

# 显示服务状态
show_status() {
    echo -e "\n${BLUE}📊 服务状态${NC}"
    echo -e "${BLUE}================================${NC}"
    
    # ASR服务状态
    if curl -s http://localhost:8080/health >/dev/null 2>&1; then
        echo -e "${GREEN}🎤 ASR服务: 运行中 (http://localhost:8080)${NC}"
    else
        echo -e "${RED}🎤 ASR服务: 未运行${NC}"
    fi
    
    # 视频分析服务状态
    if curl -s http://localhost:8081/health >/dev/null 2>&1; then
        echo -e "${GREEN}🎬 视频分析服务: 运行中 (http://localhost:8081)${NC}"
        echo -e "${GREEN}   📝 Demo页面: http://localhost:8081/demo${NC}"
        echo -e "${GREEN}   📚 API文档: http://localhost:8081/docs${NC}"
    else
        echo -e "${RED}🎬 视频分析服务: 未运行${NC}"
    fi
    
    echo -e "\n${BLUE}📋 日志文件${NC}"
    echo -e "${BLUE}================================${NC}"
    echo -e "ASR服务日志: $ASR_LOG"
    echo -e "视频服务日志: $VIDEO_LOG"
    
    echo -e "\n${BLUE}🔧 管理命令${NC}"
    echo -e "${BLUE}================================${NC}"
    echo -e "停止服务: ./stop_services.sh"
    echo -e "查看日志: tail -f $ASR_LOG"
    echo -e "查看日志: tail -f $VIDEO_LOG"
}

# 主函数
main() {
    echo -e "${YELLOW}开始启动服务...${NC}\n"
    
    # 检查环境
    check_python
    check_directories
    
    # 检查端口
    check_port 8080 "ASR服务"
    check_port 8081 "视频分析服务"
    
    echo ""
    
    # 启动服务
    if start_asr_service; then
        sleep 3
        if start_video_service; then
            echo -e "\n${GREEN}🎉 所有服务启动成功！${NC}"
            show_status
        else
            echo -e "\n${RED}❌ 视频分析服务启动失败${NC}"
            exit 1
        fi
    else
        echo -e "\n${RED}❌ ASR服务启动失败${NC}"
        exit 1
    fi
}

# 运行主函数
main
