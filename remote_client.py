#!/usr/bin/env python3
"""
远程客户端 - AI助手用来操作远程服务器的客户端
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class RemoteServerClient:
    def __init__(self, server_url: str, api_key: str):
        self.server_url = server_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def execute_command(self, command: str, cwd: Optional[str] = None, timeout: int = 30) -> Dict[str, Any]:
        """执行远程命令"""
        try:
            response = requests.post(
                f"{self.server_url}/execute",
                headers=self.headers,
                json={
                    "command": command,
                    "cwd": cwd,
                    "timeout": timeout
                },
                timeout=timeout + 5
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def read_file(self, path: str) -> Dict[str, Any]:
        """读取远程文件"""
        try:
            response = requests.post(
                f"{self.server_url}/file",
                headers=self.headers,
                json={
                    "path": path,
                    "mode": "read"
                }
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def write_file(self, path: str, content: str) -> Dict[str, Any]:
        """写入远程文件"""
        try:
            response = requests.post(
                f"{self.server_url}/file",
                headers=self.headers,
                json={
                    "path": path,
                    "content": content,
                    "mode": "write"
                }
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def list_directory(self, path: str) -> Dict[str, Any]:
        """列出远程目录"""
        try:
            # 移除开头的斜杠，因为API路径会自动添加
            clean_path = path.lstrip('/')
            response = requests.get(
                f"{self.server_url}/list/{clean_path}",
                headers=self.headers
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def service_control(self, action: str, service: str = "all") -> Dict[str, Any]:
        """控制远程服务"""
        try:
            response = requests.post(
                f"{self.server_url}/service",
                headers=self.headers,
                json={
                    "action": action,
                    "service": service
                }
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_logs(self, service: str, lines: int = 50) -> Dict[str, Any]:
        """获取远程日志"""
        try:
            response = requests.get(
                f"{self.server_url}/logs/{service}",
                headers=self.headers,
                params={"lines": lines}
            )
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = requests.get(f"{self.server_url}/health")
            return response.json()
        except Exception as e:
            return {"success": False, "error": str(e)}

# 使用示例
if __name__ == "__main__":
    # 配置
    SERVER_URL = "http://your-server-ip:9999"  # 替换为实际服务器地址
    API_KEY = "remote-agent-2024-secure-key"   # 替换为实际API密钥
    
    client = RemoteServerClient(SERVER_URL, API_KEY)
    
    # 测试连接
    print("测试连接...")
    health = client.health_check()
    print(f"健康检查: {health}")
    
    # 执行命令示例
    print("\n执行命令...")
    result = client.execute_command("pwd")
    print(f"当前目录: {result}")
    
    # 列出目录
    print("\n列出目录...")
    files = client.list_directory("alidata1/admin/material-review")
    print(f"文件列表: {files}")
    
    # 检查服务状态
    print("\n检查服务状态...")
    status = client.service_control("status")
    print(f"服务状态: {status}")
