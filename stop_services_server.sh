#!/bin/bash

# 停止视频分析服务脚本 - 服务器版

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目路径
PROJECT_DIR="/alidata1/admin/material-review"
LOG_DIR="$PROJECT_DIR/logs"

echo -e "${BLUE}🛑 停止视频分析服务${NC}"
echo -e "${BLUE}================================${NC}"

# 停止ASR服务
echo -e "${YELLOW}停止ASR服务 (端口8080)...${NC}"
if [ -f "$LOG_DIR/asr.pid" ]; then
    ASR_PID=$(cat "$LOG_DIR/asr.pid")
    if kill -0 "$ASR_PID" 2>/dev/null; then
        kill "$ASR_PID"
        echo -e "${GREEN}✅ ASR服务已停止 (PID: $ASR_PID)${NC}"
    else
        echo -e "${YELLOW}⚠️  ASR服务进程不存在${NC}"
    fi
    rm -f "$LOG_DIR/asr.pid"
else
    echo -e "${YELLOW}⚠️  ASR PID文件不存在${NC}"
fi

# 停止视频分析服务
echo -e "${YELLOW}停止视频分析服务 (端口8081)...${NC}"
if [ -f "$LOG_DIR/video.pid" ]; then
    VIDEO_PID=$(cat "$LOG_DIR/video.pid")
    if kill -0 "$VIDEO_PID" 2>/dev/null; then
        kill "$VIDEO_PID"
        echo -e "${GREEN}✅ 视频分析服务已停止 (PID: $VIDEO_PID)${NC}"
    else
        echo -e "${YELLOW}⚠️  视频分析服务进程不存在${NC}"
    fi
    rm -f "$LOG_DIR/video.pid"
else
    echo -e "${YELLOW}⚠️  视频分析 PID文件不存在${NC}"
fi

# 强制停止相关进程
echo -e "${YELLOW}强制停止相关进程...${NC}"
pkill -f "python.*8080" 2>/dev/null && echo -e "${GREEN}✅ 强制停止8080端口进程${NC}"
pkill -f "python.*8081" 2>/dev/null && echo -e "${GREEN}✅ 强制停止8081端口进程${NC}"
pkill -f "real_video_server" 2>/dev/null && echo -e "${GREEN}✅ 强制停止视频服务进程${NC}"

# 等待进程完全停止
sleep 2

# 检查端口状态
echo -e "\n${YELLOW}检查端口状态...${NC}"
if netstat -tlnp | grep ":8080 " >/dev/null 2>&1; then
    echo -e "${RED}❌ 端口8080仍被占用${NC}"
else
    echo -e "${GREEN}✅ 端口8080已释放${NC}"
fi

if netstat -tlnp | grep ":8081 " >/dev/null 2>&1; then
    echo -e "${RED}❌ 端口8081仍被占用${NC}"
else
    echo -e "${GREEN}✅ 端口8081已释放${NC}"
fi

echo -e "\n${GREEN}🎉 服务停止完成！${NC}"
