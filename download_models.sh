#!/bin/bash

echo "🤖 AI模型下载和配置脚本"
echo "================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 创建模型目录
echo -e "${YELLOW}创建模型目录...${NC}"
mkdir -p downloads/facenet
mkdir -p data-video-recog-master/downloads/facenet

# 进入模型目录
cd downloads/facenet

echo -e "${BLUE}当前目录: $(pwd)${NC}"

# 下载FaceNet模型文件
echo -e "${YELLOW}下载FaceNet模型文件...${NC}"

# 方案1: 使用OpenVINO官方模型
echo "尝试下载OpenVINO官方人脸识别模型..."

# 下载face-detection模型
wget -c "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-detection-adas-0001/FP32/face-detection-adas-0001.xml" -O face-detection.xml
wget -c "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-detection-adas-0001/FP32/face-detection-adas-0001.bin" -O face-detection.bin

# 下载face-recognition模型
wget -c "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-recognition-resnet100-arcface-onnx/FP32/face-recognition-resnet100-arcface-onnx.xml" -O facenet.xml
wget -c "https://storage.openvinotoolkit.org/repositories/open_model_zoo/2022.1/models_bin/3/face-recognition-resnet100-arcface-onnx/FP32/face-recognition-resnet100-arcface-onnx.bin" -O facenet.bin

# 检查下载结果
echo -e "\n${YELLOW}检查下载结果...${NC}"
for file in face-detection.xml face-detection.bin facenet.xml facenet.bin; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ $file 下载成功 ($(du -h $file | cut -f1))${NC}"
    else
        echo -e "${RED}✗ $file 下载失败${NC}"
    fi
done

# 复制到项目目录
echo -e "\n${YELLOW}复制模型到项目目录...${NC}"
cp -v *.xml *.bin ../../data-video-recog-master/downloads/facenet/ 2>/dev/null || echo "项目目录复制失败，请手动复制"

# 返回根目录
cd ../../

# 创建模型验证脚本
echo -e "\n${YELLOW}创建模型验证脚本...${NC}"
cat > verify_models.py << 'EOF'
#!/usr/bin/env python3
"""
模型验证脚本
"""
import os
import sys

def check_model_files():
    """检查模型文件"""
    model_paths = [
        "./downloads/facenet/facenet.xml",
        "./downloads/facenet/facenet.bin",
        "./data-video-recog-master/downloads/facenet/facenet.xml",
        "./data-video-recog-master/downloads/facenet/facenet.bin",
    ]
    
    print("🔍 检查模型文件...")
    for path in model_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✓ {path} ({size:,} bytes)")
        else:
            print(f"✗ {path} (不存在)")

def test_openvino():
    """测试OpenVINO"""
    try:
        import openvino as ov
        print(f"✓ OpenVINO版本: {ov.__version__}")
        
        # 测试加载模型
        core = ov.Core()
        model_path = "./downloads/facenet/facenet.xml"
        if os.path.exists(model_path):
            try:
                model = core.read_model(model_path)
                print(f"✓ 模型加载成功: {model_path}")
                print(f"  输入: {[input.get_any_name() for input in model.inputs]}")
                print(f"  输出: {[output.get_any_name() for output in model.outputs]}")
            except Exception as e:
                print(f"✗ 模型加载失败: {e}")
        else:
            print(f"✗ 模型文件不存在: {model_path}")
            
    except ImportError:
        print("✗ OpenVINO未安装，请运行: pip install openvino")

if __name__ == "__main__":
    check_model_files()
    print()
    test_openvino()
EOF

# 运行验证
echo -e "\n${YELLOW}运行模型验证...${NC}"
python verify_models.py

echo -e "\n${GREEN}模型下载和配置完成！${NC}"
echo -e "${BLUE}下一步: 重启服务并检查日志${NC}"
echo "  ./start_services_conda.sh"
echo "  tail -f logs/video_conda.log"
